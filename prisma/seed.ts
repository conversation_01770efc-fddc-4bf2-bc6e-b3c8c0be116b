import { PrismaClient, UserRole, PetStatus, PetSize, PetGender, ApplicationStatus } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Create default organization
  let organization = await prisma.organization.findFirst({
    where: { email: '<EMAIL>' }
  })

  if (!organization) {
    organization = await prisma.organization.create({
      data: {
        name: 'PetAdopt Rescue Center',
        email: '<EMAIL>',
        phone: '(555) 123-PETS',
        address: '123 Pet Adoption Lane',
        city: 'Compassion City',
        state: 'CC',
        zipCode: '12345',
        website: 'https://petadopt.com',
        description: 'A loving rescue center dedicated to finding homes for pets in need.',
        isActive: true,
      },
    })
  }

  console.log('✅ Created organization:', organization.name)

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin User',
      password: hashedPassword,
      role: UserRole.ADMIN,
      organizationId: organization.id,
      emailVerified: new Date(),
    },
  })

  console.log('✅ Created admin user:', adminUser.email)

  // Create staff user
  const staffUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Staff Member',
      password: await bcrypt.hash('staff123', 12),
      role: UserRole.STAFF,
      organizationId: organization.id,
      emailVerified: new Date(),
    },
  })

  console.log('✅ Created staff user:', staffUser.email)

  // Create sample pets
  const pets = [
    {
      name: 'Buddy',
      species: 'Dog',
      breed: 'Golden Retriever',
      age: 36,
      size: PetSize.LARGE,
      gender: PetGender.MALE,
      color: 'Golden',
      description: 'Buddy is a friendly and energetic Golden Retriever who loves playing fetch and swimming. He\'s great with kids and other dogs.',
      status: PetStatus.AVAILABLE,
      goodWithKids: true,
      goodWithDogs: true,
      goodWithCats: false,
      adoptionFee: 250,
      arrivalDate: new Date('2024-01-10'),
      organizationId: organization.id,
    },
    {
      name: 'Luna',
      species: 'Cat',
      breed: 'Domestic Shorthair',
      age: 24,
      size: PetSize.MEDIUM,
      gender: PetGender.FEMALE,
      color: 'Black and White',
      description: 'Luna is a sweet and gentle cat who loves to cuddle. She\'s perfect for a quiet home and gets along well with other cats.',
      status: PetStatus.AVAILABLE,
      goodWithKids: true,
      goodWithDogs: false,
      goodWithCats: true,
      adoptionFee: 150,
      arrivalDate: new Date('2024-01-15'),
      organizationId: organization.id,
    },
    {
      name: 'Max',
      species: 'Dog',
      breed: 'German Shepherd Mix',
      age: 48,
      size: PetSize.LARGE,
      gender: PetGender.MALE,
      color: 'Brown and Black',
      description: 'Max is a loyal and intelligent dog who would make an excellent companion. He knows basic commands and is house-trained.',
      status: PetStatus.AVAILABLE,
      goodWithKids: true,
      goodWithDogs: true,
      goodWithCats: false,
      adoptionFee: 200,
      arrivalDate: new Date('2024-01-08'),
      organizationId: organization.id,
    },
    {
      name: 'Whiskers',
      species: 'Cat',
      breed: 'Maine Coon',
      age: 18,
      size: PetSize.LARGE,
      gender: PetGender.MALE,
      color: 'Orange Tabby',
      description: 'Whiskers is a majestic Maine Coon with a gentle personality. He loves to be brushed and enjoys watching birds from the window.',
      status: PetStatus.AVAILABLE,
      goodWithKids: true,
      goodWithDogs: true,
      goodWithCats: true,
      adoptionFee: 175,
      arrivalDate: new Date('2024-01-12'),
      organizationId: organization.id,
    },
    {
      name: 'Bella',
      species: 'Dog',
      breed: 'Labrador Mix',
      age: 12,
      size: PetSize.MEDIUM,
      gender: PetGender.FEMALE,
      color: 'Chocolate',
      description: 'Bella is a young and playful pup who loves toys and treats. She\'s still learning basic commands but is very eager to please.',
      status: PetStatus.AVAILABLE,
      goodWithKids: true,
      goodWithDogs: true,
      goodWithCats: true,
      adoptionFee: 225,
      arrivalDate: new Date('2024-01-18'),
      organizationId: organization.id,
    },
  ]

  const createdPets = []
  for (const petData of pets) {
    const existingPet = await prisma.pet.findFirst({
      where: {
        name: petData.name,
        organizationId: petData.organizationId
      }
    })

    if (!existingPet) {
      const pet = await prisma.pet.create({
        data: petData,
      })
      createdPets.push(pet)
      console.log('✅ Created pet:', pet.name)
    } else {
      createdPets.push(existingPet)
      console.log('⚠️  Pet already exists:', petData.name)
    }
  }

  // Create sample volunteer user
  const volunteerUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'John Volunteer',
      password: await bcrypt.hash('volunteer123', 12),
      role: UserRole.VOLUNTEER,
      emailVerified: new Date(),
    },
  })

  // Create volunteer profile
  await prisma.volunteerProfile.upsert({
    where: { userId: volunteerUser.id },
    update: {},
    create: {
      userId: volunteerUser.id,
      preferredActivities: ['Dog Walking', 'Cat Care', 'Administrative'],
      maxHoursPerWeek: 10,
      volunteerExperience: 'I have been volunteering at animal shelters for 2 years.',
      animalExperience: 'I have owned dogs and cats my whole life.',
      emergencyContactName: 'Jane Volunteer',
      emergencyContactPhone: '(*************',
      status: 'ACTIVE',
    },
  })

  console.log('✅ Created volunteer user and profile')

  // Create sample adopter users
  const adopterUser1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Sarah Adopter',
      password: await bcrypt.hash('adopter123', 12),
      role: UserRole.PUBLIC,
      emailVerified: new Date(),
    },
  })

  const adopterUser2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'John Smith',
      password: await bcrypt.hash('adopter123', 12),
      role: UserRole.PUBLIC,
      emailVerified: new Date(),
    },
  })

  console.log('✅ Created adopter users')

  // Create sample applications
  if (createdPets.length > 0) {
    const applications = [
      {
        userId: adopterUser1.id,
        petId: createdPets[0].id, // Buddy
        status: ApplicationStatus.UNDER_REVIEW,
        applicantName: 'Sarah Adopter',
        applicantEmail: '<EMAIL>',
        applicantPhone: '(*************',
        housingType: 'House',
        ownOrRent: 'Own',
        hasYard: true,
        yardFenced: true,
        yardSize: 'Large',
        householdSize: 3,
        hasChildren: true,
        childrenAges: '8, 12',
        hasOtherPets: false,
        petExperience: 'I have had dogs my entire life and currently volunteer at the local animal shelter.',
        workSchedule: 'Monday-Friday 9-5, work from home 2 days per week',
        exercisePlans: 'Daily walks in the morning and evening, weekend hikes, and regular trips to the dog park.',
        trainingPlans: 'Enroll in obedience classes and continue positive reinforcement training.',
        agreesToTerms: true,
        agreesToHomeVisit: true,
        submittedAt: new Date('2024-01-20T10:30:00Z'),
      },
      {
        userId: adopterUser2.id,
        petId: createdPets[1].id, // Luna
        status: ApplicationStatus.APPROVED,
        applicantName: 'John Smith',
        applicantEmail: '<EMAIL>',
        applicantPhone: '(*************',
        housingType: 'Apartment',
        ownOrRent: 'Rent',
        hasYard: false,
        householdSize: 2,
        hasChildren: false,
        hasOtherPets: true,
        otherPetsDetails: 'One 5-year-old cat named Mittens',
        petExperience: 'I have been a cat owner for over 10 years and have experience with multi-cat households.',
        workSchedule: 'Monday-Friday 8-4',
        agreesToTerms: true,
        agreesToHomeVisit: true,
        submittedAt: new Date('2024-01-18T14:15:00Z'),
        reviewedAt: new Date('2024-01-19T09:00:00Z'),
        approvedAt: new Date('2024-01-19T16:30:00Z'),
        staffNotes: 'Excellent references from previous vet. Home visit completed successfully. Approved for adoption.',
      },
      {
        userId: adopterUser1.id,
        petId: createdPets[2].id, // Max
        status: ApplicationStatus.REFERENCE_CHECK,
        applicantName: 'Sarah Adopter',
        applicantEmail: '<EMAIL>',
        applicantPhone: '(*************',
        housingType: 'House',
        ownOrRent: 'Own',
        hasYard: true,
        yardFenced: true,
        householdSize: 3,
        hasChildren: true,
        childrenAges: '8, 12',
        hasOtherPets: false,
        petExperience: 'Previous dog owner, familiar with large breed dogs.',
        workSchedule: 'Monday-Friday 9-5',
        exercisePlans: 'Daily walks and weekend activities.',
        agreesToTerms: true,
        agreesToHomeVisit: true,
        submittedAt: new Date('2024-01-22T11:45:00Z'),
        reviewedAt: new Date('2024-01-23T10:00:00Z'),
        staffNotes: 'Application looks good. Currently checking references.',
      }
    ]

    for (const appData of applications) {
      const existingApp = await prisma.application.findFirst({
        where: {
          userId: appData.userId,
          petId: appData.petId
        }
      })

      if (!existingApp) {
        const application = await prisma.application.create({
          data: appData,
        })
        console.log('✅ Created application:', application.id)
      } else {
        console.log('⚠️  Application already exists for user/pet combination')
      }
    }
  }

  console.log('🎉 Database seed completed successfully!')
  console.log('')
  console.log('Test accounts created:')
  console.log('👑 Admin: <EMAIL> / admin123')
  console.log('👨‍💼 Staff: <EMAIL> / staff123')
  console.log('🙋‍♂️ Volunteer: <EMAIL> / volunteer123')
  console.log('👤 Adopter: <EMAIL> / adopter123')
  console.log('👤 Adopter: <EMAIL> / adopter123')
  console.log('')
  console.log('Sample pets created: Buddy, Luna, Max, Whiskers, Bella')
  console.log('Sample applications created for testing')
}

main()
  .catch((e) => {
    console.error('❌ Error during seed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
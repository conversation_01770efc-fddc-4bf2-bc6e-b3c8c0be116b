// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  PUBLIC
  ADOPTER
  STAFF
  VOLUNTEER
  ADMIN
  PARTNER_ORG
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING_VERIFICATION
}

enum PetStatus {
  AVAILABLE
  PENDING
  ADOPTED
  MEDICAL_HOLD
  FOSTER_CARE
  QUARANTINE
  UNAVAILABLE
}

enum PetSize {
  EXTRA_SMALL
  SMALL
  MEDIUM
  LARGE
  EXTRA_LARGE
}

enum PetGender {
  MALE
  FEMALE
  UNKNOWN
}

enum ApplicationStatus {
  DRAFT
  SUBMITTED
  UNDER_REVIEW
  REFERENCE_CHECK
  HOME_VISIT_SCHEDULED
  HOME_VISIT_COMPLETED
  APPROVED
  REJECTED
  WITHDRAWN
  WAITLISTED
}

enum AdoptionStatus {
  PENDING
  COMPLETED
  CANCELLED
  RETURNED
}

enum FosterStatus {
  ACTIVE
  INACTIVE
  EMERGENCY_ONLY
  FULL_CAPACITY
}

enum VolunteerStatus {
  ACTIVE
  INACTIVE
  TRAINING
  ON_HOLD
}

enum AppointmentType {
  MEET_GREET
  HOME_VISIT
  ADOPTION_PICKUP
  VETERINARY
  TRAINING
  VOLUNTEER_ORIENTATION
}

enum AppointmentStatus {
  SCHEDULED
  CONFIRMED
  COMPLETED
  CANCELLED
  NO_SHOW
  RESCHEDULED
}

enum DonationType {
  ONE_TIME
  RECURRING
  MEMORIAL
  SPONSORSHIP
  CORPORATE
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
  CANCELLED
}

// Core Models
model User {
  id                String     @id @default(cuid())
  email             String     @unique
  emailVerified     DateTime?
  password          String?
  name              String?
  image             String?
  phone             String?
  role              UserRole   @default(PUBLIC)
  status            UserStatus @default(PENDING_VERIFICATION)
  
  // Profile Information
  firstName         String?
  lastName          String?
  dateOfBirth       DateTime?
  
  // Address Information
  address           String?
  city              String?
  state             String?
  zipCode           String?
  country           String?   @default("US")
  
  // Emergency Contact
  emergencyContactName  String?
  emergencyContactPhone String?
  emergencyContactRelation String?
  
  // Preferences
  preferences       Json?
  notifications     Json?
  
  // Verification
  backgroundCheckCompleted Boolean @default(false)
  backgroundCheckDate      DateTime?
  idVerified              Boolean @default(false)
  idVerificationDate      DateTime?

  // Password Reset
  resetToken              String?
  resetTokenExpiry        DateTime?
  
  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  lastLoginAt       DateTime?
  
  // Relations
  accounts          Account[]
  sessions          Session[]
  applications      Application[]
  adoptions         Adoption[]
  fosterProfile     FosterProfile?
  volunteerProfile  VolunteerProfile?
  appointments      Appointment[]
  donations         Donation[]
  documents         Document[]
  messages          Message[]
  reviews           Review[]
  favorites         Favorite[]
  organization      Organization? @relation(fields: [organizationId], references: [id])
  organizationId    String?
  
  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

model Organization {
  id              String   @id @default(cuid())
  name            String
  description     String?
  website         String?
  email           String?
  phone           String?
  
  // Address
  address         String?
  city            String?
  state           String?
  zipCode         String?
  country         String? @default("US")
  
  // Settings
  settings        Json?
  isActive        Boolean @default(true)
  
  // Timestamps
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // Relations
  pets            Pet[]
  staff           User[]
  facilities      Facility[]
  
  @@map("organizations")
}

model Pet {
  id                String    @id @default(cuid())
  name              String
  species           String    // Dog, Cat, Rabbit, etc.
  breed             String?
  mixedBreed        Boolean   @default(false)
  secondaryBreed    String?
  
  // Basic Info
  age               Int?      // in months
  ageEstimated      Boolean   @default(false)
  size              PetSize?
  weight            Float?    // in pounds
  gender            PetGender
  color             String?
  markings          String?
  
  // Status
  status            PetStatus @default(AVAILABLE)
  isSpayedNeutered  Boolean   @default(false)
  isMicrochipped    Boolean   @default(false)
  microchipNumber   String?
  
  // Dates
  arrivalDate       DateTime  @default(now())
  availableDate     DateTime?
  adoptionDate      DateTime?
  
  // Location
  organizationId    String
  facilityId        String?
  kennelNumber      String?
  
  // Description
  description       String?
  personalityTraits String[]
  specialNeeds      String?
  
  // Compatibility
  goodWithKids      Boolean?
  goodWithDogs      Boolean?
  goodWithCats      Boolean?
  goodWithOtherPets Boolean?
  
  // Activity Level (1-5 scale)
  activityLevel     Int?
  trainingLevel     Int?
  
  // Medical
  isVaccinated      Boolean   @default(false)
  lastVetVisit      DateTime?
  medications       String?
  allergies         String?
  
  // Adoption Info
  adoptionFee       Float?
  isSponsored       Boolean   @default(false)
  sponsorshipAmount Float?
  
  // SEO and Search
  slug              String?   @unique
  searchKeywords    String[]
  
  // Timestamps
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  
  // Relations
  organization      Organization @relation(fields: [organizationId], references: [id])
  facility          Facility?    @relation(fields: [facilityId], references: [id])
  photos            PetPhoto[]
  videos            PetVideo[]
  medicalRecords    MedicalRecord[]
  behavioralAssessments BehavioralAssessment[]
  applications      Application[]
  adoptions         Adoption[]
  fosterPlacements  FosterPlacement[]
  favorites         Favorite[]
  sponsorships      Sponsorship[]
  
  @@map("pets")
}

model PetPhoto {
  id          String   @id @default(cuid())
  petId       String
  url         String
  filename    String
  caption     String?
  isPrimary   Boolean  @default(false)
  order       Int      @default(0)
  createdAt   DateTime @default(now())

  pet         Pet      @relation(fields: [petId], references: [id], onDelete: Cascade)

  @@map("pet_photos")
}

model PetVideo {
  id          String   @id @default(cuid())
  petId       String
  url         String
  filename    String
  caption     String?
  thumbnail   String?
  duration    Int?     // in seconds
  createdAt   DateTime @default(now())

  pet         Pet      @relation(fields: [petId], references: [id], onDelete: Cascade)

  @@map("pet_videos")
}

model MedicalRecord {
  id              String   @id @default(cuid())
  petId           String
  recordType      String   // vaccination, checkup, surgery, etc.
  date            DateTime
  veterinarian    String?
  clinic          String?
  description     String
  medications     String?
  nextDueDate     DateTime?
  cost            Float?
  documents       String[] // URLs to medical documents

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  pet             Pet      @relation(fields: [petId], references: [id], onDelete: Cascade)

  @@map("medical_records")
}

model BehavioralAssessment {
  id                    String   @id @default(cuid())
  petId                 String
  assessorName          String
  assessmentDate        DateTime

  // Temperament Scores (1-5 scale)
  friendliness          Int?
  energyLevel           Int?
  trainability          Int?
  independence          Int?
  protectiveness        Int?
  playfulness           Int?

  // Behavioral Notes
  behaviorWithStrangers String?
  behaviorWithChildren  String?
  behaviorWithOtherPets String?
  behaviorDuringHandling String?

  // Training Status
  housebroken           Boolean?
  crateTrained          Boolean?
  leashTrained          Boolean?
  basicCommands         String[]

  // Special Notes
  fears                 String?
  triggers              String?
  recommendations       String?

  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  pet                   Pet      @relation(fields: [petId], references: [id], onDelete: Cascade)

  @@map("behavioral_assessments")
}

model Application {
  id                    String            @id @default(cuid())
  userId                String
  petId                 String
  status                ApplicationStatus @default(DRAFT)

  // Personal Information
  applicantName         String?
  applicantEmail        String?
  applicantPhone        String?

  // Housing Information
  housingType           String?           // house, apartment, condo, etc.
  ownOrRent             String?
  landlordContact       String?
  hasYard               Boolean?
  yardFenced            Boolean?
  yardSize              String?

  // Household Information
  householdSize         Int?
  hasChildren           Boolean?
  childrenAges          String?
  hasOtherPets          Boolean?
  otherPetsDetails      String?

  // Experience
  petExperience         String?
  previousPets          String?
  currentVeterinarian   String?
  vetContact            String?

  // Lifestyle
  workSchedule          String?
  travelFrequency       String?
  exercisePlans         String?
  trainingPlans         String?

  // References
  personalReferences    Json?             // Array of reference objects
  veterinaryReference   Json?

  // Agreement
  agreesToTerms         Boolean           @default(false)
  agreesToHomeVisit     Boolean           @default(false)

  // Internal Notes
  staffNotes            String?
  rejectionReason       String?

  // Timestamps
  submittedAt           DateTime?
  reviewedAt            DateTime?
  approvedAt            DateTime?
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt

  // Relations
  user                  User              @relation(fields: [userId], references: [id])
  pet                   Pet               @relation(fields: [petId], references: [id])
  appointments          Appointment[]
  adoption              Adoption?

  @@map("applications")
}

model Adoption {
  id                String         @id @default(cuid())
  applicationId     String         @unique
  userId            String
  petId             String
  status            AdoptionStatus @default(PENDING)

  // Adoption Details
  adoptionDate      DateTime
  adoptionFee       Float?
  feeWaived         Boolean        @default(false)
  waiverReason      String?

  // Contract Information
  contractSigned    Boolean        @default(false)
  contractDate      DateTime?
  contractUrl       String?

  // Follow-up
  followUpDate      DateTime?
  followUpCompleted Boolean        @default(false)
  followUpNotes     String?

  // Return Information
  returnDate        DateTime?
  returnReason      String?

  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt

  // Relations
  application       Application    @relation(fields: [applicationId], references: [id])
  user              User           @relation(fields: [userId], references: [id])
  pet               Pet            @relation(fields: [petId], references: [id])
  payments          Payment[]

  @@map("adoptions")
}

model FosterProfile {
  id                    String       @id @default(cuid())
  userId                String       @unique
  status                FosterStatus @default(INACTIVE)

  // Capacity Information
  maxPets               Int          @default(1)
  currentPets           Int          @default(0)
  preferredSpecies      String[]
  preferredAges         String[]     // puppy, adult, senior
  preferredSizes        PetSize[]

  // Experience
  fosterExperience      String?
  specialNeedsExperience String?

  // Availability
  emergencyFoster       Boolean      @default(false)
  longTermFoster        Boolean      @default(true)
  shortTermFoster       Boolean      @default(true)

  // Home Information
  hasYard               Boolean?
  yardFenced            Boolean?
  hasOtherPets          Boolean?
  otherPetsDetails      String?
  hasChildren           Boolean?
  childrenAges          String?

  // Approval Information
  approved              Boolean      @default(false)
  approvedDate          DateTime?
  approvedBy            String?
  backgroundCheckDate   DateTime?
  homeVisitDate         DateTime?

  // Training
  trainingCompleted     Boolean      @default(false)
  trainingDate          DateTime?
  certificationExpiry   DateTime?

  createdAt             DateTime     @default(now())
  updatedAt             DateTime     @updatedAt

  // Relations
  user                  User         @relation(fields: [userId], references: [id])
  placements            FosterPlacement[]

  @@map("foster_profiles")
}

model FosterPlacement {
  id                String     @id @default(cuid())
  fosterProfileId   String
  petId             String

  // Placement Details
  startDate         DateTime
  endDate           DateTime?
  reason            String?    // medical, behavioral, temporary, etc.

  // Status
  isActive          Boolean    @default(true)

  // Notes
  placementNotes    String?
  endReason         String?

  createdAt         DateTime   @default(now())
  updatedAt         DateTime   @updatedAt

  // Relations
  fosterProfile     FosterProfile @relation(fields: [fosterProfileId], references: [id])
  pet               Pet           @relation(fields: [petId], references: [id])

  @@map("foster_placements")
}

model VolunteerProfile {
  id                    String          @id @default(cuid())
  userId                String          @unique
  status                VolunteerStatus @default(TRAINING)

  // Skills and Interests
  skills                String[]
  interests             String[]
  availability          Json?           // Schedule availability

  // Experience
  volunteerExperience   String?
  animalExperience      String?
  specialSkills         String?

  // Preferences
  preferredActivities   String[]        // dog walking, cat socialization, transport, etc.
  maxHoursPerWeek       Int?

  // Training and Certification
  orientationCompleted  Boolean         @default(false)
  orientationDate       DateTime?
  trainingModules       Json?           // Completed training modules
  certifications        String[]

  // Background Check
  backgroundCheckDate   DateTime?
  backgroundCheckStatus String?

  // Emergency Contact
  emergencyContactName  String?
  emergencyContactPhone String?

  createdAt             DateTime        @default(now())
  updatedAt             DateTime        @updatedAt

  // Relations
  user                  User            @relation(fields: [userId], references: [id])
  volunteerHours        VolunteerHours[]
  assignments           VolunteerAssignment[]

  @@map("volunteer_profiles")
}

model VolunteerHours {
  id                String   @id @default(cuid())
  volunteerProfileId String

  // Activity Details
  date              DateTime
  hours             Float
  activity          String
  description       String?

  // Verification
  verified          Boolean  @default(false)
  verifiedBy        String?
  verifiedDate      DateTime?

  createdAt         DateTime @default(now())

  // Relations
  volunteerProfile  VolunteerProfile @relation(fields: [volunteerProfileId], references: [id])

  @@map("volunteer_hours")
}

model VolunteerAssignment {
  id                String   @id @default(cuid())
  volunteerProfileId String

  // Assignment Details
  title             String
  description       String
  startDate         DateTime
  endDate           DateTime?

  // Status
  isActive          Boolean  @default(true)
  completed         Boolean  @default(false)

  // Notes
  notes             String?

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  volunteerProfile  VolunteerProfile @relation(fields: [volunteerProfileId], references: [id])

  @@map("volunteer_assignments")
}

model Appointment {
  id                String            @id @default(cuid())
  userId            String
  petId             String?
  applicationId     String?

  // Appointment Details
  type              AppointmentType
  status            AppointmentStatus @default(SCHEDULED)
  title             String
  description       String?

  // Scheduling
  scheduledDate     DateTime
  scheduledTime     String
  duration          Int               @default(60) // in minutes

  // Location
  location          String?
  address           String?
  isVirtual         Boolean           @default(false)
  meetingLink       String?

  // Staff Assignment
  assignedStaff     String?

  // Notes
  notes             String?
  completionNotes   String?

  // Reminders
  reminderSent      Boolean           @default(false)
  reminderSentAt    DateTime?

  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  user              User              @relation(fields: [userId], references: [id])
  application       Application?      @relation(fields: [applicationId], references: [id])

  @@map("appointments")
}

model Donation {
  id                String        @id @default(cuid())
  userId            String?

  // Donation Details
  type              DonationType  @default(ONE_TIME)
  amount            Float
  currency          String        @default("USD")

  // Payment Information
  paymentStatus     PaymentStatus @default(PENDING)
  paymentMethod     String?
  stripePaymentId   String?

  // Donor Information (for anonymous donations)
  donorName         String?
  donorEmail        String?
  isAnonymous       Boolean       @default(false)

  // Designation
  designation       String?       // general, medical, transport, etc.
  petId             String?       // if donation is for specific pet

  // Memorial/Tribute
  isMemorial        Boolean       @default(false)
  memorialName      String?
  memorialMessage   String?
  notifyFamily      Boolean       @default(false)
  familyEmail       String?

  // Recurring Donations
  isRecurring       Boolean       @default(false)
  recurringInterval String?       // monthly, quarterly, annually
  nextDonationDate  DateTime?

  // Tax Information
  taxDeductible     Boolean       @default(true)
  receiptSent       Boolean       @default(false)
  receiptSentAt     DateTime?

  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relations
  user              User?         @relation(fields: [userId], references: [id])

  @@map("donations")
}

model Payment {
  id                String        @id @default(cuid())
  adoptionId        String?
  donationId        String?

  // Payment Details
  amount            Float
  currency          String        @default("USD")
  status            PaymentStatus @default(PENDING)

  // Payment Method
  paymentMethod     String        // stripe, cash, check, etc.
  stripePaymentId   String?
  stripeChargeId    String?

  // Transaction Details
  transactionDate   DateTime      @default(now())
  description       String?

  // Refund Information
  refundAmount      Float?
  refundDate        DateTime?
  refundReason      String?

  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relations
  adoption          Adoption?     @relation(fields: [adoptionId], references: [id])

  @@map("payments")
}

model Facility {
  id                String   @id @default(cuid())
  organizationId    String

  // Basic Information
  name              String
  type              String   // shelter, clinic, foster home, etc.
  description       String?

  // Address
  address           String
  city              String
  state             String
  zipCode           String
  country           String   @default("US")

  // Contact Information
  phone             String?
  email             String?

  // Capacity
  totalCapacity     Int?
  currentOccupancy  Int      @default(0)

  // Facilities
  hasQuarantine     Boolean  @default(false)
  hasMedicalFacility Boolean @default(false)
  hasPlayAreas      Boolean  @default(false)
  hasGrooming       Boolean  @default(false)

  // Operating Hours
  operatingHours    Json?

  // Status
  isActive          Boolean  @default(true)

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  organization      Organization @relation(fields: [organizationId], references: [id])
  pets              Pet[]

  @@map("facilities")
}

model Document {
  id                String   @id @default(cuid())
  userId            String

  // Document Details
  name              String
  type              String   // id, contract, medical, reference, etc.
  url               String
  filename          String
  mimeType          String
  size              Int      // in bytes

  // Metadata
  description       String?
  isVerified        Boolean  @default(false)
  verifiedBy        String?
  verifiedAt        DateTime?

  // Privacy
  isPublic          Boolean  @default(false)

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  user              User     @relation(fields: [userId], references: [id])

  @@map("documents")
}

model Message {
  id                String   @id @default(cuid())
  senderId          String
  recipientId       String?

  // Message Details
  subject           String?
  content           String
  messageType       String   @default("general") // general, application, adoption, etc.

  // Status
  isRead            Boolean  @default(false)
  readAt            DateTime?

  // Threading
  parentMessageId   String?
  threadId          String?

  // Attachments
  attachments       String[] // URLs to attachments

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  sender            User     @relation(fields: [senderId], references: [id])

  @@map("messages")
}

model Review {
  id                String   @id @default(cuid())
  userId            String
  petId             String?

  // Review Details
  rating            Int      // 1-5 stars
  title             String?
  content           String

  // Review Type
  reviewType        String   // adoption, foster, volunteer, etc.

  // Status
  isApproved        Boolean  @default(false)
  approvedBy        String?
  approvedAt        DateTime?

  // Helpful Votes
  helpfulVotes      Int      @default(0)

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  user              User     @relation(fields: [userId], references: [id])

  @@map("reviews")
}

model Favorite {
  id                String   @id @default(cuid())
  userId            String
  petId             String

  createdAt         DateTime @default(now())

  // Relations
  user              User     @relation(fields: [userId], references: [id])
  pet               Pet      @relation(fields: [petId], references: [id])

  @@unique([userId, petId])
  @@map("favorites")
}

model Sponsorship {
  id                String   @id @default(cuid())
  petId             String
  sponsorName       String
  sponsorEmail      String?

  // Sponsorship Details
  amount            Float
  duration          Int?     // in months, null for indefinite
  startDate         DateTime @default(now())
  endDate           DateTime?

  // Status
  isActive          Boolean  @default(true)

  // Recognition
  isAnonymous       Boolean  @default(false)
  displayOnProfile  Boolean  @default(true)

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  pet               Pet      @relation(fields: [petId], references: [id])

  @@map("sponsorships")
}

model Event {
  id                String   @id @default(cuid())

  // Event Details
  title             String
  description       String
  eventType         String   // adoption_event, fundraiser, training, etc.

  // Scheduling
  startDate         DateTime
  endDate           DateTime
  isAllDay          Boolean  @default(false)

  // Location
  location          String?
  address           String?
  isVirtual         Boolean  @default(false)
  meetingLink       String?

  // Registration
  requiresRegistration Boolean @default(false)
  maxAttendees      Int?
  currentAttendees  Int      @default(0)
  registrationFee   Float?

  // Status
  isPublic          Boolean  @default(true)
  isActive          Boolean  @default(true)
  isCancelled       Boolean  @default(false)

  // Media
  imageUrl          String?

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("events")
}

model BlogPost {
  id                String   @id @default(cuid())

  // Content
  title             String
  slug              String   @unique
  content           String
  excerpt           String?

  // Media
  featuredImage     String?

  // SEO
  metaTitle         String?
  metaDescription   String?

  // Publishing
  isPublished       Boolean  @default(false)
  publishedAt       DateTime?

  // Categories and Tags
  category          String?
  tags              String[]

  // Author
  authorName        String
  authorEmail       String?

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("blog_posts")
}

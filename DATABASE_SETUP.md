# 🗄️ Database Setup Guide

This guide will help you set up the PostgreSQL database for the PetAdopt application.

## 🚀 Quick Setup (Recommended)

### Option 1: Automated Setup Script

Run our automated setup script that handles everything for you:

```bash
npm run setup
```

This script will:
- ✅ Check if PostgreSQL is installed and running
- ✅ Create the database and user
- ✅ Set up proper permissions
- ✅ Create `.env.local` with development settings
- ✅ Install dependencies
- ✅ Generate Prisma client
- ✅ Push database schema
- ✅ Optionally seed with sample data

### Option 2: Manual Setup

If you prefer to set up manually or the script doesn't work:

## 📋 Prerequisites

1. **PostgreSQL 14+** installed and running
   - **macOS**: `brew install postgresql && brew services start postgresql`
   - **Ubuntu**: `sudo apt-get install postgresql postgresql-contrib && sudo systemctl start postgresql`
   - **Windows**: Download from [postgresql.org](https://www.postgresql.org/download/windows/)

2. **Node.js 18+** installed
3. **npm** or **yarn** package manager

## 🔧 Manual Database Setup

### 1. Create Database and User

Connect to PostgreSQL as superuser:
```bash
psql -U postgres
```

Run the following commands:
```sql
-- Create database
CREATE DATABASE pet_adoption_db;

-- Create user
CREATE USER pet_adoption_user WITH PASSWORD 'your_secure_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE pet_adoption_db TO pet_adoption_user;

-- Connect to the database
\c pet_adoption_db;

-- Grant schema privileges
GRANT ALL ON SCHEMA public TO pet_adoption_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO pet_adoption_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO pet_adoption_user;

-- Set default privileges
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO pet_adoption_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO pet_adoption_user;

-- Exit
\q
```

### 2. Configure Environment Variables

Create a `.env.local` file in the project root:

```env
# Database
DATABASE_URL="postgresql://pet_adoption_user:your_secure_password@localhost:5432/pet_adoption_db"

# Authentication
NEXTAUTH_SECRET="your-super-secret-key-change-in-production"
NEXTAUTH_URL="http://localhost:3000"

# OAuth Providers (optional for development)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Stripe (optional for development)
STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."

# Email Service (optional for development)
EMAIL_API_KEY="your-sendgrid-api-key"
FROM_EMAIL="noreply@localhost"

# SMS Service (optional for development)
TWILIO_ACCOUNT_SID="your-twilio-sid"
TWILIO_AUTH_TOKEN="your-twilio-token"
TWILIO_FROM_NUMBER="+**********"

# File Storage (optional for development)
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"

# Google Maps (optional for development)
GOOGLE_MAPS_API_KEY="your-maps-api-key"
```

### 3. Install Dependencies and Setup Database

```bash
# Install dependencies
npm install

# Generate Prisma client
npm run db:generate

# Push database schema
npm run db:push

# Seed database with sample data (optional)
npm run db:seed
```

## 🌱 Sample Data

The seed script creates:

### Test Accounts
- **Admin**: `<EMAIL>` / `admin123`
- **Staff**: `<EMAIL>` / `staff123`
- **Volunteer**: `<EMAIL>` / `volunteer123`
- **Adopter**: `<EMAIL>` / `adopter123`

### Sample Pets
- **Buddy** - Golden Retriever (Dog)
- **Luna** - Domestic Shorthair (Cat)
- **Max** - German Shepherd Mix (Dog)
- **Whiskers** - Maine Coon (Cat)
- **Bella** - Labrador Mix (Dog)

## 🔍 Database Management

### Useful Commands

```bash
# View database in browser
npm run db:studio

# Reset database (careful!)
npx prisma db push --force-reset

# Generate Prisma client after schema changes
npm run db:generate

# Create and apply migrations (production)
npm run db:migrate

# Seed database
npm run db:seed
```

### Database Schema Overview

The database includes 20+ models:
- **Users & Authentication**: Users, Accounts, Sessions
- **Organizations**: Organizations, Staff management
- **Pets**: Pets, Photos, Medical records, Behavioral assessments
- **Applications**: Adoption applications, References
- **Volunteers**: Volunteer profiles, Activities, Hours
- **Foster Care**: Foster profiles, Placements
- **Events**: Events, Registrations
- **Financial**: Donations, Campaigns, Transactions
- **Communication**: Messages, Notifications
- **Operations**: Transport, Inventory, Contracts

## 🚨 Troubleshooting

### Common Issues

#### 1. "Database does not exist" Error
```bash
# Create the database manually
createdb pet_adoption_db
```

#### 2. "Permission denied" Error
```bash
# Grant permissions to user
psql -d pet_adoption_db -c "GRANT ALL PRIVILEGES ON DATABASE pet_adoption_db TO pet_adoption_user;"
```

#### 3. "Connection refused" Error
```bash
# Check if PostgreSQL is running
pg_isready

# Start PostgreSQL
# macOS: brew services start postgresql
# Ubuntu: sudo systemctl start postgresql
```

#### 4. "User does not exist" Error
```bash
# Create the user
psql -d postgres -c "CREATE USER pet_adoption_user WITH PASSWORD 'your_password';"
```

#### 5. Prisma Client Issues
```bash
# Regenerate Prisma client
npm run db:generate

# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

### Environment-Specific Setup

#### Development
- Use the automated setup script: `npm run setup`
- Default password: `dev_password_123`
- Database: `pet_adoption_db`

#### Production
- Use strong passwords
- Set up SSL connections
- Configure backup strategies
- Use environment-specific database names
- Set up monitoring and logging

## 🔐 Security Considerations

1. **Strong Passwords**: Use complex passwords for database users
2. **SSL Connections**: Enable SSL for production databases
3. **Network Security**: Restrict database access to application servers
4. **Regular Backups**: Set up automated database backups
5. **User Permissions**: Follow principle of least privilege
6. **Environment Variables**: Never commit sensitive data to version control

## 📊 Production Deployment

For production deployment, consider:

1. **Managed Database Services**:
   - **Vercel Postgres** (recommended for Vercel deployment)
   - **AWS RDS**
   - **Google Cloud SQL**
   - **DigitalOcean Managed Databases**

2. **Connection Pooling**:
   - Use connection pooling for better performance
   - Configure appropriate pool sizes

3. **Monitoring**:
   - Set up database monitoring
   - Configure alerts for performance issues
   - Monitor connection counts and query performance

## 🆘 Getting Help

If you encounter issues:

1. Check the [troubleshooting section](#-troubleshooting) above
2. Review PostgreSQL logs for error details
3. Ensure all environment variables are correctly set
4. Verify PostgreSQL is running and accessible
5. Check that the database user has proper permissions

For additional help, please open an issue in the repository with:
- Your operating system
- PostgreSQL version
- Error messages (with sensitive data removed)
- Steps you've already tried

---

**Happy coding! 🐾**

import { PrismaClient, UserRole, UserStatus } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function createAdminUsers() {
  try {
    console.log('Creating admin and staff users...')

    // Hash passwords
    const adminPassword = await bcrypt.hash('admin123', 12)
    const staffPassword = await bcrypt.hash('staff123', 12)

    // Create admin user
    const admin = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        role: UserRole.ADMIN,
        status: UserStatus.ACTIVE,
      },
      create: {
        email: '<EMAIL>',
        password: adminPassword,
        name: 'System Administrator',
        firstName: 'System',
        lastName: 'Administrator',
        phone: '(555) 123-ADMIN',
        role: UserRole.ADMIN,
        status: UserStatus.ACTIVE,
      },
    })

    console.log('✅ Created admin user:', admin.email)

    // Create staff user
    const staff = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        role: UserRole.STAFF,
        status: UserStatus.ACTIVE,
      },
      create: {
        email: '<EMAIL>',
        password: staffPassword,
        name: 'Staff Member',
        firstName: 'Staff',
        lastName: 'Member',
        phone: '(555) 123-STAFF',
        role: UserRole.STAFF,
        status: UserStatus.ACTIVE,
      },
    })

    console.log('✅ Created staff user:', staff.email)

    // Create volunteer user
    const volunteer = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        role: UserRole.VOLUNTEER,
        status: UserStatus.ACTIVE,
      },
      create: {
        email: '<EMAIL>',
        password: await bcrypt.hash('volunteer123', 12),
        name: 'Volunteer User',
        firstName: 'Volunteer',
        lastName: 'User',
        phone: '(555) 123-VOL',
        role: UserRole.VOLUNTEER,
        status: UserStatus.ACTIVE,
      },
    })

    console.log('✅ Created volunteer user:', volunteer.email)

    console.log('\n🎉 Admin users created successfully!')
    console.log('\nLogin credentials:')
    console.log('Admin: <EMAIL> / admin123')
    console.log('Staff: <EMAIL> / staff123')
    console.log('Volunteer: <EMAIL> / volunteer123')

  } catch (error) {
    console.error('Error creating admin users:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createAdminUsers()

#!/bin/bash

# Development setup script for PetAdopt
echo "🐾 Setting up PetAdopt development environment..."

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    echo "❌ PostgreSQL is not installed. Please install PostgreSQL first."
    echo "   macOS: brew install postgresql"
    echo "   Ubuntu: sudo apt-get install postgresql postgresql-contrib"
    exit 1
fi

# Check if PostgreSQL is running
if ! pg_isready &> /dev/null; then
    echo "❌ PostgreSQL is not running. Please start PostgreSQL first."
    echo "   macOS: brew services start postgresql"
    echo "   Ubuntu: sudo systemctl start postgresql"
    exit 1
fi

echo "✅ PostgreSQL is installed and running"

# Create database and user
echo "📊 Setting up database..."

# Check if database exists
if psql -lqt | cut -d \| -f 1 | grep -qw pet_adoption_db; then
    echo "⚠️  Database 'pet_adoption_db' already exists"
    read -p "Do you want to recreate it? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        dropdb pet_adoption_db 2>/dev/null || true
        dropuser pet_adoption_user 2>/dev/null || true
    else
        echo "Using existing database"
    fi
fi

# Create database and user
createdb pet_adoption_db 2>/dev/null || echo "Database already exists"
psql -d pet_adoption_db -c "CREATE USER pet_adoption_user WITH PASSWORD 'dev_password_123';" 2>/dev/null || echo "User already exists"
psql -d pet_adoption_db -c "GRANT ALL PRIVILEGES ON DATABASE pet_adoption_db TO pet_adoption_user;"
psql -d pet_adoption_db -c "GRANT ALL ON SCHEMA public TO pet_adoption_user;"
psql -d pet_adoption_db -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO pet_adoption_user;"
psql -d pet_adoption_db -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO pet_adoption_user;"

echo "✅ Database setup complete"

# Create .env.local if it doesn't exist
if [ ! -f .env.local ]; then
    echo "📝 Creating .env.local file..."
    cat > .env.local << EOF
# Database
DATABASE_URL="postgresql://pet_adoption_user:dev_password_123@localhost:5432/pet_adoption_db"

# Authentication
NEXTAUTH_SECRET="your-super-secret-development-key-change-in-production"
NEXTAUTH_URL="http://localhost:3000"

# OAuth Providers (optional for development)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Stripe (optional for development)
STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."

# Email Service (optional for development)
EMAIL_API_KEY="your-sendgrid-api-key"
FROM_EMAIL="noreply@localhost"

# SMS Service (optional for development)
TWILIO_ACCOUNT_SID="your-twilio-sid"
TWILIO_AUTH_TOKEN="your-twilio-token"
TWILIO_FROM_NUMBER="+**********"

# File Storage (optional for development)
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"

# Google Maps (optional for development)
GOOGLE_MAPS_API_KEY="your-maps-api-key"
EOF
    echo "✅ Created .env.local with development settings"
else
    echo "⚠️  .env.local already exists, skipping creation"
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Push database schema
echo "🗄️  Pushing database schema..."
npx prisma db push

# Seed database (optional)
echo "🌱 Would you like to seed the database with sample data?"
read -p "Seed database? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🌱 Seeding database..."
    npx prisma db seed 2>/dev/null || echo "No seed script found, skipping..."
fi

echo ""
echo "🎉 Development environment setup complete!"
echo ""
echo "Next steps:"
echo "1. Start the development server: npm run dev"
echo "2. Open http://localhost:3000 in your browser"
echo "3. Configure OAuth providers in .env.local (optional)"
echo ""
echo "Database connection string:"
echo "postgresql://pet_adoption_user:dev_password_123@localhost:5432/pet_adoption_db"
echo ""
echo "Happy coding! 🐾"

-- Database setup script for <PERSON><PERSON><PERSON><PERSON>
-- Run this script as a PostgreSQL superuser to set up the database and user

-- Create database
CREATE DATABASE pet_adoption_db;

-- Create user with password
CREATE USER pet_adoption_user WITH PASSWORD 'your_secure_password_here';

-- Grant privileges to the user
GRANT ALL PRIVILEGES ON DATABASE pet_adoption_db TO pet_adoption_user;

-- Connect to the database
\c pet_adoption_db;

-- <PERSON> schema privileges
GRANT ALL ON SCHEMA public TO pet_adoption_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO pet_adoption_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO pet_adoption_user;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO pet_adoption_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO pet_adoption_user;

-- Make the user the owner of the database
ALTER DATABASE pet_adoption_db OWNER TO pet_adoption_user;

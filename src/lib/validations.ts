import { z } from "zod"
import { UserRole, PetStatus, PetSize, PetGender, ApplicationStatus } from "@prisma/client"

// User validation schemas
export const signUpSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  name: z.string().min(2, "Name must be at least 2 characters"),
  phone: z.string().optional(),
  role: z.nativeEnum(UserRole).default(UserRole.PUBLIC),
})

export const signInSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
})

export const userProfileSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  dateOfBirth: z.date().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zipCode: z.string().optional(),
  emergencyContactName: z.string().optional(),
  emergencyContactPhone: z.string().optional(),
  emergencyContactRelation: z.string().optional(),
})

// Pet validation schemas
export const petSchema = z.object({
  name: z.string().min(1, "Pet name is required"),
  species: z.string().min(1, "Species is required"),
  breed: z.string().optional(),
  mixedBreed: z.boolean().default(false),
  secondaryBreed: z.string().optional(),
  age: z.number().int().min(0).optional(),
  ageEstimated: z.boolean().default(false),
  size: z.nativeEnum(PetSize).optional(),
  weight: z.number().positive().optional(),
  gender: z.nativeEnum(PetGender),
  color: z.string().optional(),
  markings: z.string().optional(),
  status: z.nativeEnum(PetStatus).default(PetStatus.AVAILABLE),
  isSpayedNeutered: z.boolean().default(false),
  isMicrochipped: z.boolean().default(false),
  microchipNumber: z.string().optional(),
  description: z.string().optional(),
  personalityTraits: z.array(z.string()).default([]),
  specialNeeds: z.string().optional(),
  goodWithKids: z.boolean().optional(),
  goodWithDogs: z.boolean().optional(),
  goodWithCats: z.boolean().optional(),
  goodWithOtherPets: z.boolean().optional(),
  activityLevel: z.number().int().min(1).max(5).optional(),
  trainingLevel: z.number().int().min(1).max(5).optional(),
  isVaccinated: z.boolean().default(false),
  medications: z.string().optional(),
  allergies: z.string().optional(),
  adoptionFee: z.number().positive().optional(),
  organizationId: z.string().min(1, "Organization is required"),
  facilityId: z.string().optional(),
  kennelNumber: z.string().optional(),
})

// Application validation schemas
export const applicationSchema = z.object({
  petId: z.string().min(1, "Pet selection is required"),
  applicantName: z.string().min(1, "Name is required"),
  applicantEmail: z.string().email("Invalid email address"),
  applicantPhone: z.string().min(1, "Phone number is required"),
  housingType: z.string().min(1, "Housing type is required"),
  ownOrRent: z.string().min(1, "Please specify if you own or rent"),
  landlordContact: z.string().optional(),
  hasYard: z.boolean(),
  yardFenced: z.boolean().optional(),
  yardSize: z.string().optional(),
  householdSize: z.number().int().min(1, "Household size is required"),
  hasChildren: z.boolean(),
  childrenAges: z.string().optional(),
  hasOtherPets: z.boolean(),
  otherPetsDetails: z.string().optional(),
  petExperience: z.string().min(1, "Pet experience is required"),
  previousPets: z.string().optional(),
  currentVeterinarian: z.string().optional(),
  vetContact: z.string().optional(),
  workSchedule: z.string().min(1, "Work schedule is required"),
  travelFrequency: z.string().optional(),
  exercisePlans: z.string().min(1, "Exercise plans are required"),
  trainingPlans: z.string().optional(),
  personalReferences: z.array(z.object({
    name: z.string().min(1, "Reference name is required"),
    phone: z.string().min(1, "Reference phone is required"),
    relationship: z.string().min(1, "Relationship is required"),
  })).min(2, "At least 2 personal references are required"),
  veterinaryReference: z.object({
    clinicName: z.string().optional(),
    phone: z.string().optional(),
    lastVisit: z.string().optional(),
  }).optional(),
  agreesToTerms: z.boolean().refine(val => val === true, {
    message: "You must agree to the terms and conditions"
  }),
  agreesToHomeVisit: z.boolean().refine(val => val === true, {
    message: "You must agree to a home visit"
  }),
})

// Foster application schema
export const fosterApplicationSchema = z.object({
  maxPets: z.number().int().min(1, "Maximum pets must be at least 1"),
  preferredSpecies: z.array(z.string()).min(1, "Please select at least one species"),
  preferredAges: z.array(z.string()).min(1, "Please select at least one age group"),
  preferredSizes: z.array(z.nativeEnum(PetSize)).optional(),
  fosterExperience: z.string().optional(),
  specialNeedsExperience: z.string().optional(),
  emergencyFoster: z.boolean().default(false),
  longTermFoster: z.boolean().default(true),
  shortTermFoster: z.boolean().default(true),
  hasYard: z.boolean(),
  yardFenced: z.boolean().optional(),
  hasOtherPets: z.boolean(),
  otherPetsDetails: z.string().optional(),
  hasChildren: z.boolean(),
  childrenAges: z.string().optional(),
})

// Volunteer application schema
export const volunteerApplicationSchema = z.object({
  skills: z.array(z.string()).default([]),
  interests: z.array(z.string()).min(1, "Please select at least one interest"),
  availability: z.object({
    monday: z.array(z.string()).default([]),
    tuesday: z.array(z.string()).default([]),
    wednesday: z.array(z.string()).default([]),
    thursday: z.array(z.string()).default([]),
    friday: z.array(z.string()).default([]),
    saturday: z.array(z.string()).default([]),
    sunday: z.array(z.string()).default([]),
  }),
  volunteerExperience: z.string().optional(),
  animalExperience: z.string().optional(),
  specialSkills: z.string().optional(),
  preferredActivities: z.array(z.string()).min(1, "Please select at least one activity"),
  maxHoursPerWeek: z.number().int().min(1).max(40).optional(),
  emergencyContactName: z.string().min(1, "Emergency contact name is required"),
  emergencyContactPhone: z.string().min(1, "Emergency contact phone is required"),
})

// Donation schema
export const donationSchema = z.object({
  amount: z.number().positive("Donation amount must be positive"),
  donorName: z.string().optional(),
  donorEmail: z.string().email().optional(),
  isAnonymous: z.boolean().default(false),
  designation: z.string().optional(),
  petId: z.string().optional(),
  isMemorial: z.boolean().default(false),
  memorialName: z.string().optional(),
  memorialMessage: z.string().optional(),
  isRecurring: z.boolean().default(false),
  recurringInterval: z.string().optional(),
})

export type SignUpInput = z.infer<typeof signUpSchema>
export type SignInInput = z.infer<typeof signInSchema>
export type UserProfileInput = z.infer<typeof userProfileSchema>
export type PetInput = z.infer<typeof petSchema>
export type ApplicationInput = z.infer<typeof applicationSchema>
export type FosterApplicationInput = z.infer<typeof fosterApplicationSchema>
export type VolunteerApplicationInput = z.infer<typeof volunteerApplicationSchema>
export type DonationInput = z.infer<typeof donationSchema>

// Medical Record Schema
export const medicalRecordSchema = z.object({
  petId: z.string(),
  recordType: z.string().min(1, "Record type is required"),
  date: z.string().min(1, "Date is required"),
  description: z.string().min(1, "Description is required"),
  veterinarian: z.string().optional(),
  cost: z.number().optional(),
  followUpRequired: z.boolean().default(false),
  followUpDate: z.string().optional(),
})

export type MedicalRecordInput = z.infer<typeof medicalRecordSchema>

// Behavioral Assessment Schema
export const behavioralAssessmentSchema = z.object({
  petId: z.string(),
  assessmentDate: z.string().min(1, "Assessment date is required"),
  assessorName: z.string().min(1, "Assessor name is required"),
  friendliness: z.number().min(1).max(5),
  energyLevel: z.number().min(1).max(5),
  trainability: z.number().min(1).max(5),
  behaviorWithChildren: z.string(),
  behaviorWithOtherPets: z.string(),
  specialNotes: z.string().optional(),
})

export type BehavioralAssessmentInput = z.infer<typeof behavioralAssessmentSchema>

// Event Schema
export const eventSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  eventType: z.string().min(1, "Event type is required"),
  startDate: z.string().min(1, "Start date is required"),
  endDate: z.string().optional(),
  location: z.string().min(1, "Location is required"),
  maxAttendees: z.number().optional(),
  registrationRequired: z.boolean().default(false),
  isPublic: z.boolean().default(true),
})

export type EventInput = z.infer<typeof eventSchema>

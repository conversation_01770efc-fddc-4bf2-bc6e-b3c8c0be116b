import { prisma } from "@/lib/prisma"
import { PetSize, PetGender } from "@prisma/client"

interface MatchingCriteria {
  userId: string
  preferredSpecies?: string[]
  preferredSizes?: PetSize[]
  preferredAges?: string[] // "puppy", "young", "adult", "senior"
  preferredGender?: PetGender
  goodWithKids?: boolean
  goodWithDogs?: boolean
  goodWithCats?: boolean
  maxActivityLevel?: number
  maxTrainingNeeded?: number
  specialNeedsOk?: boolean
  maxAdoptionFee?: number
}

interface PetMatch {
  pet: any
  matchScore: number
  matchReasons: string[]
}

export class PetMatchingService {
  static async findMatches(criteria: MatchingCriteria): Promise<PetMatch[]> {
    // Get user's application history to understand preferences
    const userApplications = await prisma.application.findMany({
      where: { userId: criteria.userId },
      include: {
        pet: {
          select: {
            species: true,
            size: true,
            age: true,
            gender: true,
            goodWithKids: true,
            goodWithDogs: true,
            goodWithCats: true,
          }
        }
      }
    })

    // Get available pets
    const availablePets = await prisma.pet.findMany({
      where: {
        status: "AVAILABLE",
        ...(criteria.preferredSpecies?.length && {
          species: { in: criteria.preferredSpecies }
        }),
        ...(criteria.preferredSizes?.length && {
          size: { in: criteria.preferredSizes }
        }),
        ...(criteria.preferredGender && {
          gender: criteria.preferredGender
        }),
        ...(criteria.goodWithKids !== undefined && {
          goodWithKids: criteria.goodWithKids
        }),
        ...(criteria.goodWithDogs !== undefined && {
          goodWithDogs: criteria.goodWithDogs
        }),
        ...(criteria.goodWithCats !== undefined && {
          goodWithCats: criteria.goodWithCats
        }),
        ...(criteria.maxAdoptionFee && {
          adoptionFee: { lte: criteria.maxAdoptionFee }
        }),
        ...(criteria.specialNeedsOk === false && {
          specialNeeds: { equals: null }
        }),
      },
      include: {
        photos: {
          where: { isPrimary: true },
          take: 1,
        },
        organization: {
          select: {
            name: true,
            city: true,
            state: true,
          }
        },
        behavioralAssessments: {
          orderBy: { assessmentDate: "desc" },
          take: 1,
        },
        _count: {
          select: {
            applications: true,
            favorites: true,
          }
        }
      }
    })

    // Calculate match scores
    const matches: PetMatch[] = availablePets.map(pet => {
      const { matchScore, matchReasons } = this.calculateMatchScore(pet, criteria, userApplications)
      return {
        pet,
        matchScore,
        matchReasons,
      }
    })

    // Sort by match score (highest first) and return top matches
    return matches
      .sort((a, b) => b.matchScore - a.matchScore)
      .slice(0, 20) // Return top 20 matches
  }

  private static calculateMatchScore(
    pet: any, 
    criteria: MatchingCriteria, 
    userApplications: any[]
  ): { matchScore: number; matchReasons: string[] } {
    let score = 0
    const reasons: string[] = []

    // Base compatibility score (0-100)
    let baseScore = 50

    // Species preference (20 points)
    if (criteria.preferredSpecies?.includes(pet.species)) {
      baseScore += 20
      reasons.push(`Matches your preferred species: ${pet.species}`)
    }

    // Size preference (15 points)
    if (criteria.preferredSizes?.includes(pet.size)) {
      baseScore += 15
      reasons.push(`Matches your preferred size: ${pet.size}`)
    }

    // Age preference (15 points)
    const petAgeCategory = this.getPetAgeCategory(pet.age)
    if (criteria.preferredAges?.includes(petAgeCategory)) {
      baseScore += 15
      reasons.push(`Matches your preferred age: ${petAgeCategory}`)
    }

    // Gender preference (10 points)
    if (criteria.preferredGender && pet.gender === criteria.preferredGender) {
      baseScore += 10
      reasons.push(`Matches your preferred gender: ${pet.gender}`)
    }

    // Compatibility with household (20 points total)
    if (criteria.goodWithKids !== undefined) {
      if (criteria.goodWithKids === pet.goodWithKids) {
        baseScore += 7
        reasons.push(pet.goodWithKids ? "Good with children" : "Suitable for adult-only household")
      } else if (criteria.goodWithKids && !pet.goodWithKids) {
        baseScore -= 10 // Penalty for incompatibility
      }
    }

    if (criteria.goodWithDogs !== undefined) {
      if (criteria.goodWithDogs === pet.goodWithDogs) {
        baseScore += 7
        reasons.push(pet.goodWithDogs ? "Good with other dogs" : "Prefers to be the only dog")
      } else if (criteria.goodWithDogs && !pet.goodWithDogs) {
        baseScore -= 10
      }
    }

    if (criteria.goodWithCats !== undefined) {
      if (criteria.goodWithCats === pet.goodWithCats) {
        baseScore += 6
        reasons.push(pet.goodWithCats ? "Good with cats" : "Prefers a cat-free home")
      } else if (criteria.goodWithCats && !pet.goodWithCats) {
        baseScore -= 10
      }
    }

    // Activity level compatibility (10 points)
    if (criteria.maxActivityLevel && pet.activityLevel) {
      if (pet.activityLevel <= criteria.maxActivityLevel) {
        baseScore += 10
        reasons.push("Activity level matches your lifestyle")
      } else {
        baseScore -= 5
        reasons.push("Higher activity level than preferred")
      }
    }

    // Training needs (10 points)
    if (criteria.maxTrainingNeeded && pet.trainingLevel) {
      const trainingNeeded = 5 - pet.trainingLevel // Invert scale
      if (trainingNeeded <= criteria.maxTrainingNeeded) {
        baseScore += 10
        reasons.push("Training needs match your experience")
      } else {
        baseScore -= 5
        reasons.push("May need more training than preferred")
      }
    }

    // Special needs consideration
    if (pet.specialNeeds) {
      if (criteria.specialNeedsOk) {
        baseScore += 5
        reasons.push("You're open to special needs pets")
      } else {
        baseScore -= 15
        reasons.push("Has special needs")
      }
    }

    // Adoption fee consideration
    if (criteria.maxAdoptionFee && pet.adoptionFee) {
      if (pet.adoptionFee <= criteria.maxAdoptionFee) {
        baseScore += 5
        reasons.push("Within your budget")
      }
    }

    // Behavioral assessment bonus
    if (pet.behavioralAssessments?.length > 0) {
      const assessment = pet.behavioralAssessments[0]
      if (assessment.friendliness >= 4) {
        baseScore += 5
        reasons.push("Very friendly personality")
      }
    }

    // Historical preference learning
    if (userApplications.length > 0) {
      const historicalBonus = this.calculateHistoricalPreferenceBonus(pet, userApplications)
      baseScore += historicalBonus
      if (historicalBonus > 0) {
        reasons.push("Similar to pets you've shown interest in")
      }
    }

    // Popularity adjustment (slight penalty for very popular pets)
    if (pet._count.applications > 10) {
      baseScore -= 2
      reasons.push("High interest from other adopters")
    }

    // Ensure score is within bounds
    score = Math.max(0, Math.min(100, baseScore))

    return { matchScore: score, matchReasons: reasons }
  }

  private static getPetAgeCategory(ageInMonths: number): string {
    if (ageInMonths < 6) return "puppy"
    if (ageInMonths < 24) return "young"
    if (ageInMonths < 84) return "adult"
    return "senior"
  }

  private static calculateHistoricalPreferenceBonus(pet: any, userApplications: any[]): number {
    let bonus = 0
    const preferences = {
      species: {} as Record<string, number>,
      size: {} as Record<string, number>,
      gender: {} as Record<string, number>,
    }

    // Analyze historical preferences
    userApplications.forEach(app => {
      const appPet = app.pet
      preferences.species[appPet.species] = (preferences.species[appPet.species] || 0) + 1
      preferences.size[appPet.size] = (preferences.size[appPet.size] || 0) + 1
      preferences.gender[appPet.gender] = (preferences.gender[appPet.gender] || 0) + 1
    })

    // Apply bonuses based on historical preferences
    if (preferences.species[pet.species] > 0) bonus += 3
    if (preferences.size[pet.size] > 0) bonus += 2
    if (preferences.gender[pet.gender] > 0) bonus += 1

    return bonus
  }

  static async getRecommendationsForUser(userId: string): Promise<PetMatch[]> {
    // Get user's profile and preferences from applications
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        applications: {
          include: {
            pet: {
              select: {
                species: true,
                size: true,
                age: true,
                gender: true,
                goodWithKids: true,
                goodWithDogs: true,
                goodWithCats: true,
              }
            }
          },
          orderBy: { submittedAt: "desc" },
          take: 5, // Last 5 applications
        },
        favorites: {
          include: {
            pet: {
              select: {
                species: true,
                size: true,
                age: true,
                gender: true,
                goodWithKids: true,
                goodWithDogs: true,
                goodWithCats: true,
              }
            }
          },
          orderBy: { createdAt: "desc" },
          take: 10, // Last 10 favorites
        }
      }
    })

    if (!user) {
      throw new Error("User not found")
    }

    // Build criteria from user's history
    const criteria: MatchingCriteria = {
      userId,
      // Extract preferences from applications and favorites
      preferredSpecies: this.extractPreferredValues(
        [...user.applications.map(a => a.pet), ...user.favorites.map(f => f.pet)],
        'species'
      ),
      preferredSizes: this.extractPreferredValues(
        [...user.applications.map(a => a.pet), ...user.favorites.map(f => f.pet)],
        'size'
      ) as PetSize[],
    }

    return this.findMatches(criteria)
  }

  private static extractPreferredValues(pets: any[], field: string): string[] {
    const counts: Record<string, number> = {}
    pets.forEach(pet => {
      const value = pet[field]
      if (value) {
        counts[value] = (counts[value] || 0) + 1
      }
    })

    // Return values that appear more than once, sorted by frequency
    return Object.entries(counts)
      .filter(([_, count]) => count > 1)
      .sort(([_, a], [__, b]) => b - a)
      .map(([value, _]) => value)
  }
}

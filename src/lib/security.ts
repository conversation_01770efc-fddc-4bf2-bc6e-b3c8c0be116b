import { NextRequest } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { UserRole } from "@prisma/client"
import rateLimit from "express-rate-limit"
import slowDown from "express-slow-down"

// Rate limiting configuration
export const createRateLimiter = (options: {
  windowMs: number
  max: number
  message?: string
}) => {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    message: options.message || "Too many requests, please try again later.",
    standardHeaders: true,
    legacyHeaders: false,
  })
}

// Speed limiting for sensitive endpoints
export const createSpeedLimiter = (options: {
  windowMs: number
  delayAfter: number
  delayMs: number
}) => {
  return slowDown({
    windowMs: options.windowMs,
    delayAfter: options.delayAfter,
    delayMs: options.delayMs,
  })
}

// Common rate limiters
export const rateLimiters = {
  // General API rate limiting
  general: createRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // 100 requests per window
  }),
  
  // Authentication endpoints
  auth: createRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts per window
    message: "Too many authentication attempts, please try again later.",
  }),
  
  // Application submissions
  applications: createRateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3, // 3 applications per hour
    message: "Too many application submissions, please wait before submitting another.",
  }),
  
  // File uploads
  uploads: createRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // 10 uploads per window
    message: "Too many file uploads, please try again later.",
  }),
  
  // Password reset
  passwordReset: createRateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3, // 3 reset attempts per hour
    message: "Too many password reset attempts, please try again later.",
  }),
}

// Input sanitization
export class InputSanitizer {
  // Sanitize HTML content
  static sanitizeHtml(input: string): string {
    if (!input) return ""
    
    // Remove script tags and their content
    let sanitized = input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "")
    
    // Remove dangerous attributes
    sanitized = sanitized.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, "")
    sanitized = sanitized.replace(/\s*javascript\s*:/gi, "")
    sanitized = sanitized.replace(/\s*vbscript\s*:/gi, "")
    sanitized = sanitized.replace(/\s*data\s*:/gi, "")
    
    // Remove dangerous tags
    const dangerousTags = ["iframe", "object", "embed", "form", "input", "button", "textarea", "select"]
    dangerousTags.forEach(tag => {
      const regex = new RegExp(`<${tag}\\b[^>]*>.*?<\\/${tag}>`, "gi")
      sanitized = sanitized.replace(regex, "")
    })
    
    return sanitized.trim()
  }
  
  // Sanitize file names
  static sanitizeFileName(fileName: string): string {
    if (!fileName) return ""
    
    // Remove path traversal attempts
    let sanitized = fileName.replace(/\.\./g, "")
    
    // Remove dangerous characters
    sanitized = sanitized.replace(/[<>:"/\\|?*\x00-\x1f]/g, "")
    
    // Limit length
    sanitized = sanitized.substring(0, 255)
    
    return sanitized.trim()
  }
  
  // Sanitize SQL-like inputs (for search queries)
  static sanitizeSearchQuery(query: string): string {
    if (!query) return ""
    
    // Remove SQL injection attempts
    let sanitized = query.replace(/['";\\]/g, "")
    sanitized = sanitized.replace(/\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b/gi, "")
    
    // Limit length
    sanitized = sanitized.substring(0, 100)
    
    return sanitized.trim()
  }
  
  // Validate email format
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email) && email.length <= 254
  }
  
  // Validate phone number
  static validatePhone(phone: string): boolean {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,15}$/
    return phoneRegex.test(phone)
  }
  
  // Validate URL
  static validateUrl(url: string): boolean {
    try {
      const urlObj = new URL(url)
      return ["http:", "https:"].includes(urlObj.protocol)
    } catch {
      return false
    }
  }
}

// Permission checking
export class PermissionChecker {
  // Check if user has required role
  static hasRole(userRole: UserRole, requiredRoles: UserRole[]): boolean {
    return requiredRoles.includes(userRole)
  }
  
  // Check if user can access resource
  static async canAccessResource(
    request: NextRequest,
    requiredRoles: UserRole[],
    resourceOwnerId?: string
  ): Promise<{ allowed: boolean; user?: any; reason?: string }> {
    try {
      const session = await getServerSession(authOptions)
      
      if (!session?.user) {
        return { allowed: false, reason: "Not authenticated" }
      }
      
      // Admin can access everything
      if (session.user.role === UserRole.ADMIN) {
        return { allowed: true, user: session.user }
      }
      
      // Check role permissions
      if (!this.hasRole(session.user.role, requiredRoles)) {
        return { allowed: false, reason: "Insufficient role permissions" }
      }
      
      // Check resource ownership if specified
      if (resourceOwnerId && resourceOwnerId !== session.user.id) {
        // Staff can access resources in their organization
        if (session.user.role === UserRole.STAFF && session.user.organizationId) {
          // Additional check would be needed here to verify resource belongs to same org
          return { allowed: true, user: session.user }
        }
        
        return { allowed: false, reason: "Not resource owner" }
      }
      
      return { allowed: true, user: session.user }
    } catch (error) {
      console.error("Permission check error:", error)
      return { allowed: false, reason: "Permission check failed" }
    }
  }
  
  // Check if user can modify pet
  static canModifyPet(userRole: UserRole, petOrganizationId: string, userOrganizationId?: string): boolean {
    if (userRole === UserRole.ADMIN) return true
    if (userRole === UserRole.STAFF && userOrganizationId === petOrganizationId) return true
    return false
  }
  
  // Check if user can view application
  static canViewApplication(
    userRole: UserRole, 
    applicationUserId: string, 
    currentUserId: string,
    userOrganizationId?: string,
    applicationOrganizationId?: string
  ): boolean {
    if (userRole === UserRole.ADMIN) return true
    if (applicationUserId === currentUserId) return true
    if (userRole === UserRole.STAFF && userOrganizationId === applicationOrganizationId) return true
    return false
  }
}

// Security headers
export const securityHeaders = {
  "X-DNS-Prefetch-Control": "on",
  "Strict-Transport-Security": "max-age=63072000; includeSubDomains; preload",
  "X-XSS-Protection": "1; mode=block",
  "X-Frame-Options": "SAMEORIGIN",
  "X-Content-Type-Options": "nosniff",
  "Referrer-Policy": "origin-when-cross-origin",
  "Content-Security-Policy": [
    "default-src 'self'",
    "script-src 'self' 'unsafe-eval' 'unsafe-inline' https://js.stripe.com https://www.google-analytics.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "img-src 'self' data: https: blob:",
    "font-src 'self' https://fonts.gstatic.com",
    "connect-src 'self' https://api.stripe.com https://www.google-analytics.com",
    "frame-src 'self' https://js.stripe.com",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests"
  ].join("; ")
}

// CSRF protection
export class CSRFProtection {
  private static tokens = new Map<string, { token: string; expires: number }>()
  
  static generateToken(sessionId: string): string {
    const token = this.randomString(32)
    const expires = Date.now() + (60 * 60 * 1000) // 1 hour
    
    this.tokens.set(sessionId, { token, expires })
    
    // Clean up expired tokens
    this.cleanupExpiredTokens()
    
    return token
  }
  
  static validateToken(sessionId: string, token: string): boolean {
    const stored = this.tokens.get(sessionId)
    
    if (!stored) return false
    if (stored.expires < Date.now()) {
      this.tokens.delete(sessionId)
      return false
    }
    
    return stored.token === token
  }
  
  private static cleanupExpiredTokens() {
    const now = Date.now()
    for (const [sessionId, data] of this.tokens.entries()) {
      if (data.expires < now) {
        this.tokens.delete(sessionId)
      }
    }
  }
  
  private static randomString(length: number): string {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    let result = ""
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }
}

// Enhanced audit logging
export class AuditLogger {
  static async log(action: string, details: {
    userId?: string
    resourceType?: string
    resourceId?: string
    ipAddress?: string
    userAgent?: string
    success: boolean
    error?: string
    metadata?: Record<string, any>
  }) {
    try {
      const logEntry = {
        timestamp: new Date().toISOString(),
        action,
        ...details
      }

      // In production, send to your logging service
      console.log("AUDIT:", JSON.stringify(logEntry))

      // Store critical security events in database
      if (this.isCriticalAction(action)) {
        // await prisma.auditLog.create({ data: logEntry })
      }

    } catch (error) {
      console.error("Audit logging failed:", error)
    }
  }

  private static isCriticalAction(action: string): boolean {
    const criticalActions = [
      'user_role_changed',
      'user_deleted',
      'admin_access',
      'permission_denied',
      'login_failed',
      'password_changed',
      'account_locked'
    ]
    return criticalActions.some(critical => action.includes(critical))
  }

  static async logSecurityEvent(event: {
    type: 'login' | 'logout' | 'access_denied' | 'role_change' | 'permission_check'
    userId?: string
    ipAddress?: string
    userAgent?: string
    details?: Record<string, any>
    success: boolean
  }) {
    await this.log(`security_${event.type}`, {
      userId: event.userId,
      ipAddress: event.ipAddress,
      userAgent: event.userAgent,
      success: event.success,
      metadata: event.details
    })
  }
}

// Security monitoring
export class SecurityMonitor {
  private static failedAttempts = new Map<string, { count: number; lastAttempt: number }>()
  private static suspiciousIPs = new Set<string>()

  static recordFailedAttempt(identifier: string, ipAddress?: string) {
    const now = Date.now()
    const current = this.failedAttempts.get(identifier) || { count: 0, lastAttempt: 0 }

    // Reset count if last attempt was more than 1 hour ago
    if (now - current.lastAttempt > 60 * 60 * 1000) {
      current.count = 0
    }

    current.count++
    current.lastAttempt = now
    this.failedAttempts.set(identifier, current)

    // Mark IP as suspicious after 5 failed attempts
    if (current.count >= 5 && ipAddress) {
      this.suspiciousIPs.add(ipAddress)
      AuditLogger.logSecurityEvent({
        type: 'access_denied',
        details: { reason: 'too_many_failed_attempts', identifier },
        ipAddress,
        success: false
      })
    }
  }

  static isBlocked(identifier: string): boolean {
    const current = this.failedAttempts.get(identifier)
    if (!current) return false

    const now = Date.now()
    // Block for 1 hour after 5 failed attempts
    return current.count >= 5 && (now - current.lastAttempt) < 60 * 60 * 1000
  }

  static isSuspiciousIP(ipAddress: string): boolean {
    return this.suspiciousIPs.has(ipAddress)
  }

  static clearFailedAttempts(identifier: string) {
    this.failedAttempts.delete(identifier)
  }
}

// Password strength validation
export class PasswordValidator {
  static validate(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (password.length < 8) {
      errors.push("Password must be at least 8 characters long")
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push("Password must contain at least one lowercase letter")
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push("Password must contain at least one uppercase letter")
    }
    
    if (!/\d/.test(password)) {
      errors.push("Password must contain at least one number")
    }
    
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push("Password must contain at least one special character")
    }
    
    // Check for common weak passwords
    const commonPasswords = [
      "password", "123456", "password123", "admin", "qwerty",
      "letmein", "welcome", "monkey", "dragon", "master"
    ]
    
    if (commonPasswords.includes(password.toLowerCase())) {
      errors.push("Password is too common")
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
}

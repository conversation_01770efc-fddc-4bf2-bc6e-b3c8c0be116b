import { UserRole, UserStatus } from "@prisma/client"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { NextRequest } from "next/server"

// Define granular permissions
export enum Permission {
  // User Management
  VIEW_USERS = "view_users",
  CREATE_USERS = "create_users", 
  EDIT_USERS = "edit_users",
  DELETE_USERS = "delete_users",
  MANAGE_USER_ROLES = "manage_user_roles",
  
  // Pet Management
  VIEW_PETS = "view_pets",
  CREATE_PETS = "create_pets",
  EDIT_PETS = "edit_pets", 
  DELETE_PETS = "delete_pets",
  MANA<PERSON>_PET_STATUS = "manage_pet_status",
  
  // Application Management
  VIEW_ALL_APPLICATIONS = "view_all_applications",
  REVIEW_APPLICATIONS = "review_applications",
  APPROVE_APPLICATIONS = "approve_applications",
  REJECT_APPLICATIONS = "reject_applications",
  
  // Foster Management
  VIEW_FOSTER_APPLICATIONS = "view_foster_applications",
  MANAGE_FOSTER_FAMILIES = "manage_foster_families",
  ASSIGN_FOSTER_PETS = "assign_foster_pets",
  
  // Volunteer Management
  VIEW_VOLUNTEER_APPLICATIONS = "view_volunteer_applications",
  MANAGE_VOLUNTEERS = "manage_volunteers",
  ASSIGN_VOLUNTEER_TASKS = "assign_volunteer_tasks",
  
  // Medical Records
  VIEW_MEDICAL_RECORDS = "view_medical_records",
  CREATE_MEDICAL_RECORDS = "create_medical_records",
  EDIT_MEDICAL_RECORDS = "edit_medical_records",
  
  // Transport & Tracking
  VIEW_TRANSPORT = "view_transport",
  CREATE_TRANSPORT = "create_transport",
  MANAGE_TRACKING = "manage_tracking",
  
  // Donations & Inventory
  VIEW_DONATIONS = "view_donations",
  MANAGE_INVENTORY = "manage_inventory",
  VIEW_FINANCIAL_REPORTS = "view_financial_reports",
  
  // Reports & Analytics
  VIEW_REPORTS = "view_reports",
  CREATE_REPORTS = "create_reports",
  EXPORT_DATA = "export_data",
  
  // Content Management
  MANAGE_CONTENT = "manage_content",
  MANAGE_BLOG = "manage_blog",
  MANAGE_ANNOUNCEMENTS = "manage_announcements",
  
  // Communication
  SEND_NOTIFICATIONS = "send_notifications",
  MANAGE_COMMUNICATIONS = "manage_communications",
  
  // System Administration
  MANAGE_SYSTEM_SETTINGS = "manage_system_settings",
  VIEW_AUDIT_LOGS = "view_audit_logs",
  MANAGE_INTEGRATIONS = "manage_integrations",
}

// Role-based permission mapping
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.PUBLIC]: [
    Permission.VIEW_PETS,
  ],
  
  [UserRole.ADOPTER]: [
    Permission.VIEW_PETS,
  ],
  
  [UserRole.VOLUNTEER]: [
    Permission.VIEW_PETS,
    Permission.VIEW_VOLUNTEER_APPLICATIONS,
  ],
  
  [UserRole.STAFF]: [
    Permission.VIEW_USERS,
    Permission.EDIT_USERS,
    Permission.VIEW_PETS,
    Permission.CREATE_PETS,
    Permission.EDIT_PETS,
    Permission.MANAGE_PET_STATUS,
    Permission.VIEW_ALL_APPLICATIONS,
    Permission.REVIEW_APPLICATIONS,
    Permission.APPROVE_APPLICATIONS,
    Permission.REJECT_APPLICATIONS,
    Permission.VIEW_FOSTER_APPLICATIONS,
    Permission.MANAGE_FOSTER_FAMILIES,
    Permission.ASSIGN_FOSTER_PETS,
    Permission.VIEW_VOLUNTEER_APPLICATIONS,
    Permission.MANAGE_VOLUNTEERS,
    Permission.ASSIGN_VOLUNTEER_TASKS,
    Permission.VIEW_MEDICAL_RECORDS,
    Permission.CREATE_MEDICAL_RECORDS,
    Permission.EDIT_MEDICAL_RECORDS,
    Permission.VIEW_TRANSPORT,
    Permission.CREATE_TRANSPORT,
    Permission.MANAGE_TRACKING,
    Permission.VIEW_DONATIONS,
    Permission.MANAGE_INVENTORY,
    Permission.VIEW_REPORTS,
    Permission.CREATE_REPORTS,
    Permission.MANAGE_CONTENT,
    Permission.MANAGE_BLOG,
    Permission.SEND_NOTIFICATIONS,
    Permission.MANAGE_COMMUNICATIONS,
  ],
  
  [UserRole.ADMIN]: [
    // Admins have all permissions
    ...Object.values(Permission),
  ],
  
  [UserRole.PARTNER_ORG]: [
    Permission.VIEW_PETS,
    Permission.CREATE_PETS,
    Permission.EDIT_PETS,
    Permission.VIEW_ALL_APPLICATIONS,
    Permission.REVIEW_APPLICATIONS,
    Permission.VIEW_TRANSPORT,
    Permission.CREATE_TRANSPORT,
    Permission.VIEW_REPORTS,
  ],
}

// Permission checking utilities
export class PermissionManager {
  /**
   * Check if a user role has a specific permission
   */
  static hasPermission(userRole: UserRole, permission: Permission): boolean {
    const rolePermissions = ROLE_PERMISSIONS[userRole] || []
    return rolePermissions.includes(permission)
  }

  /**
   * Check if a user role has any of the specified permissions
   */
  static hasAnyPermission(userRole: UserRole, permissions: Permission[]): boolean {
    return permissions.some(permission => this.hasPermission(userRole, permission))
  }

  /**
   * Check if a user role has all of the specified permissions
   */
  static hasAllPermissions(userRole: UserRole, permissions: Permission[]): boolean {
    return permissions.every(permission => this.hasPermission(userRole, permission))
  }

  /**
   * Get all permissions for a user role
   */
  static getRolePermissions(userRole: UserRole): Permission[] {
    return ROLE_PERMISSIONS[userRole] || []
  }

  /**
   * Check if user can access a resource based on ownership and permissions
   */
  static async canAccessResource(
    request: NextRequest,
    requiredPermissions: Permission[],
    resourceOwnerId?: string
  ): Promise<{ allowed: boolean; user?: any; reason?: string }> {
    try {
      const session = await getServerSession(authOptions)
      
      if (!session?.user) {
        return { allowed: false, reason: "Not authenticated" }
      }

      // Check if user account is active
      if (session.user.status !== UserStatus.ACTIVE) {
        return { allowed: false, reason: "Account not active" }
      }

      const userRole = session.user.role

      // Admin can access everything
      if (userRole === UserRole.ADMIN) {
        return { allowed: true, user: session.user }
      }

      // Check if user has any of the required permissions
      if (!this.hasAnyPermission(userRole, requiredPermissions)) {
        return { allowed: false, reason: "Insufficient permissions" }
      }

      // Check resource ownership if specified
      if (resourceOwnerId && resourceOwnerId !== session.user.id) {
        // Staff can access resources in their organization
        if (userRole === UserRole.STAFF && session.user.organizationId) {
          // Additional check would be needed here to verify resource belongs to same org
          return { allowed: true, user: session.user }
        }
        
        return { allowed: false, reason: "Not resource owner" }
      }

      return { allowed: true, user: session.user }
    } catch (error) {
      console.error("Permission check error:", error)
      return { allowed: false, reason: "Permission check failed" }
    }
  }
}

// User role hierarchy (higher number = more permissions)
export const ROLE_HIERARCHY: Record<UserRole, number> = {
  [UserRole.PUBLIC]: 0,
  [UserRole.ADOPTER]: 1,
  [UserRole.VOLUNTEER]: 2,
  [UserRole.PARTNER_ORG]: 3,
  [UserRole.STAFF]: 4,
  [UserRole.ADMIN]: 5,
}

/**
 * Check if one role is higher than another in the hierarchy
 */
export function isRoleHigher(role1: UserRole, role2: UserRole): boolean {
  return ROLE_HIERARCHY[role1] > ROLE_HIERARCHY[role2]
}

/**
 * Check if a user can manage another user based on role hierarchy
 */
export function canManageUser(managerRole: UserRole, targetRole: UserRole): boolean {
  // Admins can manage everyone
  if (managerRole === UserRole.ADMIN) return true
  
  // Staff can manage volunteers and below
  if (managerRole === UserRole.STAFF) {
    return [UserRole.PUBLIC, UserRole.ADOPTER, UserRole.VOLUNTEER].includes(targetRole)
  }
  
  return false
}

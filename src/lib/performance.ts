// Performance monitoring and optimization utilities

export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: Map<string, number[]> = new Map()
  private observers: Map<string, PerformanceObserver> = new Map()

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  // Initialize performance monitoring
  init() {
    if (typeof window === 'undefined') return

    // Monitor Core Web Vitals
    this.observeWebVitals()
    
    // Monitor resource loading
    this.observeResourceTiming()
    
    // Monitor navigation timing
    this.observeNavigationTiming()
    
    // Monitor long tasks
    this.observeLongTasks()
  }

  private observeWebVitals() {
    // Largest Contentful Paint (LCP)
    this.createObserver('largest-contentful-paint', (entries) => {
      const lastEntry = entries[entries.length - 1]
      this.recordMetric('LCP', lastEntry.startTime)
    })

    // First Input Delay (FID)
    this.createObserver('first-input', (entries) => {
      const firstEntry = entries[0]
      this.recordMetric('FID', firstEntry.processingStart - firstEntry.startTime)
    })

    // Cumulative Layout Shift (CLS)
    this.createObserver('layout-shift', (entries) => {
      let clsValue = 0
      for (const entry of entries) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value
        }
      }
      this.recordMetric('CLS', clsValue)
    })
  }

  private observeResourceTiming() {
    this.createObserver('resource', (entries) => {
      for (const entry of entries) {
        const duration = entry.responseEnd - entry.startTime
        this.recordMetric(`resource-${this.getResourceType(entry.name)}`, duration)
      }
    })
  }

  private observeNavigationTiming() {
    this.createObserver('navigation', (entries) => {
      const entry = entries[0] as PerformanceNavigationTiming
      
      // Time to First Byte
      const ttfb = entry.responseStart - entry.requestStart
      this.recordMetric('TTFB', ttfb)
      
      // DOM Content Loaded
      const dcl = entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart
      this.recordMetric('DCL', dcl)
      
      // Load Complete
      const loadComplete = entry.loadEventEnd - entry.loadEventStart
      this.recordMetric('LoadComplete', loadComplete)
    })
  }

  private observeLongTasks() {
    this.createObserver('longtask', (entries) => {
      for (const entry of entries) {
        this.recordMetric('LongTask', entry.duration)
      }
    })
  }

  private createObserver(type: string, callback: (entries: any[]) => void) {
    try {
      const observer = new PerformanceObserver((list) => {
        callback(list.getEntries())
      })
      observer.observe({ type, buffered: true })
      this.observers.set(type, observer)
    } catch (error) {
      console.warn(`Performance observer for ${type} not supported`, error)
    }
  }

  private recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }
    this.metrics.get(name)!.push(value)
    
    // Send to analytics if configured
    this.sendToAnalytics(name, value)
  }

  private getResourceType(url: string): string {
    if (url.includes('.css')) return 'css'
    if (url.includes('.js')) return 'js'
    if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) return 'image'
    if (url.includes('/api/')) return 'api'
    return 'other'
  }

  private sendToAnalytics(metric: string, value: number) {
    // Send to your analytics service
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'performance_metric', {
        metric_name: metric,
        metric_value: Math.round(value),
        custom_parameter: 'petadopt_performance'
      })
    }
  }

  // Get performance summary
  getSummary() {
    const summary: Record<string, any> = {}
    
    for (const [metric, values] of this.metrics.entries()) {
      summary[metric] = {
        count: values.length,
        average: values.reduce((a, b) => a + b, 0) / values.length,
        min: Math.min(...values),
        max: Math.max(...values),
        latest: values[values.length - 1]
      }
    }
    
    return summary
  }

  // Cleanup observers
  disconnect() {
    for (const observer of this.observers.values()) {
      observer.disconnect()
    }
    this.observers.clear()
  }
}

// Image optimization utilities
export class ImageOptimizer {
  static getOptimizedImageUrl(
    src: string, 
    width?: number, 
    height?: number, 
    quality: number = 80
  ): string {
    if (!src) return ''
    
    // If using Next.js Image component, it handles optimization
    // For external images, you might want to use a service like Cloudinary
    const params = new URLSearchParams()
    
    if (width) params.set('w', width.toString())
    if (height) params.set('h', height.toString())
    params.set('q', quality.toString())
    
    // Example for Cloudinary or similar service
    // return `https://res.cloudinary.com/your-cloud/image/fetch/${params.toString()}/${encodeURIComponent(src)}`
    
    return src
  }

  static preloadCriticalImages(urls: string[]) {
    if (typeof window === 'undefined') return
    
    urls.forEach(url => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'image'
      link.href = url
      document.head.appendChild(link)
    })
  }

  static lazyLoadImages() {
    if (typeof window === 'undefined') return
    
    const images = document.querySelectorAll('img[data-src]')
    
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            img.src = img.dataset.src!
            img.classList.remove('lazy')
            imageObserver.unobserve(img)
          }
        })
      })
      
      images.forEach(img => imageObserver.observe(img))
    } else {
      // Fallback for older browsers
      images.forEach(img => {
        const imgElement = img as HTMLImageElement
        imgElement.src = imgElement.dataset.src!
      })
    }
  }
}

// API response caching
export class APICache {
  private static cache = new Map<string, { data: any; timestamp: number; ttl: number }>()

  static set(key: string, data: any, ttlMinutes: number = 5) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMinutes * 60 * 1000
    })
  }

  static get(key: string): any | null {
    const cached = this.cache.get(key)
    
    if (!cached) return null
    
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return cached.data
  }

  static clear() {
    this.cache.clear()
  }

  static clearExpired() {
    const now = Date.now()
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > value.ttl) {
        this.cache.delete(key)
      }
    }
  }
}

// Bundle size analyzer
export class BundleAnalyzer {
  static analyzeChunks() {
    if (typeof window === 'undefined') return
    
    const scripts = Array.from(document.querySelectorAll('script[src]'))
    const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'))
    
    const analysis = {
      scripts: scripts.map(script => ({
        src: (script as HTMLScriptElement).src,
        async: (script as HTMLScriptElement).async,
        defer: (script as HTMLScriptElement).defer
      })),
      styles: styles.map(style => ({
        href: (style as HTMLLinkElement).href,
        media: (style as HTMLLinkElement).media
      })),
      totalScripts: scripts.length,
      totalStyles: styles.length
    }
    
    console.table(analysis)
    return analysis
  }
}

// Database query optimization
export class QueryOptimizer {
  static optimizePetQuery(filters: any) {
    const optimized: any = {
      select: {
        id: true,
        name: true,
        species: true,
        breed: true,
        age: true,
        size: true,
        gender: true,
        color: true,
        adoptionFee: true,
        goodWithKids: true,
        goodWithDogs: true,
        goodWithCats: true,
        status: true,
        photos: {
          where: { isPrimary: true },
          take: 1,
          select: {
            url: true,
            caption: true
          }
        },
        organization: {
          select: {
            name: true,
            city: true,
            state: true
          }
        },
        _count: {
          select: {
            applications: true,
            favorites: true
          }
        }
      },
      where: {
        status: 'AVAILABLE'
      }
    }

    // Add filters only if they exist
    if (filters.species?.length) {
      optimized.where.species = { in: filters.species }
    }
    
    if (filters.size?.length) {
      optimized.where.size = { in: filters.size }
    }
    
    if (filters.age) {
      optimized.where.age = filters.age
    }
    
    if (filters.goodWithKids !== undefined) {
      optimized.where.goodWithKids = filters.goodWithKids
    }
    
    if (filters.goodWithDogs !== undefined) {
      optimized.where.goodWithDogs = filters.goodWithDogs
    }
    
    if (filters.goodWithCats !== undefined) {
      optimized.where.goodWithCats = filters.goodWithCats
    }
    
    if (filters.maxFee) {
      optimized.where.adoptionFee = { lte: filters.maxFee }
    }

    return optimized
  }
}

// Initialize performance monitoring
if (typeof window !== 'undefined') {
  const monitor = PerformanceMonitor.getInstance()
  monitor.init()
  
  // Clean up expired cache entries every 5 minutes
  setInterval(() => {
    APICache.clearExpired()
  }, 5 * 60 * 1000)
}

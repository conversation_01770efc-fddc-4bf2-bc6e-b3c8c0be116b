// Third-party API integrations

// Email service integration (SendGrid, Resend, etc.)
export class EmailService {
  private static apiKey = process.env.EMAIL_API_KEY
  private static fromEmail = process.env.FROM_EMAIL || "<EMAIL>"
  
  static async sendEmail(options: {
    to: string | string[]
    subject: string
    html: string
    text?: string
    templateId?: string
    templateData?: Record<string, any>
  }) {
    try {
      // Example using SendGrid
      const response = await fetch("https://api.sendgrid.com/v3/mail/send", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          from: { email: this.fromEmail },
          personalizations: [{
            to: Array.isArray(options.to) 
              ? options.to.map(email => ({ email }))
              : [{ email: options.to }],
            subject: options.subject,
            ...(options.templateData && { dynamic_template_data: options.templateData })
          }],
          ...(options.templateId 
            ? { template_id: options.templateId }
            : {
                content: [
                  { type: "text/html", value: options.html },
                  ...(options.text ? [{ type: "text/plain", value: options.text }] : [])
                ]
              }
          )
        })
      })
      
      if (!response.ok) {
        throw new Error(`Email API error: ${response.status}`)
      }
      
      return { success: true }
    } catch (error) {
      console.error("Email sending failed:", error)
      return { success: false, error: error instanceof Error ? error.message : "Unknown error" }
    }
  }
  
  // Pre-built email templates
  static async sendWelcomeEmail(userEmail: string, userName: string) {
    return this.sendEmail({
      to: userEmail,
      subject: "Welcome to PetAdopt!",
      html: `
        <h1>Welcome to PetAdopt, ${userName}!</h1>
        <p>Thank you for joining our community of pet lovers.</p>
        <p>You can now:</p>
        <ul>
          <li>Browse available pets</li>
          <li>Submit adoption applications</li>
          <li>Save your favorite pets</li>
          <li>Apply to volunteer or foster</li>
        </ul>
        <p><a href="${process.env.NEXTAUTH_URL}/pets">Start browsing pets</a></p>
      `,
      text: `Welcome to PetAdopt, ${userName}! Thank you for joining our community.`
    })
  }
  
  static async sendApplicationStatusUpdate(
    userEmail: string, 
    userName: string, 
    petName: string, 
    status: string,
    message?: string
  ) {
    const statusMessages = {
      APPROVED: "Congratulations! Your application has been approved.",
      REJECTED: "Unfortunately, your application was not approved at this time.",
      UNDER_REVIEW: "Your application is currently under review.",
      REFERENCE_CHECK: "We are contacting your references.",
      HOME_VISIT_SCHEDULED: "A home visit has been scheduled."
    }
    
    return this.sendEmail({
      to: userEmail,
      subject: `Application Update for ${petName}`,
      html: `
        <h1>Application Update</h1>
        <p>Hi ${userName},</p>
        <p>Your adoption application for <strong>${petName}</strong> has been updated.</p>
        <p><strong>Status:</strong> ${status.replace('_', ' ')}</p>
        <p>${statusMessages[status as keyof typeof statusMessages] || ''}</p>
        ${message ? `<p><strong>Message from staff:</strong> ${message}</p>` : ''}
        <p><a href="${process.env.NEXTAUTH_URL}/dashboard/applications">View your applications</a></p>
      `
    })
  }
}

// SMS service integration (Twilio, etc.)
export class SMSService {
  private static accountSid = process.env.TWILIO_ACCOUNT_SID
  private static authToken = process.env.TWILIO_AUTH_TOKEN
  private static fromNumber = process.env.TWILIO_FROM_NUMBER
  
  static async sendSMS(to: string, message: string) {
    try {
      const response = await fetch(
        `https://api.twilio.com/2010-04-01/Accounts/${this.accountSid}/Messages.json`,
        {
          method: "POST",
          headers: {
            "Authorization": `Basic ${Buffer.from(`${this.accountSid}:${this.authToken}`).toString('base64')}`,
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: new URLSearchParams({
            From: this.fromNumber!,
            To: to,
            Body: message
          })
        }
      )
      
      if (!response.ok) {
        throw new Error(`SMS API error: ${response.status}`)
      }
      
      return { success: true }
    } catch (error) {
      console.error("SMS sending failed:", error)
      return { success: false, error: error instanceof Error ? error.message : "Unknown error" }
    }
  }
  
  static async sendAppointmentReminder(phone: string, appointmentDetails: {
    type: string
    date: string
    location: string
    petName?: string
  }) {
    const message = `Reminder: You have a ${appointmentDetails.type} scheduled for ${appointmentDetails.date} at ${appointmentDetails.location}${appointmentDetails.petName ? ` regarding ${appointmentDetails.petName}` : ''}. Reply STOP to opt out.`
    
    return this.sendSMS(phone, message)
  }
}

// File storage integration (AWS S3, Cloudinary, etc.)
export class FileStorageService {
  private static cloudinaryUrl = process.env.CLOUDINARY_URL
  private static cloudName = process.env.CLOUDINARY_CLOUD_NAME
  private static apiKey = process.env.CLOUDINARY_API_KEY
  private static apiSecret = process.env.CLOUDINARY_API_SECRET
  
  static async uploadFile(file: File, folder: string = "uploads"): Promise<{
    success: boolean
    url?: string
    publicId?: string
    error?: string
  }> {
    try {
      const formData = new FormData()
      formData.append("file", file)
      formData.append("upload_preset", "petadopt_uploads") // Configure in Cloudinary
      formData.append("folder", folder)
      
      const response = await fetch(
        `https://api.cloudinary.com/v1_1/${this.cloudName}/image/upload`,
        {
          method: "POST",
          body: formData
        }
      )
      
      if (!response.ok) {
        throw new Error(`Upload failed: ${response.status}`)
      }
      
      const result = await response.json()
      
      return {
        success: true,
        url: result.secure_url,
        publicId: result.public_id
      }
    } catch (error) {
      console.error("File upload failed:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Upload failed"
      }
    }
  }
  
  static async deleteFile(publicId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const timestamp = Math.round(Date.now() / 1000)
      const signature = this.generateSignature({ public_id: publicId, timestamp })
      
      const formData = new FormData()
      formData.append("public_id", publicId)
      formData.append("timestamp", timestamp.toString())
      formData.append("api_key", this.apiKey!)
      formData.append("signature", signature)
      
      const response = await fetch(
        `https://api.cloudinary.com/v1_1/${this.cloudName}/image/destroy`,
        {
          method: "POST",
          body: formData
        }
      )
      
      if (!response.ok) {
        throw new Error(`Delete failed: ${response.status}`)
      }
      
      return { success: true }
    } catch (error) {
      console.error("File deletion failed:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Deletion failed"
      }
    }
  }
  
  private static generateSignature(params: Record<string, any>): string {
    const crypto = require('crypto')
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&')
    
    return crypto
      .createHash('sha1')
      .update(sortedParams + this.apiSecret)
      .digest('hex')
  }
}

// Payment processing (Stripe)
export class PaymentService {
  private static stripeSecretKey = process.env.STRIPE_SECRET_KEY
  
  static async createPaymentIntent(amount: number, currency: string = "usd", metadata?: Record<string, string>) {
    try {
      const response = await fetch("https://api.stripe.com/v1/payment_intents", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${this.stripeSecretKey}`,
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams({
          amount: (amount * 100).toString(), // Convert to cents
          currency,
          ...(metadata && Object.entries(metadata).reduce((acc, [key, value]) => {
            acc[`metadata[${key}]`] = value
            return acc
          }, {} as Record<string, string>))
        })
      })
      
      if (!response.ok) {
        throw new Error(`Payment intent creation failed: ${response.status}`)
      }
      
      const paymentIntent = await response.json()
      return { success: true, paymentIntent }
    } catch (error) {
      console.error("Payment intent creation failed:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Payment failed"
      }
    }
  }
  
  static async createCheckoutSession(options: {
    lineItems: Array<{
      price_data: {
        currency: string
        product_data: {
          name: string
          description?: string
        }
        unit_amount: number
      }
      quantity: number
    }>
    successUrl: string
    cancelUrl: string
    metadata?: Record<string, string>
  }) {
    try {
      const response = await fetch("https://api.stripe.com/v1/checkout/sessions", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${this.stripeSecretKey}`,
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams({
          "mode": "payment",
          "success_url": options.successUrl,
          "cancel_url": options.cancelUrl,
          ...options.lineItems.reduce((acc, item, index) => {
            acc[`line_items[${index}][price_data][currency]`] = item.price_data.currency
            acc[`line_items[${index}][price_data][product_data][name]`] = item.price_data.product_data.name
            if (item.price_data.product_data.description) {
              acc[`line_items[${index}][price_data][product_data][description]`] = item.price_data.product_data.description
            }
            acc[`line_items[${index}][price_data][unit_amount]`] = item.price_data.unit_amount.toString()
            acc[`line_items[${index}][quantity]`] = item.quantity.toString()
            return acc
          }, {} as Record<string, string>),
          ...(options.metadata && Object.entries(options.metadata).reduce((acc, [key, value]) => {
            acc[`metadata[${key}]`] = value
            return acc
          }, {} as Record<string, string>))
        })
      })
      
      if (!response.ok) {
        throw new Error(`Checkout session creation failed: ${response.status}`)
      }
      
      const session = await response.json()
      return { success: true, session }
    } catch (error) {
      console.error("Checkout session creation failed:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Checkout failed"
      }
    }
  }
}

// Mapping service integration (Google Maps, MapBox)
export class MappingService {
  private static googleMapsApiKey = process.env.GOOGLE_MAPS_API_KEY
  
  static async geocodeAddress(address: string): Promise<{
    success: boolean
    coordinates?: { lat: number; lng: number }
    formattedAddress?: string
    error?: string
  }> {
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${this.googleMapsApiKey}`
      )
      
      if (!response.ok) {
        throw new Error(`Geocoding failed: ${response.status}`)
      }
      
      const data = await response.json()
      
      if (data.status !== "OK" || !data.results.length) {
        throw new Error(`Geocoding failed: ${data.status}`)
      }
      
      const result = data.results[0]
      
      return {
        success: true,
        coordinates: {
          lat: result.geometry.location.lat,
          lng: result.geometry.location.lng
        },
        formattedAddress: result.formatted_address
      }
    } catch (error) {
      console.error("Geocoding failed:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Geocoding failed"
      }
    }
  }
  
  static async calculateDistance(
    origin: { lat: number; lng: number },
    destination: { lat: number; lng: number }
  ): Promise<{
    success: boolean
    distance?: { text: string; value: number }
    duration?: { text: string; value: number }
    error?: string
  }> {
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/distancematrix/json?origins=${origin.lat},${origin.lng}&destinations=${destination.lat},${destination.lng}&key=${this.googleMapsApiKey}`
      )
      
      if (!response.ok) {
        throw new Error(`Distance calculation failed: ${response.status}`)
      }
      
      const data = await response.json()
      
      if (data.status !== "OK" || !data.rows.length) {
        throw new Error(`Distance calculation failed: ${data.status}`)
      }
      
      const element = data.rows[0].elements[0]
      
      if (element.status !== "OK") {
        throw new Error(`Distance calculation failed: ${element.status}`)
      }
      
      return {
        success: true,
        distance: element.distance,
        duration: element.duration
      }
    } catch (error) {
      console.error("Distance calculation failed:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Distance calculation failed"
      }
    }
  }
}

// Analytics integration (Google Analytics, Mixpanel, etc.)
export class AnalyticsService {
  static trackEvent(eventName: string, properties?: Record<string, any>) {
    if (typeof window !== 'undefined') {
      // Google Analytics 4
      if ((window as any).gtag) {
        (window as any).gtag('event', eventName, properties)
      }
      
      // Mixpanel
      if ((window as any).mixpanel) {
        (window as any).mixpanel.track(eventName, properties)
      }
      
      // Custom analytics
      console.log('Analytics Event:', { eventName, properties })
    }
  }
  
  static trackPageView(path: string) {
    if (typeof window !== 'undefined') {
      if ((window as any).gtag) {
        (window as any).gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID, {
          page_path: path
        })
      }
    }
  }
  
  static trackUserAction(action: string, category: string, label?: string, value?: number) {
    this.trackEvent('user_action', {
      action,
      category,
      label,
      value
    })
  }
}

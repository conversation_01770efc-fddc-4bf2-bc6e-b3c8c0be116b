"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useSession } from "next-auth/react"
import { cn } from "@/lib/utils"
import { UserRole } from "@prisma/client"
import {
  BarChart3,
  Car,
  FileText,
  Heart,
  Home,
  Package,
  MessageSquare,
  PlusCircle,
  Settings,
  Shield,
  Users,
  UserCheck,
  DollarSign,
  Calendar,
  Stethoscope,
  BookOpen,
  Bell,
  Database,
  Activity,
} from "lucide-react"

interface SidebarItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  roles: UserRole[]
  description?: string
}

const sidebarItems: SidebarItem[] = [
  {
    title: "Dashboard",
    href: "/admin",
    icon: Home,
    roles: [UserRole.STAFF, UserRole.ADMIN],
    description: "Overview and quick actions"
  },
  {
    title: "Pet Management",
    href: "/admin/pets",
    icon: Heart,
    roles: [UserRole.STAFF, UserRole.ADMIN],
    description: "Manage pets and their information"
  },
  {
    title: "Add New Pet",
    href: "/admin/pets/new",
    icon: PlusCircle,
    roles: [UserRole.STAFF, UserRole.ADMIN],
    description: "Add a new pet to the system"
  },
  {
    title: "Applications",
    href: "/admin/applications",
    icon: FileText,
    roles: [UserRole.STAFF, UserRole.ADMIN],
    description: "Review adoption applications"
  },
  {
    title: "Volunteers",
    href: "/admin/volunteers",
    icon: Users,
    roles: [UserRole.STAFF, UserRole.ADMIN],
    description: "Manage volunteer applications"
  },
  {
    title: "Foster Families",
    href: "/admin/foster",
    icon: UserCheck,
    roles: [UserRole.STAFF, UserRole.ADMIN],
    description: "Manage foster families"
  },
  {
    title: "Transport & Tracking",
    href: "/admin/tracking",
    icon: Car,
    roles: [UserRole.STAFF, UserRole.ADMIN],
    description: "Manage pet transport and tracking"
  },
  {
    title: "Medical Records",
    href: "/admin/medical",
    icon: Stethoscope,
    roles: [UserRole.STAFF, UserRole.ADMIN],
    description: "Manage pet medical records"
  },
  {
    title: "Appointments",
    href: "/admin/appointments",
    icon: Calendar,
    roles: [UserRole.STAFF, UserRole.ADMIN],
    description: "Schedule and manage appointments"
  },
  {
    title: "Donations",
    href: "/admin/donations",
    icon: DollarSign,
    roles: [UserRole.STAFF, UserRole.ADMIN],
    description: "Track donations and fundraising"
  },
  {
    title: "Inventory",
    href: "/admin/inventory",
    icon: Package,
    roles: [UserRole.STAFF, UserRole.ADMIN],
    description: "Manage supplies and inventory"
  },
  {
    title: "Communications",
    href: "/admin/communications",
    icon: MessageSquare,
    roles: [UserRole.STAFF, UserRole.ADMIN],
    description: "Send notifications and messages"
  },
  {
    title: "Content Management",
    href: "/admin/content",
    icon: BookOpen,
    roles: [UserRole.STAFF, UserRole.ADMIN],
    description: "Manage website content and blog"
  },
  {
    title: "Reports & Analytics",
    href: "/admin/reports",
    icon: BarChart3,
    roles: [UserRole.STAFF, UserRole.ADMIN],
    description: "View reports and analytics"
  },
  {
    title: "User Management",
    href: "/admin/users",
    icon: Shield,
    roles: [UserRole.ADMIN],
    description: "Manage user accounts and roles"
  },
  {
    title: "System Settings",
    href: "/admin/settings",
    icon: Settings,
    roles: [UserRole.ADMIN],
    description: "Configure system settings"
  },
  {
    title: "Audit Logs",
    href: "/admin/audit",
    icon: Activity,
    roles: [UserRole.ADMIN],
    description: "View system audit logs"
  },
  {
    title: "Database Management",
    href: "/admin/database",
    icon: Database,
    roles: [UserRole.ADMIN],
    description: "Database maintenance and backups"
  },
]

interface AdminSidebarProps {
  className?: string
}

export function AdminSidebar({ className }: AdminSidebarProps) {
  const pathname = usePathname()
  const { data: session } = useSession()

  if (!session?.user?.role || ![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role as UserRole)) {
    return null
  }

  const userRole = session.user.role as UserRole
  const filteredItems = sidebarItems.filter(item => item.roles.includes(userRole))

  return (
    <div className={cn("flex flex-col w-64 bg-white border-r border-gray-200", className)}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <Shield className="h-8 w-8 text-blue-600" />
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Admin Panel</h2>
            <p className="text-sm text-gray-500 capitalize">{userRole.toLowerCase()} Access</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
        {filteredItems.map((item) => {
          const isActive = pathname === item.href || 
            (item.href !== "/admin" && pathname.startsWith(item.href))
          
          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                isActive
                  ? "bg-blue-100 text-blue-700 border-r-2 border-blue-700"
                  : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
              )}
              title={item.description}
            >
              <item.icon className={cn(
                "mr-3 h-5 w-5",
                isActive ? "text-blue-700" : "text-gray-400"
              )} />
              {item.title}
            </Link>
          )
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-blue-700">
                {session.user.name?.charAt(0).toUpperCase()}
              </span>
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {session.user.name}
            </p>
            <p className="text-xs text-gray-500 truncate">
              {session.user.email}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

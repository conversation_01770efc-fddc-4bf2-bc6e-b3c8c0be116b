import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Heart, 
  Users, 
  Award, 
  Target,
  Shield,
  Globe,
  Calendar,
  TrendingUp,
  CheckCircle,
  Star,
  MapPin,
  Phone,
  Mail
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"

const TEAM_MEMBERS = [
  {
    name: "Dr. <PERSON>",
    role: "Executive Director",
    image: "/images/team/sarah.jpg",
    bio: "Veterinarian with 15+ years of experience in animal welfare and rescue operations.",
    specialties: ["Animal Medicine", "Rescue Operations", "Policy Development"]
  },
  {
    name: "<PERSON>",
    role: "Operations Manager",
    image: "/images/team/michael.jpg",
    bio: "Former shelter manager with expertise in volunteer coordination and facility management.",
    specialties: ["Operations", "Volunteer Management", "Facility Planning"]
  },
  {
    name: "<PERSON>",
    role: "Adoption Coordinator",
    image: "/images/team/emily.jpg",
    bio: "Animal behaviorist specializing in pet-family matching and adoption counseling.",
    specialties: ["Animal Behavior", "Adoption Counseling", "Training"]
  },
  {
    name: "David Park",
    role: "Community Outreach Director",
    image: "/images/team/david.jpg",
    bio: "Marketing professional dedicated to raising awareness about pet adoption and animal welfare.",
    specialties: ["Community Outreach", "Marketing", "Education"]
  }
]

const STATS = [
  { number: "5,000+", label: "Pets Rescued", icon: Heart },
  { number: "3,200+", label: "Successful Adoptions", icon: Users },
  { number: "500+", label: "Active Volunteers", icon: Award },
  { number: "15", label: "Years of Service", icon: Calendar },
]

const VALUES = [
  {
    icon: Heart,
    title: "Compassion",
    description: "Every animal deserves love, care, and a second chance at happiness."
  },
  {
    icon: Shield,
    title: "Integrity",
    description: "We operate with transparency, honesty, and ethical practices in all we do."
  },
  {
    icon: Users,
    title: "Community",
    description: "Building strong partnerships with adopters, volunteers, and local organizations."
  },
  {
    icon: Target,
    title: "Excellence",
    description: "Committed to the highest standards of animal care and adoption services."
  }
]

export default function AboutPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-purple-100 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="mb-4 bg-blue-100 text-blue-800">About PetAdopt</Badge>
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
              Connecting Hearts, Saving Lives
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              For over 15 years, we've been dedicated to rescuing, rehabilitating, and rehoming 
              animals in need. Our mission is to create lasting bonds between pets and families 
              while promoting responsible pet ownership.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/pets">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                  Find Your Pet
                </Button>
              </Link>
              <Link href="/volunteer">
                <Button variant="outline" size="lg">
                  Join Our Mission
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Mission</h2>
              <p className="text-lg text-gray-600 mb-6">
                To rescue, rehabilitate, and rehome animals in need while educating the community 
                about responsible pet ownership and the importance of spaying and neutering.
              </p>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Vision</h3>
              <p className="text-lg text-gray-600">
                A world where every pet has a loving home and no animal suffers from neglect, 
                abuse, or abandonment.
              </p>
            </div>
            <div className="relative">
              <div className="aspect-square bg-gray-200 rounded-lg flex items-center justify-center">
                <Heart className="h-24 w-24 text-gray-400" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Impact</h2>
            <p className="text-xl text-gray-600">Making a difference, one pet at a time</p>
          </div>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {STATS.map((stat, index) => {
              const Icon = stat.icon
              return (
                <div key={index} className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                    <Icon className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{stat.number}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Values</h2>
            <p className="text-xl text-gray-600">The principles that guide everything we do</p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {VALUES.map((value, index) => {
              const Icon = value.icon
              return (
                <Card key={index} className="text-center">
                  <CardHeader>
                    <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4 mx-auto">
                      <Icon className="h-6 w-6 text-blue-600" />
                    </div>
                    <CardTitle>{value.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">{value.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Team */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Meet Our Team</h2>
            <p className="text-xl text-gray-600">Dedicated professionals working to save lives</p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {TEAM_MEMBERS.map((member, index) => (
              <Card key={index} className="overflow-hidden">
                <div className="aspect-square bg-gray-200 flex items-center justify-center">
                  <Users className="h-16 w-16 text-gray-400" />
                </div>
                <CardHeader>
                  <CardTitle className="text-lg">{member.name}</CardTitle>
                  <CardDescription className="text-blue-600 font-medium">
                    {member.role}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">{member.bio}</p>
                  <div className="flex flex-wrap gap-1">
                    {member.specialties.map((specialty, idx) => (
                      <Badge key={idx} variant="secondary" className="text-xs">
                        {specialty}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* History */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Story</h2>
              <p className="text-xl text-gray-600">How we started and where we're going</p>
            </div>
            
            <div className="space-y-12">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <Calendar className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">2009 - The Beginning</h3>
                  <p className="text-gray-600">
                    Founded by Dr. Sarah Johnson with a simple mission: to provide a safe haven 
                    for abandoned and neglected animals in our community.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">2015 - Expansion</h3>
                  <p className="text-gray-600">
                    Opened our second facility and launched our foster program, allowing us to 
                    care for more animals and provide specialized care for those with special needs.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <Globe className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">2020 - Digital Innovation</h3>
                  <p className="text-gray-600">
                    Launched our comprehensive online platform, making it easier for families 
                    to find their perfect companion and for volunteers to get involved.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                  <Star className="h-6 w-6 text-yellow-600" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Today - Leading the Way</h3>
                  <p className="text-gray-600">
                    Recognized as a leader in animal welfare, we continue to innovate and expand 
                    our services to help more animals find loving homes.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Info */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Visit Us</h2>
              <p className="text-xl text-gray-600">Come meet our amazing animals and team</p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-8">
              <Card>
                <CardHeader>
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4">
                    <MapPin className="h-6 w-6 text-blue-600" />
                  </div>
                  <CardTitle>Location</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    123 Pet Adoption Lane<br />
                    Compassion City, CC 12345
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mb-4">
                    <Phone className="h-6 w-6 text-green-600" />
                  </div>
                  <CardTitle>Phone</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    (555) 123-PETS<br />
                    Emergency: (555) 911-HELP
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mb-4">
                    <Mail className="h-6 w-6 text-purple-600" />
                  </div>
                  <CardTitle>Email</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    <EMAIL><br />
                    <EMAIL>
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Make a Difference?</h2>
          <p className="text-xl mb-8 opacity-90">
            Join our mission to save lives and create lasting bonds between pets and families.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/pets">
              <Button size="lg" variant="secondary">
                Adopt a Pet
              </Button>
            </Link>
            <Link href="/volunteer">
              <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-blue-600">
                Volunteer
              </Button>
            </Link>
            <Link href="/donate">
              <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-blue-600">
                Donate
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}

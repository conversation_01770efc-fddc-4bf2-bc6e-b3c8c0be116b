"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  ArrowLeft, 
  Calendar, 
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText,
  MessageSquare,
  User,
  Home,
  Phone,
  Mail,
  Heart,
  PawPrint,
  Users,
  Briefcase,
  Shield
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { toast } from "react-hot-toast"

interface ApplicationDetails {
  id: string
  status: string
  submittedAt: string
  reviewedAt?: string
  approvedAt?: string
  staffNotes?: string
  rejectionReason?: string
  
  // Personal Information
  firstName: string
  lastName: string
  email: string
  phone?: string
  
  // Housing Information
  housingType?: string
  ownRent?: string
  landlordContact?: string
  hasYard?: boolean
  yardFenced?: boolean
  
  // Experience and Lifestyle
  petExperience?: string
  currentPets?: string
  veterinarianInfo?: string
  
  // Employment
  employmentStatus?: string
  
  // References
  references?: Array<{
    name: string
    relationship: string
    phone: string
    email: string
  }>
  
  pet: {
    id: string
    name: string
    species: string
    breed: string
    age: number
    gender: string
    size: string
    color: string
    description: string
    photos: Array<{ url: string; isPrimary: boolean }>
    organization: {
      id: string
      name: string
      city: string
      state: string
      phone: string
      email: string
    }
  }
  
  appointments: Array<{
    id: string
    type: string
    scheduledDate: string
    status: string
    notes?: string
  }>
  
  timeline: Array<{
    id: string
    status: string
    timestamp: string
    notes?: string
    staffMember?: string
  }>
}

export default function ApplicationDetailsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const [application, setApplication] = useState<ApplicationDetails | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin")
      return
    }

    if (status === "authenticated" && params.id) {
      fetchApplicationDetails()
    }
  }, [status, router, params.id])

  const fetchApplicationDetails = async () => {
    try {
      const response = await fetch(`/api/applications/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setApplication(data.application)
      } else if (response.status === 404) {
        toast.error("Application not found")
        router.push("/dashboard/applications")
      } else {
        toast.error("Failed to load application details")
      }
    } catch (error) {
      console.error("Error fetching application details:", error)
      toast.error("Failed to load application details")
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved":
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case "rejected":
        return <XCircle className="h-5 w-5 text-red-500" />
      case "under_review":
      case "reference_check":
      case "home_visit_scheduled":
      case "home_visit_completed":
        return <Clock className="h-5 w-5 text-yellow-500" />
      case "waitlisted":
        return <AlertCircle className="h-5 w-5 text-orange-500" />
      default:
        return <FileText className="h-5 w-5 text-blue-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved":
        return "bg-green-100 text-green-800 border-green-200"
      case "rejected":
        return "bg-red-100 text-red-800 border-red-200"
      case "under_review":
      case "reference_check":
      case "home_visit_scheduled":
      case "home_visit_completed":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "waitlisted":
        return "bg-orange-100 text-orange-800 border-orange-200"
      default:
        return "bg-blue-100 text-blue-800 border-blue-200"
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatAge = (ageInMonths: number) => {
    if (ageInMonths < 12) {
      return `${ageInMonths} month${ageInMonths !== 1 ? 's' : ''} old`
    } else {
      const years = Math.floor(ageInMonths / 12)
      const months = ageInMonths % 12
      if (months === 0) {
        return `${years} year${years !== 1 ? 's' : ''} old`
      } else {
        return `${years}y ${months}m old`
      }
    }
  }

  if (status === "loading" || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-200 rounded"></div>
              ))}
            </div>
            <div className="space-y-6">
              {[...Array(2)].map((_, i) => (
                <div key={i} className="h-48 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!application) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="text-center py-12">
            <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Application Not Found</h3>
            <p className="text-gray-600 mb-6">
              The application you're looking for doesn't exist or you don't have permission to view it.
            </p>
            <Link href="/dashboard/applications">
              <Button>Back to Applications</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Link href="/dashboard/applications" className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Applications
        </Link>
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Application for {application.pet.name}
            </h1>
            <p className="text-gray-600">
              Submitted on {formatDate(application.submittedAt)}
            </p>
          </div>
          <div className="mt-4 lg:mt-0 flex items-center">
            {getStatusIcon(application.status)}
            <Badge className={`ml-2 text-lg px-4 py-2 ${getStatusColor(application.status)}`}>
              {application.status.replace(/_/g, ' ')}
            </Badge>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Pet Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <PawPrint className="h-5 w-5 mr-2" />
                Pet Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-6">
                <div className="md:w-48 h-48 relative rounded-lg overflow-hidden">
                  {application.pet.photos.length > 0 ? (
                    <Image
                      src={application.pet.photos.find(p => p.isPrimary)?.url || application.pet.photos[0].url}
                      alt={application.pet.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                      <span className="text-4xl">🐾</span>
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold mb-2">{application.pet.name}</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Species:</span> {application.pet.species}
                    </div>
                    <div>
                      <span className="font-medium">Breed:</span> {application.pet.breed}
                    </div>
                    <div>
                      <span className="font-medium">Age:</span> {formatAge(application.pet.age)}
                    </div>
                    <div>
                      <span className="font-medium">Gender:</span> {application.pet.gender}
                    </div>
                    <div>
                      <span className="font-medium">Size:</span> {application.pet.size}
                    </div>
                    <div>
                      <span className="font-medium">Color:</span> {application.pet.color}
                    </div>
                  </div>
                  {application.pet.description && (
                    <div className="mt-4">
                      <span className="font-medium">Description:</span>
                      <p className="text-gray-700 mt-1">{application.pet.description}</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Personal Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                Personal Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="font-medium">Name:</span> {application.firstName} {application.lastName}
                </div>
                <div className="flex items-center">
                  <Mail className="h-4 w-4 mr-2 text-gray-500" />
                  {application.email}
                </div>
                {application.phone && (
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 mr-2 text-gray-500" />
                    {application.phone}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Housing Information */}
          {(application.housingType || application.hasYard !== undefined) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Home className="h-5 w-5 mr-2" />
                  Housing Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {application.housingType && (
                    <div>
                      <span className="font-medium">Housing Type:</span> {application.housingType}
                    </div>
                  )}
                  {application.ownRent && (
                    <div>
                      <span className="font-medium">Own/Rent:</span> {application.ownRent}
                    </div>
                  )}
                  {application.hasYard !== undefined && (
                    <div>
                      <span className="font-medium">Has Yard:</span> {application.hasYard ? 'Yes' : 'No'}
                    </div>
                  )}
                  {application.hasYard && application.yardFenced !== undefined && (
                    <div>
                      <span className="font-medium">Yard Fenced:</span> {application.yardFenced ? 'Yes' : 'No'}
                    </div>
                  )}
                </div>
                {application.landlordContact && (
                  <div className="mt-4">
                    <span className="font-medium">Landlord Contact:</span> {application.landlordContact}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Experience and Lifestyle */}
          {(application.petExperience || application.currentPets || application.veterinarianInfo) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Heart className="h-5 w-5 mr-2" />
                  Pet Experience & Lifestyle
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {application.petExperience && (
                  <div>
                    <span className="font-medium">Pet Experience:</span>
                    <p className="text-gray-700 mt-1">{application.petExperience}</p>
                  </div>
                )}
                {application.currentPets && (
                  <div>
                    <span className="font-medium">Current/Previous Pets:</span>
                    <p className="text-gray-700 mt-1">{application.currentPets}</p>
                  </div>
                )}
                {application.veterinarianInfo && (
                  <div>
                    <span className="font-medium">Veterinarian Information:</span>
                    <p className="text-gray-700 mt-1">{application.veterinarianInfo}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Employment Information */}
          {application.employmentStatus && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Briefcase className="h-5 w-5 mr-2" />
                  Employment Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <span className="font-medium">Work Schedule:</span> {application.employmentStatus}
                </div>
              </CardContent>
            </Card>
          )}

          {/* References */}
          {application.references && application.references.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  References
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {application.references.map((reference, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="font-medium">{reference.name}</div>
                      <div className="text-sm text-gray-600">{reference.relationship}</div>
                      <div className="flex items-center mt-2 space-x-4">
                        <div className="flex items-center text-sm">
                          <Phone className="h-3 w-3 mr-1 text-gray-500" />
                          {reference.phone}
                        </div>
                        <div className="flex items-center text-sm">
                          <Mail className="h-3 w-3 mr-1 text-gray-500" />
                          {reference.email}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status and Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Application Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                {getStatusIcon(application.status)}
                <div className="mt-2">
                  <Badge className={`text-lg px-4 py-2 ${getStatusColor(application.status)}`}>
                    {application.status.replace(/_/g, ' ')}
                  </Badge>
                </div>
              </div>
              
              {application.staffNotes && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-start">
                    <Shield className="h-4 w-4 text-blue-500 mr-2 mt-1" />
                    <div>
                      <p className="text-sm font-medium text-blue-900">Staff Notes</p>
                      <p className="text-sm text-blue-800 mt-1">{application.staffNotes}</p>
                    </div>
                  </div>
                </div>
              )}

              {application.rejectionReason && (
                <div className="bg-red-50 p-4 rounded-lg">
                  <div className="flex items-start">
                    <XCircle className="h-4 w-4 text-red-500 mr-2 mt-1" />
                    <div>
                      <p className="text-sm font-medium text-red-900">Rejection Reason</p>
                      <p className="text-sm text-red-800 mt-1">{application.rejectionReason}</p>
                    </div>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <Link href={`/pets/${application.pet.id}`}>
                  <Button className="w-full" variant="outline">
                    View Pet Profile
                  </Button>
                </Link>
                <Button className="w-full" variant="outline">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Contact Shelter
                </Button>
                {application.status === "APPROVED" && (
                  <Button className="w-full bg-green-600 hover:bg-green-700">
                    <Calendar className="h-4 w-4 mr-2" />
                    Schedule Pickup
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Organization Info */}
          <Card>
            <CardHeader>
              <CardTitle>Organization</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <h4 className="font-medium">{application.pet.organization.name}</h4>
                <p className="text-sm text-gray-600">
                  {application.pet.organization.city}, {application.pet.organization.state}
                </p>
                <div className="flex items-center text-sm">
                  <Phone className="h-3 w-3 mr-2 text-gray-500" />
                  {application.pet.organization.phone}
                </div>
                <div className="flex items-center text-sm">
                  <Mail className="h-3 w-3 mr-2 text-gray-500" />
                  {application.pet.organization.email}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Appointments */}
          {application.appointments && application.appointments.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Appointments
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {application.appointments.map((appointment) => (
                    <div key={appointment.id} className="p-3 border rounded-lg">
                      <div className="font-medium text-sm">
                        {appointment.type.replace(/_/g, ' ')}
                      </div>
                      <div className="text-sm text-gray-600">
                        {formatDate(appointment.scheduledDate)}
                      </div>
                      <Badge variant="outline" className="mt-1">
                        {appointment.status}
                      </Badge>
                      {appointment.notes && (
                        <p className="text-xs text-gray-600 mt-2">{appointment.notes}</p>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Timeline */}
          {application.timeline && application.timeline.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Application Timeline</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {application.timeline.map((event, index) => (
                    <div key={event.id} className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        {getStatusIcon(event.status)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium">
                          {event.status.replace(/_/g, ' ')}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatDate(event.timestamp)}
                        </p>
                        {event.notes && (
                          <p className="text-xs text-gray-600 mt-1">{event.notes}</p>
                        )}
                        {event.staffMember && (
                          <p className="text-xs text-gray-500">by {event.staffMember}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
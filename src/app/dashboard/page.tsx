"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  User, 
  FileText, 
  Heart, 
  Calendar, 
  Settings, 
  Upload,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { toast } from "react-hot-toast"

interface Application {
  id: string
  status: string
  submittedAt: string
  pet: {
    id: string
    name: string
    species: string
    breed: string
    photos: Array<{ url: string; isPrimary: boolean }>
  }
}

interface Favorite {
  id: string
  pet: {
    id: string
    name: string
    species: string
    breed: string
    age: number
    photos: Array<{ url: string; isPrimary: boolean }>
    organization: {
      city: string
      state: string
    }
  }
}

interface Appointment {
  id: string
  type: string
  scheduledDate: string
  status: string
  pet?: {
    name: string
  }
}

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [applications, setApplications] = useState<Application[]>([])
  const [favorites, setFavorites] = useState<Favorite[]>([])
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin")
      return
    }

    if (status === "authenticated") {
      fetchDashboardData()
    }
  }, [status, router])

  const fetchDashboardData = async () => {
    try {
      const [applicationsRes, favoritesRes, appointmentsRes] = await Promise.all([
        fetch("/api/applications"),
        fetch("/api/favorites"),
        fetch("/api/appointments"),
      ])

      if (applicationsRes.ok) {
        const applicationsData = await applicationsRes.json()
        setApplications(applicationsData.applications || [])
      }

      if (favoritesRes.ok) {
        const favoritesData = await favoritesRes.json()
        setFavorites(favoritesData.favorites || [])
      }

      if (appointmentsRes.ok) {
        const appointmentsData = await appointmentsRes.json()
        setAppointments(appointmentsData.appointments || [])
      }
    } catch (error) {
      console.error("Error fetching dashboard data:", error)
      toast.error("Failed to load dashboard data")
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "rejected":
        return <XCircle className="h-4 w-4 text-red-500" />
      case "under_review":
      case "reference_check":
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-blue-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved":
        return "bg-green-100 text-green-800"
      case "rejected":
        return "bg-red-100 text-red-800"
      case "under_review":
      case "reference_check":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-blue-100 text-blue-800"
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatAge = (ageInMonths: number) => {
    if (ageInMonths < 12) {
      return `${ageInMonths} month${ageInMonths !== 1 ? 's' : ''} old`
    } else {
      const years = Math.floor(ageInMonths / 12)
      const months = ageInMonths % 12
      if (months === 0) {
        return `${years} year${years !== 1 ? 's' : ''} old`
      } else {
        return `${years}y ${months}m old`
      }
    }
  }

  if (status === "loading" || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Welcome back, {session?.user?.name}!
        </h1>
        <p className="text-gray-600">
          Manage your adoption applications, favorites, and profile.
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-2xl font-bold">{applications.length}</p>
                <p className="text-sm text-gray-600">Applications</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Heart className="h-8 w-8 text-red-500" />
              <div className="ml-4">
                <p className="text-2xl font-bold">{favorites.length}</p>
                <p className="text-sm text-gray-600">Favorites</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-green-500" />
              <div className="ml-4">
                <p className="text-2xl font-bold">{appointments.length}</p>
                <p className="text-sm text-gray-600">Appointments</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <User className="h-8 w-8 text-purple-500" />
              <div className="ml-4">
                <p className="text-sm font-medium">Profile</p>
                <Link href="/dashboard/profile">
                  <Button variant="outline" size="sm" className="mt-1">
                    <Settings className="h-3 w-3 mr-1" />
                    Edit
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Applications */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Recent Applications</CardTitle>
              <Link href="/dashboard/applications">
                <Button variant="outline" size="sm">View All</Button>
              </Link>
            </div>
            <CardDescription>
              Track the status of your adoption applications
            </CardDescription>
          </CardHeader>
          <CardContent>
            {applications.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">No applications yet</p>
                <Link href="/pets">
                  <Button>Browse Pets</Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {applications.slice(0, 3).map((application) => (
                  <div key={application.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                    <div className="relative w-16 h-16 rounded-lg overflow-hidden">
                      {application.pet.photos.length > 0 ? (
                        <Image
                          src={application.pet.photos.find(p => p.isPrimary)?.url || application.pet.photos[0].url}
                          alt={application.pet.name}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <span className="text-xl">🐾</span>
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold">{application.pet.name}</h4>
                      <p className="text-sm text-gray-600">
                        {application.pet.breed} • Applied {formatDate(application.submittedAt)}
                      </p>
                      <div className="flex items-center mt-2">
                        {getStatusIcon(application.status)}
                        <Badge className={`ml-2 ${getStatusColor(application.status)}`}>
                          {application.status.replace('_', ' ')}
                        </Badge>
                      </div>
                    </div>
                    <Link href={`/dashboard/applications/${application.id}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                    </Link>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Favorite Pets */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Favorite Pets</CardTitle>
              <Link href="/dashboard/favorites">
                <Button variant="outline" size="sm">View All</Button>
              </Link>
            </div>
            <CardDescription>
              Pets you've saved for later
            </CardDescription>
          </CardHeader>
          <CardContent>
            {favorites.length === 0 ? (
              <div className="text-center py-8">
                <Heart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">No favorites yet</p>
                <Link href="/pets">
                  <Button>Browse Pets</Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {favorites.slice(0, 3).map((favorite) => (
                  <div key={favorite.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                    <div className="relative w-16 h-16 rounded-lg overflow-hidden">
                      {favorite.pet.photos.length > 0 ? (
                        <Image
                          src={favorite.pet.photos.find(p => p.isPrimary)?.url || favorite.pet.photos[0].url}
                          alt={favorite.pet.name}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <span className="text-xl">🐾</span>
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold">{favorite.pet.name}</h4>
                      <p className="text-sm text-gray-600">
                        {favorite.pet.breed} • {formatAge(favorite.pet.age)}
                      </p>
                      <p className="text-xs text-gray-500">
                        {favorite.pet.organization.city}, {favorite.pet.organization.state}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <Link href={`/pets/${favorite.pet.id}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="h-3 w-3 mr-1" />
                          View
                        </Button>
                      </Link>
                      <Link href={`/apply/${favorite.pet.id}`}>
                        <Button size="sm">Apply</Button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

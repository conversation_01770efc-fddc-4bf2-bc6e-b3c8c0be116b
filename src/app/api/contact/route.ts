import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"

const contactSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Valid email is required"),
  phone: z.string().optional(),
  subject: z.string().min(1, "Subject is required"),
  message: z.string().min(10, "Message must be at least 10 characters"),
  inquiryType: z.string().min(1, "Please select an inquiry type"),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = contactSchema.parse(body)

    // In a real application, you would:
    // 1. Save the contact form submission to the database
    // 2. Send an email notification to staff
    // 3. Send a confirmation email to the user

    // For now, we'll just log the submission and return success
    console.log("Contact form submission:", validatedData)

    // Simulate email sending
    const emailSent = await sendContactEmail(validatedData)
    
    if (!emailSent) {
      return NextResponse.json(
        { error: "Failed to send email notification" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: "Contact form submitted successfully",
      submissionId: generateSubmissionId(),
    }, { status: 201 })

  } catch (error) {
    console.error("Error processing contact form:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid form data", details: error.errors },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: "Failed to process contact form" },
      { status: 500 }
    )
  }
}

// Simulate email sending function
async function sendContactEmail(data: any): Promise<boolean> {
  try {
    // In a real application, you would use an email service like:
    // - SendGrid
    // - Mailgun
    // - AWS SES
    // - Resend
    
    // Example with a hypothetical email service:
    /*
    await emailService.send({
      to: "<EMAIL>",
      subject: `New Contact Form: ${data.subject}`,
      html: `
        <h2>New Contact Form Submission</h2>
        <p><strong>Name:</strong> ${data.name}</p>
        <p><strong>Email:</strong> ${data.email}</p>
        <p><strong>Phone:</strong> ${data.phone || 'Not provided'}</p>
        <p><strong>Inquiry Type:</strong> ${data.inquiryType}</p>
        <p><strong>Subject:</strong> ${data.subject}</p>
        <p><strong>Message:</strong></p>
        <p>${data.message}</p>
      `
    })

    // Send confirmation email to user
    await emailService.send({
      to: data.email,
      subject: "Thank you for contacting PetAdopt",
      html: `
        <h2>Thank you for your message!</h2>
        <p>Hi ${data.name},</p>
        <p>We've received your message and will get back to you within 24 hours.</p>
        <p><strong>Your message:</strong></p>
        <p>${data.message}</p>
        <p>Best regards,<br>The PetAdopt Team</p>
      `
    })
    */

    // For demo purposes, we'll just return true
    return true
  } catch (error) {
    console.error("Error sending email:", error)
    return false
  }
}

// Generate a unique submission ID
function generateSubmissionId(): string {
  return `contact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { UserRole, ContractStatus, ContractType } from "@prisma/client"
import { z } from "zod"
import crypto from "crypto"

const contractSchema = z.object({
  type: z.nativeEnum(ContractType),
  applicationId: z.string().min(1, "Application ID is required"),
  terms: z.string().min(1, "Contract terms are required"),
  adoptionFee: z.number().min(0).optional(),
  specialConditions: z.string().optional(),
  expiresAt: z.string().optional(),
})

const signatureSchema = z.object({
  contractId: z.string().min(1, "Contract ID is required"),
  signature: z.string().min(1, "Signature is required"),
  signedAt: z.string().min(1, "Signature date is required"),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "20")
    const status = searchParams.get("status")
    const type = searchParams.get("type")
    const applicationId = searchParams.get("applicationId")
    const skip = (page - 1) * limit

    let where: any = {}

    // Regular users can only see their own contracts
    if (![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role)) {
      where.application = {
        userId: session.user.id
      }
    }

    if (status) where.status = status as ContractStatus
    if (type) where.type = type as ContractType
    if (applicationId) where.applicationId = applicationId

    const [contracts, total] = await Promise.all([
      prisma.contract.findMany({
        where,
        include: {
          application: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true,
                }
              },
              pet: {
                select: {
                  name: true,
                  species: true,
                  breed: true,
                }
              }
            }
          },
          signatures: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true,
                  role: true,
                }
              }
            },
            orderBy: { signedAt: "desc" }
          }
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.contract.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      contracts,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }
    })

  } catch (error) {
    console.error("Error fetching contracts:", error)
    return NextResponse.json(
      { error: "Failed to fetch contracts" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action, ...data } = body

    if (action === "create_contract") {
      // Check if user has permission to create contracts
      if (![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role)) {
        return NextResponse.json(
          { error: "Insufficient permissions" },
          { status: 403 }
        )
      }

      const validatedData = contractSchema.parse(data)

      // Check if application exists and is approved
      const application = await prisma.application.findUnique({
        where: { id: validatedData.applicationId },
        include: {
          user: {
            select: {
              name: true,
              email: true,
            }
          },
          pet: {
            select: {
              name: true,
              species: true,
              breed: true,
              adoptionFee: true,
            }
          }
        }
      })

      if (!application) {
        return NextResponse.json(
          { error: "Application not found" },
          { status: 404 }
        )
      }

      if (application.status !== "APPROVED") {
        return NextResponse.json(
          { error: "Application must be approved before creating contract" },
          { status: 400 }
        )
      }

      // Check if contract already exists for this application
      const existingContract = await prisma.contract.findFirst({
        where: { applicationId: validatedData.applicationId }
      })

      if (existingContract) {
        return NextResponse.json(
          { error: "Contract already exists for this application" },
          { status: 400 }
        )
      }

      // Generate contract hash for integrity verification
      const contractHash = crypto
        .createHash('sha256')
        .update(JSON.stringify({
          type: validatedData.type,
          applicationId: validatedData.applicationId,
          terms: validatedData.terms,
          adoptionFee: validatedData.adoptionFee || application.pet.adoptionFee,
          specialConditions: validatedData.specialConditions,
        }))
        .digest('hex')

      const contract = await prisma.contract.create({
        data: {
          ...validatedData,
          adoptionFee: validatedData.adoptionFee || application.pet.adoptionFee,
          expiresAt: validatedData.expiresAt ? new Date(validatedData.expiresAt) : null,
          contractHash,
          status: ContractStatus.PENDING,
          createdById: session.user.id,
        },
        include: {
          application: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true,
                }
              },
              pet: {
                select: {
                  name: true,
                  species: true,
                  breed: true,
                }
              }
            }
          }
        }
      })

      // TODO: Send email notification to adopter
      // await EmailService.sendContractNotification(application.user.email, contract)

      return NextResponse.json({
        message: "Contract created successfully",
        contract
      }, { status: 201 })

    } else if (action === "sign_contract") {
      const validatedData = signatureSchema.parse(data)

      // Check if contract exists
      const contract = await prisma.contract.findUnique({
        where: { id: validatedData.contractId },
        include: {
          application: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                }
              }
            }
          },
          signatures: true
        }
      })

      if (!contract) {
        return NextResponse.json(
          { error: "Contract not found" },
          { status: 404 }
        )
      }

      // Check if contract is still valid
      if (contract.status !== ContractStatus.PENDING) {
        return NextResponse.json(
          { error: "Contract is not available for signing" },
          { status: 400 }
        )
      }

      if (contract.expiresAt && contract.expiresAt < new Date()) {
        return NextResponse.json(
          { error: "Contract has expired" },
          { status: 400 }
        )
      }

      // Check if user has permission to sign this contract
      const canSign = session.user.role === UserRole.ADMIN ||
                     session.user.role === UserRole.STAFF ||
                     contract.application.user.id === session.user.id

      if (!canSign) {
        return NextResponse.json(
          { error: "You don't have permission to sign this contract" },
          { status: 403 }
        )
      }

      // Check if user has already signed
      const existingSignature = contract.signatures.find(
        sig => sig.userId === session.user.id
      )

      if (existingSignature) {
        return NextResponse.json(
          { error: "You have already signed this contract" },
          { status: 400 }
        )
      }

      // Get client IP and user agent for audit trail
      const ipAddress = request.headers.get('x-forwarded-for') || 
                       request.headers.get('x-real-ip') || 
                       'unknown'
      const userAgent = request.headers.get('user-agent') || 'unknown'

      // Create signature
      const signature = await prisma.contractSignature.create({
        data: {
          contractId: validatedData.contractId,
          userId: session.user.id,
          signature: validatedData.signature,
          signedAt: new Date(validatedData.signedAt),
          ipAddress,
          userAgent,
        },
        include: {
          user: {
            select: {
              name: true,
              email: true,
              role: true,
            }
          }
        }
      })

      // Check if all required signatures are collected
      const updatedContract = await prisma.contract.findUnique({
        where: { id: validatedData.contractId },
        include: {
          signatures: {
            include: {
              user: {
                select: {
                  role: true,
                }
              }
            }
          }
        }
      })

      const hasAdopterSignature = updatedContract!.signatures.some(
        sig => sig.user.role === UserRole.USER
      )
      const hasStaffSignature = updatedContract!.signatures.some(
        sig => [UserRole.STAFF, UserRole.ADMIN].includes(sig.user.role)
      )

      // Update contract status if fully signed
      if (hasAdopterSignature && hasStaffSignature) {
        await prisma.contract.update({
          where: { id: validatedData.contractId },
          data: { 
            status: ContractStatus.SIGNED,
            completedAt: new Date()
          }
        })

        // Update application status to completed
        await prisma.application.update({
          where: { id: contract.applicationId },
          data: { status: "COMPLETED" }
        })

        // Update pet status to adopted
        await prisma.pet.update({
          where: { id: contract.application.petId },
          data: { status: "ADOPTED" }
        })

        // TODO: Send completion notifications
        // await EmailService.sendAdoptionCompletionNotification(contract)
      }

      return NextResponse.json({
        message: "Contract signed successfully",
        signature,
        contractStatus: hasAdopterSignature && hasStaffSignature ? "SIGNED" : "PENDING"
      }, { status: 201 })

    } else {
      return NextResponse.json(
        { error: "Invalid action" },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error("Error processing contract:", error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: "Failed to process contract" },
      { status: 500 }
    )
  }
}

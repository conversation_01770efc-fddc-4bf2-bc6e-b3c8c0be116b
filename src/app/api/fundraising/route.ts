import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { UserRole, CampaignStatus, CampaignType } from "@prisma/client"
import { z } from "zod"

const campaignSchema = z.object({
  title: z.string().min(1, "Campaign title is required"),
  description: z.string().min(1, "Campaign description is required"),
  type: z.nativeEnum(CampaignType),
  goalAmount: z.number().min(1, "Goal amount must be positive"),
  startDate: z.string().min(1, "Start date is required"),
  endDate: z.string().min(1, "End date is required"),
  petId: z.string().optional(),
  imageUrl: z.string().optional(),
  story: z.string().optional(),
  urgency: z.enum(["LOW", "MEDIUM", "HIGH", "CRITICAL"]).default("MEDIUM"),
  isPublic: z.boolean().default(true),
})

const donationSchema = z.object({
  campaignId: z.string().min(1, "Campaign ID is required"),
  amount: z.number().min(1, "Donation amount must be positive"),
  isAnonymous: z.boolean().default(false),
  donorName: z.string().optional(),
  donorEmail: z.string().email().optional(),
  message: z.string().optional(),
  dedicatedTo: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "20")
    const status = searchParams.get("status")
    const type = searchParams.get("type")
    const urgency = searchParams.get("urgency")
    const active = searchParams.get("active") === "true"
    const skip = (page - 1) * limit

    let where: any = {}

    const session = await getServerSession(authOptions)
    
    // Non-authenticated users can only see public campaigns
    if (!session?.user) {
      where.isPublic = true
      where.status = CampaignStatus.ACTIVE
    } else if (![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role)) {
      // Regular users can see public campaigns and their own
      where.OR = [
        { isPublic: true },
        { createdById: session.user.id }
      ]
    }

    if (status) where.status = status as CampaignStatus
    if (type) where.type = type as CampaignType
    if (urgency) where.urgency = urgency
    if (active) {
      where.status = CampaignStatus.ACTIVE
      where.endDate = { gte: new Date() }
    }

    const [campaigns, total] = await Promise.all([
      prisma.campaign.findMany({
        where,
        include: {
          pet: {
            select: {
              name: true,
              species: true,
              breed: true,
              photos: {
                where: { isPrimary: true },
                take: 1,
                select: { url: true }
              }
            }
          },
          organization: {
            select: {
              name: true,
              city: true,
              state: true,
            }
          },
          _count: {
            select: {
              donations: true,
            }
          },
          donations: {
            select: {
              amount: true,
            }
          }
        },
        orderBy: [
          { urgency: "desc" },
          { createdAt: "desc" }
        ],
        skip,
        take: limit,
      }),
      prisma.campaign.count({ where })
    ])

    // Calculate raised amounts and progress
    const campaignsWithProgress = campaigns.map(campaign => {
      const raisedAmount = campaign.donations.reduce((sum, donation) => sum + donation.amount, 0)
      const progress = Math.min((raisedAmount / campaign.goalAmount) * 100, 100)
      const daysLeft = Math.max(0, Math.ceil((campaign.endDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24)))
      
      return {
        ...campaign,
        raisedAmount,
        progress,
        daysLeft,
        donorCount: campaign._count.donations,
        donations: undefined, // Remove individual donations from response
      }
    })

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      campaigns: campaignsWithProgress,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }
    })

  } catch (error) {
    console.error("Error fetching campaigns:", error)
    return NextResponse.json(
      { error: "Failed to fetch campaigns" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action, ...data } = body

    if (action === "create_campaign") {
      // Check if user has permission to create campaigns
      if (![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role)) {
        return NextResponse.json(
          { error: "Insufficient permissions" },
          { status: 403 }
        )
      }

      const validatedData = campaignSchema.parse(data)

      // Validate dates
      const startDate = new Date(validatedData.startDate)
      const endDate = new Date(validatedData.endDate)

      if (endDate <= startDate) {
        return NextResponse.json(
          { error: "End date must be after start date" },
          { status: 400 }
        )
      }

      if (startDate < new Date()) {
        return NextResponse.json(
          { error: "Start date cannot be in the past" },
          { status: 400 }
        )
      }

      // Validate pet if specified
      if (validatedData.petId) {
        const pet = await prisma.pet.findUnique({
          where: { id: validatedData.petId }
        })
        if (!pet) {
          return NextResponse.json(
            { error: "Pet not found" },
            { status: 404 }
          )
        }
      }

      // Get user's organization
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { organizationId: true }
      })

      const campaign = await prisma.campaign.create({
        data: {
          ...validatedData,
          startDate,
          endDate,
          status: startDate <= new Date() ? CampaignStatus.ACTIVE : CampaignStatus.DRAFT,
          organizationId: user?.organizationId || "default",
          createdById: session.user.id,
        },
        include: {
          pet: {
            select: {
              name: true,
              species: true,
              breed: true,
            }
          },
          organization: {
            select: {
              name: true,
              city: true,
              state: true,
            }
          }
        }
      })

      return NextResponse.json({
        message: "Campaign created successfully",
        campaign
      }, { status: 201 })

    } else if (action === "donate") {
      const validatedData = donationSchema.parse(data)

      // Check if campaign exists and is active
      const campaign = await prisma.campaign.findUnique({
        where: { id: validatedData.campaignId },
        include: {
          donations: {
            select: { amount: true }
          }
        }
      })

      if (!campaign) {
        return NextResponse.json(
          { error: "Campaign not found" },
          { status: 404 }
        )
      }

      if (campaign.status !== CampaignStatus.ACTIVE) {
        return NextResponse.json(
          { error: "Campaign is not active" },
          { status: 400 }
        )
      }

      if (campaign.endDate < new Date()) {
        return NextResponse.json(
          { error: "Campaign has ended" },
          { status: 400 }
        )
      }

      // Check if campaign has reached its goal
      const currentRaised = campaign.donations.reduce((sum, d) => sum + d.amount, 0)
      if (currentRaised >= campaign.goalAmount) {
        return NextResponse.json(
          { error: "Campaign has already reached its goal" },
          { status: 400 }
        )
      }

      // Create donation record
      const donation = await prisma.donation.create({
        data: {
          amount: validatedData.amount,
          isAnonymous: validatedData.isAnonymous,
          donorName: validatedData.donorName || (session.user?.name || null),
          donorEmail: validatedData.donorEmail || (session.user?.email || null),
          message: validatedData.message,
          dedicatedTo: validatedData.dedicatedTo,
          campaignId: validatedData.campaignId,
          userId: session.user.id,
          status: "PENDING", // Will be updated by payment webhook
        }
      })

      // Create Stripe payment intent
      const paymentIntent = await fetch("/api/payments/create-intent", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          amount: validatedData.amount,
          metadata: {
            donationId: donation.id,
            campaignId: validatedData.campaignId,
            type: "campaign_donation"
          }
        })
      })

      if (!paymentIntent.ok) {
        // Clean up donation record if payment intent creation fails
        await prisma.donation.delete({ where: { id: donation.id } })
        return NextResponse.json(
          { error: "Failed to create payment intent" },
          { status: 500 }
        )
      }

      const paymentData = await paymentIntent.json()

      return NextResponse.json({
        message: "Donation initiated successfully",
        donation,
        clientSecret: paymentData.clientSecret
      }, { status: 201 })

    } else if (action === "update_status") {
      // Check if user has permission to update campaigns
      if (![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role)) {
        return NextResponse.json(
          { error: "Insufficient permissions" },
          { status: 403 }
        )
      }

      const { campaignId, status } = data

      if (!campaignId || !status) {
        return NextResponse.json(
          { error: "Campaign ID and status are required" },
          { status: 400 }
        )
      }

      const campaign = await prisma.campaign.update({
        where: { id: campaignId },
        data: { status: status as CampaignStatus },
        include: {
          _count: {
            select: { donations: true }
          },
          donations: {
            select: { amount: true }
          }
        }
      })

      const raisedAmount = campaign.donations.reduce((sum, d) => sum + d.amount, 0)

      return NextResponse.json({
        message: "Campaign status updated successfully",
        campaign: {
          ...campaign,
          raisedAmount,
          donorCount: campaign._count.donations,
          donations: undefined
        }
      })

    } else {
      return NextResponse.json(
        { error: "Invalid action" },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error("Error processing fundraising request:", error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 }
    )
  }
}

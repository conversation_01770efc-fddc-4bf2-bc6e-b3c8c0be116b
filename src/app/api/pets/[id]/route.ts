import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { petSchema } from "@/lib/validations"
import { UserRole } from "@prisma/client"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const pet = await prisma.pet.findUnique({
      where: { id },
      include: {
        photos: {
          orderBy: { order: "asc" }
        },
        videos: true,
        medicalRecords: {
          orderBy: { date: "desc" },
          take: 5,
        },
        behavioralAssessments: {
          orderBy: { assessmentDate: "desc" },
          take: 1,
        },
        organization: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            address: true,
            city: true,
            state: true,
            zipCode: true,
          }
        },
        facility: {
          select: {
            name: true,
            address: true,
            city: true,
            state: true,
            phone: true,
          }
        },
        sponsorships: {
          where: { isActive: true },
          select: {
            sponsorName: true,
            amount: true,
            isAnonymous: true,
          }
        },
        _count: {
          select: {
            favorites: true,
            applications: true,
          }
        }
      }
    })
    
    if (!pet) {
      return NextResponse.json(
        { error: "Pet not found" },
        { status: 404 }
      )
    }
    
    return NextResponse.json({ pet })
    
  } catch (error) {
    console.error("Error fetching pet:", error)
    return NextResponse.json(
      { error: "Failed to fetch pet" },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if user has permission to update pets
    if (![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const body = await request.json()

    // Validate input
    const validatedData = petSchema.partial().parse(body)

    const { id } = await params

    // Check if pet exists
    const existingPet = await prisma.pet.findUnique({
      where: { id }
    })
    
    if (!existingPet) {
      return NextResponse.json(
        { error: "Pet not found" },
        { status: 404 }
      )
    }
    
    // Update search keywords if name, breed, or other searchable fields changed
    const searchKeywords = [
      validatedData.name || existingPet.name,
      validatedData.species || existingPet.species,
      validatedData.breed || existingPet.breed,
      validatedData.color || existingPet.color,
      ...(validatedData.personalityTraits || existingPet.personalityTraits),
    ].filter(Boolean) as string[]
    
    // Update pet
    const pet = await prisma.pet.update({
      where: { id },
      data: {
        ...validatedData,
        searchKeywords,
        updatedAt: new Date(),
      },
      include: {
        photos: {
          orderBy: { order: "asc" }
        },
        organization: {
          select: {
            name: true,
            city: true,
            state: true,
          }
        }
      }
    })
    
    return NextResponse.json({
      message: "Pet updated successfully",
      pet
    })
    
  } catch (error) {
    console.error("Error updating pet:", error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: "Failed to update pet" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if user has permission to delete pets
    if (![UserRole.ADMIN].includes(session.user.role)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const { id } = await params

    // Check if pet exists
    const existingPet = await prisma.pet.findUnique({
      where: { id }
    })

    if (!existingPet) {
      return NextResponse.json(
        { error: "Pet not found" },
        { status: 404 }
      )
    }

    // Delete pet (this will cascade delete related records)
    await prisma.pet.delete({
      where: { id }
    })
    
    return NextResponse.json({
      message: "Pet deleted successfully"
    })
    
  } catch (error) {
    console.error("Error deleting pet:", error)
    return NextResponse.json(
      { error: "Failed to delete pet" },
      { status: 500 }
    )
  }
}

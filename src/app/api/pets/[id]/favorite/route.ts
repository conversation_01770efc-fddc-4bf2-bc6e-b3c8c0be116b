import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { id } = await params

    // Check if pet exists
    const pet = await prisma.pet.findUnique({
      where: { id },
      select: { id: true, name: true }
    })

    if (!pet) {
      return NextResponse.json(
        { error: "Pet not found" },
        { status: 404 }
      )
    }

    // Check if already favorited
    const existingFavorite = await prisma.favorite.findUnique({
      where: {
        userId_petId: {
          userId: session.user.id,
          petId: id
        }
      }
    })

    if (existingFavorite) {
      return NextResponse.json(
        { error: "Pet already in favorites" },
        { status: 400 }
      )
    }

    // Add to favorites
    const favorite = await prisma.favorite.create({
      data: {
        userId: session.user.id,
        petId: id
      },
      include: {
        pet: {
          select: {
            id: true,
            name: true,
            species: true,
            breed: true
          }
        }
      }
    })

    return NextResponse.json({
      message: "Pet added to favorites",
      favorite
    }, { status: 201 })

  } catch (error) {
    console.error("Error adding favorite:", error)
    return NextResponse.json(
      { error: "Failed to add favorite" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { id } = await params

    // Find and delete the favorite
    const favorite = await prisma.favorite.findUnique({
      where: {
        userId_petId: {
          userId: session.user.id,
          petId: id
        }
      }
    })

    if (!favorite) {
      return NextResponse.json(
        { error: "Favorite not found" },
        { status: 404 }
      )
    }

    await prisma.favorite.delete({
      where: {
        userId_petId: {
          userId: session.user.id,
          petId: id
        }
      }
    })

    return NextResponse.json({
      message: "Pet removed from favorites"
    })

  } catch (error) {
    console.error("Error removing favorite:", error)
    return NextResponse.json(
      { error: "Failed to remove favorite" },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { behavioralAssessmentSchema } from "@/lib/validations"
import { UserRole } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const petId = searchParams.get("petId")
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "20")
    const skip = (page - 1) * limit

    let where: any = {}
    if (petId) where.petId = petId

    const [assessments, total] = await Promise.all([
      prisma.behavioralAssessment.findMany({
        where,
        include: {
          pet: {
            select: {
              name: true,
              species: true,
              breed: true,
            }
          }
        },
        orderBy: { assessmentDate: "desc" },
        skip,
        take: limit,
      }),
      prisma.behavioralAssessment.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      assessments,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }
    })

  } catch (error) {
    console.error("Error fetching behavioral assessments:", error)
    return NextResponse.json(
      { error: "Failed to fetch behavioral assessments" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if user has permission to create behavioral assessments
    if (![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = behavioralAssessmentSchema.parse(body)

    // Check if pet exists
    const pet = await prisma.pet.findUnique({
      where: { id: validatedData.petId },
      select: { id: true, name: true }
    })

    if (!pet) {
      return NextResponse.json(
        { error: "Pet not found" },
        { status: 404 }
      )
    }

    const assessment = await prisma.behavioralAssessment.create({
      data: {
        ...validatedData,
        assessmentDate: new Date(validatedData.assessmentDate),
      },
      include: {
        pet: {
          select: {
            name: true,
            species: true,
            breed: true,
          }
        }
      }
    })

    return NextResponse.json({
      message: "Behavioral assessment created successfully",
      assessment
    }, { status: 201 })

  } catch (error) {
    console.error("Error creating behavioral assessment:", error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: "Failed to create behavioral assessment" },
      { status: 500 }
    )
  }
}

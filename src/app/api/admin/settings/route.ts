import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { UserRole } from "@prisma/client"

// Mock settings data - in a real application, this would be stored in the database
const defaultSettings = {
  organizationName: "Pet Adoption Center",
  organizationDescription: "Helping pets find their forever homes",
  contactEmail: "<EMAIL>",
  contactPhone: "(*************",
  address: "123 Pet Street",
  city: "Pet City",
  state: "CA",
  zipCode: "12345",
  website: "https://petadoption.org",
  socialMedia: {
    facebook: "https://facebook.com/petadoption",
    twitter: "https://twitter.com/petadoption",
    instagram: "https://instagram.com/petadoption"
  },
  emailSettings: {
    smtpHost: "smtp.gmail.com",
    smtpPort: 587,
    smtpUser: "",
    smtpPassword: "",
    fromEmail: "<EMAIL>",
    fromName: "Pet Adoption Center"
  },
  notificationSettings: {
    emailNotifications: true,
    smsNotifications: false,
    newApplicationAlert: true,
    adoptionAlert: true,
    donationAlert: true
  },
  adoptionSettings: {
    adoptionFeeRequired: true,
    homeVisitRequired: true,
    referenceCheckRequired: true,
    backgroundCheckRequired: false,
    adoptionContract: "Standard adoption contract template..."
  },
  donationSettings: {
    acceptDonations: true,
    recurringDonations: true,
    donationGoal: 5000,
    taxDeductible: true
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check permissions - only admins can view settings
    const userRole = session.user.role as UserRole
    if (userRole !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    // In a real application, you would fetch settings from the database
    // For now, return the default settings
    return NextResponse.json(defaultSettings)

  } catch (error) {
    console.error("Error fetching settings:", error)
    return NextResponse.json(
      { error: "Failed to fetch settings" },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check permissions - only admins can update settings
    const userRole = session.user.role as UserRole
    if (userRole !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const body = await request.json()

    // In a real application, you would validate and save the settings to the database
    // For now, just return success
    console.log("Settings updated:", body)

    return NextResponse.json({
      message: "Settings updated successfully",
      settings: body
    })

  } catch (error) {
    console.error("Error updating settings:", error)
    return NextResponse.json(
      { error: "Failed to update settings" },
      { status: 500 }
    )
  }
}
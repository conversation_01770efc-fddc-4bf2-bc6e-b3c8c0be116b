import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { UserRole } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || ![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role as UserRole)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search") || ""
    const messageType = searchParams.get("messageType") || ""
    const isRead = searchParams.get("isRead")

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.OR = [
        { subject: { contains: search, mode: "insensitive" } },
        { content: { contains: search, mode: "insensitive" } },
        { sender: { name: { contains: search, mode: "insensitive" } } },
        { sender: { email: { contains: search, mode: "insensitive" } } }
      ]
    }

    if (messageType) {
      where.messageType = messageType
    }

    if (isRead !== null && isRead !== undefined) {
      where.isRead = isRead === "true"
    }

    // Get messages with sender information
    const [messages, total] = await Promise.all([
      prisma.message.findMany({
        where,
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true
            }
          }
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit
      }),
      prisma.message.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      messages,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })
  } catch (error) {
    console.error("Error fetching messages:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || ![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role as UserRole)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const {
      recipientIds,
      subject,
      content,
      messageType,
      sendEmail,
      sendSMS,
      scheduledDate
    } = body

    // Validate required fields
    if (!content) {
      return NextResponse.json(
        { error: "Message content is required" },
        { status: 400 }
      )
    }

    if (!recipientIds || recipientIds.length === 0) {
      return NextResponse.json(
        { error: "At least one recipient is required" },
        { status: 400 }
      )
    }

    // Verify recipients exist
    const recipients = await prisma.user.findMany({
      where: {
        id: {
          in: recipientIds
        }
      },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true
      }
    })

    if (recipients.length !== recipientIds.length) {
      return NextResponse.json(
        { error: "Some recipients were not found" },
        { status: 404 }
      )
    }

    // Create messages for each recipient
    const messages = await Promise.all(
      recipients.map(recipient =>
        prisma.message.create({
          data: {
            senderId: session.user.id,
            recipientId: recipient.id,
            subject: subject || null,
            content,
            messageType: messageType || "general",
            isRead: false
          },
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true
              }
            }
          }
        })
      )
    )

    // TODO: Implement actual email/SMS sending logic here
    if (sendEmail) {
      // Send emails to recipients
      console.log("Sending emails to:", recipients.map(r => r.email))
    }

    if (sendSMS) {
      // Send SMS to recipients with phone numbers
      const recipientsWithPhone = recipients.filter(r => r.phone)
      console.log("Sending SMS to:", recipientsWithPhone.map(r => r.phone))
    }

    return NextResponse.json({
      messages,
      sentCount: messages.length,
      emailSent: sendEmail,
      smsSent: sendSMS
    }, { status: 201 })
  } catch (error) {
    console.error("Error sending messages:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// Get users for recipient selection
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || ![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role as UserRole)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search") || ""
    const role = searchParams.get("role") || ""

    const where: any = {}
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } }
      ]
    }

    if (role) {
      where.role = role
    }

    const users = await prisma.user.findMany({
      where,
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        role: true,
        status: true
      },
      orderBy: { name: "asc" },
      take: 50 // Limit to prevent too many results
    })

    return NextResponse.json({ users })
  } catch (error) {
    console.error("Error fetching users:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || ![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role as UserRole)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { messageIds, markAsRead } = body

    if (!messageIds || messageIds.length === 0) {
      return NextResponse.json(
        { error: "Message IDs are required" },
        { status: 400 }
      )
    }

    const updatedMessages = await prisma.message.updateMany({
      where: {
        id: {
          in: messageIds
        }
      },
      data: {
        isRead: markAsRead,
        ...(markAsRead && { readAt: new Date() })
      }
    })

    return NextResponse.json({
      updatedCount: updatedMessages.count
    })
  } catch (error) {
    console.error("Error updating messages:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

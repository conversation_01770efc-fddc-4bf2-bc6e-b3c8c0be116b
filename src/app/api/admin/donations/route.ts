import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { UserRole, DonationType, PaymentStatus } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check permissions
    const userRole = session.user.role as UserRole
    if (![UserRole.STAFF, UserRole.ADMIN].includes(userRole)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search") || ""
    const type = searchParams.get("type") as DonationType | null
    const status = searchParams.get("status") as PaymentStatus | null
    const sortBy = searchParams.get("sortBy") || "createdAt"
    const sortOrder = searchParams.get("sortOrder") || "desc"

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.OR = [
        { donorName: { contains: search, mode: "insensitive" } },
        { donorEmail: { contains: search, mode: "insensitive" } },
        { user: { name: { contains: search, mode: "insensitive" } } },
        { user: { email: { contains: search, mode: "insensitive" } } },
        { designation: { contains: search, mode: "insensitive" } },
        { memorialName: { contains: search, mode: "insensitive" } },
      ]
    }

    if (type) {
      where.type = type
    }

    if (status) {
      where.paymentStatus = status
    }

    // Get donations with pagination
    const [donations, total] = await Promise.all([
      prisma.donation.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          }
        },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder
        }
      }),
      prisma.donation.count({ where })
    ])

    // Get statistics
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    
    const [
      totalStats,
      thisMonthStats,
      recurringStats
    ] = await Promise.all([
      prisma.donation.aggregate({
        _sum: { amount: true },
        _avg: { amount: true },
        _count: true,
        where: {
          paymentStatus: PaymentStatus.COMPLETED
        }
      }),
      prisma.donation.aggregate({
        _sum: { amount: true },
        where: {
          paymentStatus: PaymentStatus.COMPLETED,
          createdAt: { gte: startOfMonth }
        }
      }),
      prisma.donation.aggregate({
        _sum: { amount: true },
        where: {
          paymentStatus: PaymentStatus.COMPLETED,
          isRecurring: true
        }
      })
    ])

    const stats = {
      totalAmount: totalStats._sum.amount || 0,
      totalCount: totalStats._count || 0,
      averageAmount: totalStats._avg.amount || 0,
      thisMonthAmount: thisMonthStats._sum.amount || 0,
      recurringAmount: recurringStats._sum.amount || 0,
    }

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      donations,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      stats
    })

  } catch (error) {
    console.error("Error fetching donations:", error)
    return NextResponse.json(
      { error: "Failed to fetch donations" },
      { status: 500 }
    )
  }
}
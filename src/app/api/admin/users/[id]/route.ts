import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { UserRole, UserStatus } from "@prisma/client"
import { z } from "zod"
import bcrypt from "bcryptjs"
import { PermissionManager, Permission, canManageUser } from "@/lib/permissions"

const updateUserSchema = z.object({
  name: z.string().min(2).optional(),
  role: z.nativeEnum(UserRole).optional(),
  status: z.nativeEnum(UserStatus).optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  phone: z.string().optional(),
  password: z.string().min(8).optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check permissions
    const userRole = session.user.role as UserRole
    if (!PermissionManager.hasPermission(userRole, Permission.VIEW_USERS)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const userId = params.id

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        firstName: true,
        lastName: true,
        phone: true,
        role: true,
        status: true,
        image: true,
        createdAt: true,
        updatedAt: true,
        emailVerified: true,
        dateOfBirth: true,
        address: true,
        city: true,
        state: true,
        zipCode: true,
        country: true,
        emergencyContactName: true,
        emergencyContactPhone: true,
        emergencyContactRelation: true,
        _count: {
          select: {
            adoptionApplications: true,
            fosterApplications: true,
            volunteerApplications: true,
            favoritePets: true,
          }
        },
        adoptionApplications: {
          select: {
            id: true,
            status: true,
            submittedAt: true,
            pet: {
              select: {
                id: true,
                name: true,
                species: true,
                breed: true,
              }
            }
          },
          orderBy: { submittedAt: "desc" },
          take: 5
        },
        fosterApplications: {
          select: {
            id: true,
            status: true,
            submittedAt: true,
          },
          orderBy: { submittedAt: "desc" },
          take: 5
        },
        volunteerApplications: {
          select: {
            id: true,
            status: true,
            submittedAt: true,
          },
          orderBy: { submittedAt: "desc" },
          take: 5
        }
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({ user })

  } catch (error) {
    console.error("Error fetching user:", error)
    return NextResponse.json(
      { error: "Failed to fetch user" },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check permissions
    const userRole = session.user.role as UserRole
    if (!PermissionManager.hasPermission(userRole, Permission.EDIT_USERS)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const userId = params.id
    const body = await request.json()
    const validatedData = updateUserSchema.parse(body)

    // Get current user data
    const currentUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { role: true }
    })

    if (!currentUser) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Check if user can manage this user's current role
    if (!canManageUser(userRole, currentUser.role)) {
      return NextResponse.json(
        { error: "Cannot manage this user" },
        { status: 403 }
      )
    }

    // Check if user can assign the new role (if role is being changed)
    if (validatedData.role && !canManageUser(userRole, validatedData.role)) {
      return NextResponse.json(
        { error: "Cannot assign this role" },
        { status: 403 }
      )
    }

    // Prevent users from modifying their own role (except admins)
    if (userId === session.user.id && validatedData.role && userRole !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: "Cannot modify your own role" },
        { status: 403 }
      )
    }

    // Prepare update data
    const updateData: any = { ...validatedData }
    
    // Hash password if provided
    if (validatedData.password) {
      updateData.password = await bcrypt.hash(validatedData.password, 12)
    }

    // Update user
    const user = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      select: {
        id: true,
        email: true,
        name: true,
        firstName: true,
        lastName: true,
        phone: true,
        role: true,
        status: true,
        updatedAt: true,
      }
    })

    return NextResponse.json({
      message: "User updated successfully",
      user
    })

  } catch (error) {
    console.error("Error updating user:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid input data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Failed to update user" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check permissions
    const userRole = session.user.role as UserRole
    if (!PermissionManager.hasPermission(userRole, Permission.DELETE_USERS)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const userId = params.id

    // Prevent users from deleting themselves
    if (userId === session.user.id) {
      return NextResponse.json(
        { error: "Cannot delete your own account" },
        { status: 403 }
      )
    }

    // Get user to check if we can manage them
    const userToDelete = await prisma.user.findUnique({
      where: { id: userId },
      select: { role: true, email: true }
    })

    if (!userToDelete) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Check if user can manage this user
    if (!canManageUser(userRole, userToDelete.role)) {
      return NextResponse.json(
        { error: "Cannot delete this user" },
        { status: 403 }
      )
    }

    // Soft delete by setting status to inactive instead of hard delete
    // This preserves data integrity for applications, etc.
    await prisma.user.update({
      where: { id: userId },
      data: { 
        status: UserStatus.INACTIVE,
        email: `deleted_${Date.now()}_${userToDelete.email}` // Prevent email conflicts
      }
    })

    return NextResponse.json({
      message: "User deleted successfully"
    })

  } catch (error) {
    console.error("Error deleting user:", error)
    return NextResponse.json(
      { error: "Failed to delete user" },
      { status: 500 }
    )
  }
}

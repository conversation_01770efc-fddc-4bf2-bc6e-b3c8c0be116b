import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { UserRole } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || ![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role as UserRole)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search") || ""
    const category = searchParams.get("category") || ""
    const isPublished = searchParams.get("isPublished")

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { content: { contains: search, mode: "insensitive" } },
        { excerpt: { contains: search, mode: "insensitive" } },
        { authorName: { contains: search, mode: "insensitive" } }
      ]
    }

    if (category) {
      where.category = category
    }

    if (isPublished !== null && isPublished !== undefined) {
      where.isPublished = isPublished === "true"
    }

    // Get blog posts
    const [blogPosts, total] = await Promise.all([
      prisma.blogPost.findMany({
        where,
        orderBy: { createdAt: "desc" },
        skip,
        take: limit
      }),
      prisma.blogPost.count({ where })
    ])

    // Get statistics
    const stats = await prisma.blogPost.groupBy({
      by: ['isPublished'],
      _count: {
        id: true
      }
    })

    const publishedCount = stats.find(s => s.isPublished)?._count.id || 0
    const draftCount = stats.find(s => !s.isPublished)?._count.id || 0

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      blogPosts,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      stats: {
        total,
        published: publishedCount,
        drafts: draftCount
      }
    })
  } catch (error) {
    console.error("Error fetching blog posts:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || ![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role as UserRole)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const {
      title,
      slug,
      content,
      excerpt,
      featuredImage,
      metaTitle,
      metaDescription,
      category,
      tags,
      isPublished,
      publishedAt
    } = body

    // Validate required fields
    if (!title || !content) {
      return NextResponse.json(
        { error: "Title and content are required" },
        { status: 400 }
      )
    }

    // Generate slug if not provided
    const finalSlug = slug || title.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')

    // Check if slug already exists
    const existingPost = await prisma.blogPost.findUnique({
      where: { slug: finalSlug }
    })

    if (existingPost) {
      return NextResponse.json(
        { error: "A post with this slug already exists" },
        { status: 400 }
      )
    }

    const blogPost = await prisma.blogPost.create({
      data: {
        title,
        slug: finalSlug,
        content,
        excerpt: excerpt || null,
        featuredImage: featuredImage || null,
        metaTitle: metaTitle || null,
        metaDescription: metaDescription || null,
        category: category || null,
        tags: tags || [],
        isPublished: isPublished || false,
        publishedAt: isPublished && publishedAt ? new Date(publishedAt) : (isPublished ? new Date() : null),
        authorName: session.user.name || "Admin",
        authorEmail: session.user.email || null
      }
    })

    return NextResponse.json(blogPost, { status: 201 })
  } catch (error) {
    console.error("Error creating blog post:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || ![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role as UserRole)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { id, isPublished, ...updateData } = body

    if (!id) {
      return NextResponse.json(
        { error: "Post ID is required" },
        { status: 400 }
      )
    }

    // If publishing, set publishedAt if not already set
    if (isPublished && !updateData.publishedAt) {
      updateData.publishedAt = new Date()
    }

    // If unpublishing, clear publishedAt
    if (isPublished === false) {
      updateData.publishedAt = null
    }

    const blogPost = await prisma.blogPost.update({
      where: { id },
      data: {
        ...updateData,
        isPublished,
        updatedAt: new Date()
      }
    })

    return NextResponse.json(blogPost)
  } catch (error) {
    console.error("Error updating blog post:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || ![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role as UserRole)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get("id")

    if (!id) {
      return NextResponse.json(
        { error: "Post ID is required" },
        { status: 400 }
      )
    }

    await prisma.blogPost.delete({
      where: { id }
    })

    return NextResponse.json({ message: "Post deleted successfully" })
  } catch (error) {
    console.error("Error deleting blog post:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { UserRole, ApplicationStatus } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check permissions
    const userRole = session.user.role as UserRole
    if (![UserRole.STAFF, UserRole.ADMIN].includes(userRole)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "50")
    const search = searchParams.get("search") || ""
    const status = searchParams.get("status")
    const sortBy = searchParams.get("sortBy") || "submittedAt"
    const sortOrder = searchParams.get("sortOrder") || "desc"

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.OR = [
        { applicantName: { contains: search, mode: "insensitive" } },
        { applicantEmail: { contains: search, mode: "insensitive" } },
        { pet: { name: { contains: search, mode: "insensitive" } } },
        { pet: { breed: { contains: search, mode: "insensitive" } } },
        { user: { name: { contains: search, mode: "insensitive" } } },
        { user: { email: { contains: search, mode: "insensitive" } } }
      ]
    }
    
    if (status && status !== "all") {
      where.status = status as ApplicationStatus
    }

    // Build orderBy clause
    const orderBy: any = {}
    if (sortBy === "submittedAt") {
      orderBy.submittedAt = sortOrder
    } else if (sortBy === "applicantName") {
      orderBy.applicantName = sortOrder
    } else if (sortBy === "status") {
      orderBy.status = sortOrder
    } else if (sortBy === "updatedAt") {
      orderBy.updatedAt = sortOrder
    } else {
      orderBy.submittedAt = "desc"
    }

    const [applications, totalCount] = await Promise.all([
      prisma.application.findMany({
        where,
        orderBy,
        skip: (page - 1) * limit,
        take: limit,
        include: {
          pet: {
            select: {
              id: true,
              name: true,
              species: true,
              breed: true,
              photos: {
                select: {
                  url: true,
                  isPrimary: true
                },
                orderBy: { order: "asc" }
              }
            }
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          }
        }
      }),
      prisma.application.count({ where })
    ])

    const totalPages = Math.ceil(totalCount / limit)

    return NextResponse.json({
      applications,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }
    })

  } catch (error) {
    console.error("Error fetching applications:", error)
    return NextResponse.json(
      { error: "Failed to fetch applications" },
      { status: 500 }
    )
  }
}
import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { UserRole, ApplicationStatus } from "@prisma/client"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check permissions
    const userRole = session.user.role as UserRole
    if (![UserRole.STAFF, UserRole.ADMIN].includes(userRole)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const applicationId = params.id

    // Get application with related data
    const application = await prisma.application.findUnique({
      where: { id: applicationId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            firstName: true,
            lastName: true,
          }
        },
        pet: {
          select: {
            id: true,
            name: true,
            species: true,
            breed: true,
            age: true,
            status: true,
            photos: {
              select: {
                id: true,
                url: true,
                isPrimary: true,
              },
              where: { isPrimary: true },
              take: 1
            }
          }
        }
      }
    })

    if (!application) {
      return NextResponse.json(
        { error: "Application not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({ application })

  } catch (error) {
    console.error("Error fetching application:", error)
    return NextResponse.json(
      { error: "Failed to fetch application" },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check permissions
    const userRole = session.user.role as UserRole
    if (![UserRole.STAFF, UserRole.ADMIN].includes(userRole)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const applicationId = params.id
    const body = await request.json()

    const updateData: any = {}

    // Handle status updates
    if (body.status) {
      updateData.status = body.status
      
      // Set timestamps based on status
      if (body.status === ApplicationStatus.UNDER_REVIEW && !updateData.reviewedAt) {
        updateData.reviewedAt = new Date()
      }
      
      if (body.status === ApplicationStatus.APPROVED) {
        updateData.approvedAt = new Date()
      }
    }

    // Handle all form fields
    const allowedFields = [
      'applicantName', 'applicantEmail', 'applicantPhone', 'housingType', 'ownOrRent',
      'landlordContact', 'hasYard', 'yardFenced', 'yardSize', 'householdSize',
      'hasChildren', 'childrenAges', 'hasOtherPets', 'otherPetsDetails',
      'petExperience', 'previousPets', 'currentVeterinarian', 'vetContact',
      'workSchedule', 'travelFrequency', 'exercisePlans', 'trainingPlans',
      'agreesToTerms', 'agreesToHomeVisit', 'staffNotes', 'rejectionReason'
    ]

    allowedFields.forEach(field => {
      if (body[field] !== undefined) {
        updateData[field] = body[field]
      }
    })

    // Update the application
    const updatedApplication = await prisma.application.update({
      where: { id: applicationId },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            firstName: true,
            lastName: true,
          }
        },
        pet: {
          select: {
            id: true,
            name: true,
            species: true,
            breed: true,
            age: true,
            status: true,
            photos: {
              select: {
                id: true,
                url: true,
                isPrimary: true,
              },
              where: { isPrimary: true },
              take: 1
            }
          }
        }
      }
    })

    return NextResponse.json({
      message: "Application updated successfully",
      application: updatedApplication
    })

  } catch (error) {
    console.error("Error updating application:", error)
    return NextResponse.json(
      { error: "Failed to update application" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check permissions - only admins can delete applications
    const userRole = session.user.role as UserRole
    if (userRole !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const applicationId = params.id

    // Delete the application
    await prisma.application.delete({
      where: { id: applicationId }
    })

    return NextResponse.json({
      message: "Application deleted successfully"
    })

  } catch (error) {
    console.error("Error deleting application:", error)
    return NextResponse.json(
      { error: "Failed to delete application" },
      { status: 500 }
    )
  }
}
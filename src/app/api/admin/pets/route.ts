import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { UserRole, PetStatus } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if user has admin/staff permissions
    if (![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role as UserRole)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "12")
    const search = searchParams.get("search") || ""
    const status = searchParams.get("status")
    const species = searchParams.get("species")
    const sortBy = searchParams.get("sortBy") || "arrivalDate"
    const sortOrder = searchParams.get("sortOrder") || "desc"

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { breed: { contains: search, mode: "insensitive" } },
        { species: { contains: search, mode: "insensitive" } },
        { color: { contains: search, mode: "insensitive" } }
      ]
    }

    if (status && status !== "all") {
      where.status = status as PetStatus
    }

    if (species && species !== "all") {
      where.species = { equals: species, mode: "insensitive" }
    }

    // Build orderBy clause
    const orderBy: any = {}
    if (sortBy === "arrivalDate") {
      orderBy.arrivalDate = sortOrder
    } else if (sortBy === "name") {
      orderBy.name = sortOrder
    } else if (sortBy === "age") {
      orderBy.age = sortOrder
    } else if (sortBy === "adoptionFee") {
      orderBy.adoptionFee = sortOrder
    } else {
      orderBy.arrivalDate = "desc"
    }

    const [pets, totalCount] = await Promise.all([
      prisma.pet.findMany({
        where,
        orderBy,
        skip: (page - 1) * limit,
        take: limit,
        include: {
          photos: {
            orderBy: { order: "asc" }
          },
          organization: {
            select: {
              name: true,
              city: true,
              state: true,
            }
          },
          _count: {
            select: {
              applications: true,
              favorites: true,
            }
          }
        }
      }),
      prisma.pet.count({ where })
    ])

    const totalPages = Math.ceil(totalCount / limit)

    return NextResponse.json({
      pets,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }
    })

  } catch (error) {
    console.error("Error fetching pets:", error)
    return NextResponse.json(
      { error: "Failed to fetch pets" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if user has admin/staff permissions
    if (![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const body = await request.json()
    
    // Validate required fields
    const requiredFields = ["name", "species", "breed", "age", "size", "gender", "organizationId"]
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        )
      }
    }

    // Generate slug
    const baseSlug = body.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
    let slug = baseSlug
    let counter = 1
    
    while (await prisma.pet.findUnique({ where: { slug } })) {
      slug = `${baseSlug}-${counter}`
      counter++
    }

    // Create pet
    const pet = await prisma.pet.create({
      data: {
        ...body,
        slug,
        searchKeywords: [
          body.name,
          body.species,
          body.breed,
          body.color,
          ...(body.personalityTraits || []),
        ].filter(Boolean) as string[],
      },
      include: {
        photos: true,
        organization: {
          select: {
            name: true,
            city: true,
            state: true,
          }
        },
        _count: {
          select: {
            applications: true,
            favorites: true,
          }
        }
      }
    })
    
    return NextResponse.json({
      message: "Pet created successfully",
      pet
    }, { status: 201 })
    
  } catch (error) {
    console.error("Error creating pet:", error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: "Failed to create pet" },
      { status: 500 }
    )
  }
}

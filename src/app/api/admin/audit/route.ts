import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { UserRole } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "20")
    const search = searchParams.get("search") || ""
    const action = searchParams.get("action") || ""
    const userId = searchParams.get("userId") || ""
    const dateFrom = searchParams.get("dateFrom") || ""
    const dateTo = searchParams.get("dateTo") || ""

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.OR = [
        { action: { contains: search, mode: "insensitive" } },
        { details: { contains: search, mode: "insensitive" } },
        { user: { name: { contains: search, mode: "insensitive" } } },
        { user: { email: { contains: search, mode: "insensitive" } } },
        { ipAddress: { contains: search, mode: "insensitive" } }
      ]
    }

    if (action) {
      where.action = action
    }

    if (userId) {
      where.userId = userId
    }

    if (dateFrom || dateTo) {
      where.createdAt = {}
      if (dateFrom) {
        where.createdAt.gte = new Date(dateFrom)
      }
      if (dateTo) {
        const endDate = new Date(dateTo)
        endDate.setHours(23, 59, 59, 999)
        where.createdAt.lte = endDate
      }
    }

    // Get audit logs with user information
    const [auditLogs, total] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true
            }
          }
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit
      }),
      prisma.auditLog.count({ where })
    ])

    // Get summary statistics
    const stats = await Promise.all([
      // Actions by type
      prisma.auditLog.groupBy({
        by: ['action'],
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        },
        _count: { id: true }
      }),
      // Activity by user
      prisma.auditLog.groupBy({
        by: ['userId'],
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        },
        _count: { id: true },
        orderBy: { _count: { id: 'desc' } },
        take: 10
      }),
      // Total logs count
      prisma.auditLog.count(),
      // Failed login attempts
      prisma.auditLog.count({
        where: {
          action: 'LOGIN_FAILED',
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        }
      })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      auditLogs,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      stats: {
        actionsByType: stats[0],
        activityByUser: stats[1],
        totalLogs: stats[2],
        failedLogins: stats[3]
      }
    })
  } catch (error) {
    console.error("Error fetching audit logs:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const {
      action,
      details,
      resourceType,
      resourceId,
      ipAddress,
      userAgent
    } = body

    // Validate required fields
    if (!action) {
      return NextResponse.json(
        { error: "Action is required" },
        { status: 400 }
      )
    }

    const auditLog = await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action,
        details: details || null,
        resourceType: resourceType || null,
        resourceId: resourceId || null,
        ipAddress: ipAddress || null,
        userAgent: userAgent || null
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    })

    return NextResponse.json(auditLog, { status: 201 })
  } catch (error) {
    console.error("Error creating audit log:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get("action")
    const days = parseInt(searchParams.get("days") || "0")

    if (action === "cleanup" && days > 0) {
      // Delete logs older than specified days
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - days)

      const result = await prisma.auditLog.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate
          }
        }
      })

      // Log the cleanup action
      await prisma.auditLog.create({
        data: {
          userId: session.user.id,
          action: "AUDIT_CLEANUP",
          details: `Deleted ${result.count} audit logs older than ${days} days`,
          resourceType: "AUDIT_LOG"
        }
      })

      return NextResponse.json({
        message: `Deleted ${result.count} audit logs`,
        deletedCount: result.count
      })
    }

    return NextResponse.json({ error: "Invalid cleanup action" }, { status: 400 })
  } catch (error) {
    console.error("Error cleaning up audit logs:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// Helper function to create audit log entries (can be used by other parts of the application)
export async function createAuditLog(
  userId: string,
  action: string,
  details?: string,
  resourceType?: string,
  resourceId?: string,
  ipAddress?: string,
  userAgent?: string
) {
  try {
    await prisma.auditLog.create({
      data: {
        userId,
        action,
        details: details || null,
        resourceType: resourceType || null,
        resourceId: resourceId || null,
        ipAddress: ipAddress || null,
        userAgent: userAgent || null
      }
    })
  } catch (error) {
    console.error("Error creating audit log:", error)
    // Don't throw error to avoid breaking the main operation
  }
}

import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { UserRole } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get("action")

    switch (action) {
      case "stats":
        return await getDatabaseStats()
      case "health":
        return await getDatabaseHealth()
      case "backups":
        return await getBackupHistory()
      default:
        return await getDatabaseOverview()
    }
  } catch (error) {
    console.error("Error fetching database information:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { action, options } = body

    switch (action) {
      case "backup":
        return await createBackup(session.user.id, options)
      case "optimize":
        return await optimizeDatabase(session.user.id)
      case "cleanup":
        return await cleanupDatabase(session.user.id, options)
      case "vacuum":
        return await vacuumDatabase(session.user.id)
      default:
        return NextResponse.json({ error: "Invalid action" }, { status: 400 })
    }
  } catch (error) {
    console.error("Error performing database operation:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

async function getDatabaseOverview() {
  // Get table counts and sizes
  const tables = [
    'users', 'pets', 'applications', 'adoptions', 'donations',
    'volunteerProfiles', 'fosterProfiles', 'medicalRecords',
    'appointments', 'inventoryItems', 'blogPosts', 'messages',
    'auditLogs', 'systemSettings'
  ]

  const tableStats = await Promise.all(
    tables.map(async (table) => {
      try {
        const count = await (prisma as any)[table].count()
        return { table, count, error: null }
      } catch (error) {
        return { table, count: 0, error: error.message }
      }
    })
  )

  // Get recent activity
  const recentActivity = await Promise.all([
    prisma.user.count({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
        }
      }
    }),
    prisma.application.count({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
        }
      }
    }),
    prisma.adoption.count({
      where: {
        adoptionDate: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
        }
      }
    }),
    prisma.auditLog.count({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
        }
      }
    })
  ])

  return NextResponse.json({
    tableStats,
    recentActivity: {
      newUsers: recentActivity[0],
      newApplications: recentActivity[1],
      newAdoptions: recentActivity[2],
      auditLogs: recentActivity[3]
    },
    lastUpdated: new Date().toISOString()
  })
}

async function getDatabaseStats() {
  try {
    // Get database size and connection info
    const dbInfo = await prisma.$queryRaw`
      SELECT 
        pg_size_pretty(pg_database_size(current_database())) as database_size,
        current_database() as database_name,
        version() as postgres_version
    `

    // Get table sizes
    const tableSizes = await prisma.$queryRaw`
      SELECT 
        schemaname,
        tablename,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
        pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
      FROM pg_tables 
      WHERE schemaname = 'public'
      ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
    `

    // Get index usage
    const indexUsage = await prisma.$queryRaw`
      SELECT 
        schemaname,
        tablename,
        indexname,
        idx_scan,
        idx_tup_read,
        idx_tup_fetch
      FROM pg_stat_user_indexes
      ORDER BY idx_scan DESC
      LIMIT 10
    `

    return NextResponse.json({
      dbInfo,
      tableSizes,
      indexUsage
    })
  } catch (error) {
    // Fallback for non-PostgreSQL databases
    return NextResponse.json({
      dbInfo: [{ database_name: "Unknown", database_size: "Unknown", postgres_version: "Unknown" }],
      tableSizes: [],
      indexUsage: [],
      error: "Database statistics not available for this database type"
    })
  }
}

async function getDatabaseHealth() {
  try {
    // Check database connectivity
    const connectionTest = await prisma.$queryRaw`SELECT 1 as connected`
    
    // Get slow queries (if available)
    const slowQueries = await prisma.$queryRaw`
      SELECT 
        query,
        calls,
        total_time,
        mean_time,
        rows
      FROM pg_stat_statements 
      ORDER BY mean_time DESC 
      LIMIT 10
    `.catch(() => [])

    // Get active connections
    const activeConnections = await prisma.$queryRaw`
      SELECT 
        count(*) as active_connections,
        max(now() - query_start) as longest_query_time
      FROM pg_stat_activity 
      WHERE state = 'active'
    `.catch(() => [{ active_connections: 0, longest_query_time: null }])

    return NextResponse.json({
      status: "healthy",
      connectionTest,
      slowQueries,
      activeConnections,
      lastChecked: new Date().toISOString()
    })
  } catch (error) {
    return NextResponse.json({
      status: "error",
      error: error.message,
      lastChecked: new Date().toISOString()
    })
  }
}

async function getBackupHistory() {
  try {
    const backups = await prisma.databaseBackup.findMany({
      orderBy: { createdAt: 'desc' },
      take: 20,
      include: {
        createdBy: {
          select: {
            name: true,
            email: true
          }
        }
      }
    })

    return NextResponse.json({ backups })
  } catch (error) {
    return NextResponse.json({ backups: [], error: "Backup history not available" })
  }
}

async function createBackup(userId: string, options: any = {}) {
  try {
    // Create backup record
    const backup = await prisma.databaseBackup.create({
      data: {
        filename: `backup_${Date.now()}.sql`,
        size: 0, // Will be updated after backup
        status: 'IN_PROGRESS',
        backupType: options.type || 'MANUAL',
        createdById: userId,
        description: options.description || 'Manual backup'
      }
    })

    // In a real implementation, you would:
    // 1. Create actual database backup using pg_dump or similar
    // 2. Store the backup file in secure storage
    // 3. Update the backup record with file size and status

    // For demo purposes, simulate backup completion
    setTimeout(async () => {
      await prisma.databaseBackup.update({
        where: { id: backup.id },
        data: {
          status: 'COMPLETED',
          size: Math.floor(Math.random() * 1000000) + 500000, // Random size for demo
          completedAt: new Date()
        }
      })
    }, 2000)

    // Log the backup action
    await prisma.auditLog.create({
      data: {
        userId,
        action: 'DATABASE_BACKUP',
        details: `Database backup initiated: ${backup.filename}`,
        resourceType: 'DATABASE',
        resourceId: backup.id
      }
    })

    return NextResponse.json({
      message: "Backup initiated successfully",
      backup
    })
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to create backup" },
      { status: 500 }
    )
  }
}

async function optimizeDatabase(userId: string) {
  try {
    // Run database optimization commands
    const optimizationResults = []

    // Analyze tables for better query planning
    try {
      await prisma.$executeRaw`ANALYZE`
      optimizationResults.push("Table statistics updated")
    } catch (error) {
      optimizationResults.push(`Analysis failed: ${error.message}`)
    }

    // Reindex if PostgreSQL
    try {
      await prisma.$executeRaw`REINDEX DATABASE ${prisma.$queryRaw`SELECT current_database()`}`
      optimizationResults.push("Indexes rebuilt")
    } catch (error) {
      optimizationResults.push("Index rebuild not available for this database")
    }

    // Log the optimization
    await prisma.auditLog.create({
      data: {
        userId,
        action: 'DATABASE_OPTIMIZE',
        details: `Database optimization completed: ${optimizationResults.join(', ')}`,
        resourceType: 'DATABASE'
      }
    })

    return NextResponse.json({
      message: "Database optimization completed",
      results: optimizationResults
    })
  } catch (error) {
    return NextResponse.json(
      { error: "Database optimization failed" },
      { status: 500 }
    )
  }
}

async function cleanupDatabase(userId: string, options: any = {}) {
  try {
    const cleanupResults = []
    const { days = 90, tables = [] } = options

    // Clean up old audit logs
    if (tables.includes('auditLogs') || tables.length === 0) {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - days)

      const deletedLogs = await prisma.auditLog.deleteMany({
        where: {
          createdAt: { lt: cutoffDate }
        }
      })
      cleanupResults.push(`Deleted ${deletedLogs.count} old audit logs`)
    }

    // Clean up old backups
    if (tables.includes('backups') || tables.length === 0) {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - (days * 2)) // Keep backups longer

      const deletedBackups = await prisma.databaseBackup.deleteMany({
        where: {
          createdAt: { lt: cutoffDate },
          status: 'COMPLETED'
        }
      })
      cleanupResults.push(`Deleted ${deletedBackups.count} old backup records`)
    }

    // Log the cleanup
    await prisma.auditLog.create({
      data: {
        userId,
        action: 'DATABASE_CLEANUP',
        details: `Database cleanup completed: ${cleanupResults.join(', ')}`,
        resourceType: 'DATABASE'
      }
    })

    return NextResponse.json({
      message: "Database cleanup completed",
      results: cleanupResults
    })
  } catch (error) {
    return NextResponse.json(
      { error: "Database cleanup failed" },
      { status: 500 }
    )
  }
}

async function vacuumDatabase(userId: string) {
  try {
    // Run VACUUM command for PostgreSQL
    await prisma.$executeRaw`VACUUM ANALYZE`

    // Log the vacuum operation
    await prisma.auditLog.create({
      data: {
        userId,
        action: 'DATABASE_VACUUM',
        details: 'Database vacuum and analyze completed',
        resourceType: 'DATABASE'
      }
    })

    return NextResponse.json({
      message: "Database vacuum completed successfully"
    })
  } catch (error) {
    return NextResponse.json(
      { error: "Database vacuum not available for this database type" },
      { status: 400 }
    )
  }
}

import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { UserRole, ApplicationStatus, PetStatus, VolunteerStatus, FosterStatus } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check permissions
    const userRole = session.user.role as UserRole
    if (![UserRole.STAFF, UserRole.ADMIN].includes(userRole)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get("period") || "12months"
    const type = searchParams.get("type") || "overview"

    // Calculate date range based on period
    const now = new Date()
    let startDate = new Date()
    
    switch (period) {
      case "1month":
        startDate.setMonth(now.getMonth() - 1)
        break
      case "3months":
        startDate.setMonth(now.getMonth() - 3)
        break
      case "6months":
        startDate.setMonth(now.getMonth() - 6)
        break
      case "12months":
        startDate.setFullYear(now.getFullYear() - 1)
        break
      case "ytd":
        startDate = new Date(now.getFullYear(), 0, 1)
        break
      default:
        startDate.setFullYear(now.getFullYear() - 1)
    }

    // Get adoption statistics
    const [
      totalAdoptions,
      thisMonthAdoptions,
      lastMonthAdoptions,
      totalApplications,
      pendingApplications,
      approvedApplications,
      rejectedApplications,
      totalPets,
      availablePets,
      adoptedPets,
      fosterPets,
      totalVolunteers,
      activeVolunteers,
      totalVolunteerHours,
      totalDonations,
      thisMonthDonations,
    ] = await Promise.all([
      // Adoptions
      prisma.adoption.count({
        where: {
          adoptionDate: { gte: startDate }
        }
      }),
      prisma.adoption.count({
        where: {
          adoptionDate: {
            gte: new Date(now.getFullYear(), now.getMonth(), 1),
            lt: new Date(now.getFullYear(), now.getMonth() + 1, 1)
          }
        }
      }),
      prisma.adoption.count({
        where: {
          adoptionDate: {
            gte: new Date(now.getFullYear(), now.getMonth() - 1, 1),
            lt: new Date(now.getFullYear(), now.getMonth(), 1)
          }
        }
      }),

      // Applications
      prisma.application.count({
        where: {
          createdAt: { gte: startDate }
        }
      }),
      prisma.application.count({
        where: {
          status: ApplicationStatus.UNDER_REVIEW,
          createdAt: { gte: startDate }
        }
      }),
      prisma.application.count({
        where: {
          status: ApplicationStatus.APPROVED,
          createdAt: { gte: startDate }
        }
      }),
      prisma.application.count({
        where: {
          status: ApplicationStatus.REJECTED,
          createdAt: { gte: startDate }
        }
      }),

      // Pets
      prisma.pet.count(),
      prisma.pet.count({
        where: { status: PetStatus.AVAILABLE }
      }),
      prisma.pet.count({
        where: { status: PetStatus.ADOPTED }
      }),
      prisma.pet.count({
        where: { status: PetStatus.FOSTER_CARE }
      }),

      // Volunteers
      prisma.volunteerProfile.count(),
      prisma.volunteerProfile.count({
        where: { status: VolunteerStatus.ACTIVE }
      }),
      prisma.volunteerHours.aggregate({
        _sum: { hours: true },
        where: {
          date: { gte: startDate }
        }
      }),

      // Donations
      prisma.donation.aggregate({
        _sum: { amount: true },
        _avg: { amount: true },
        where: {
          createdAt: { gte: startDate },
          paymentStatus: "COMPLETED"
        }
      }),
      prisma.donation.aggregate({
        _sum: { amount: true },
        where: {
          createdAt: {
            gte: new Date(now.getFullYear(), now.getMonth(), 1),
            lt: new Date(now.getFullYear(), now.getMonth() + 1, 1)
          },
          paymentStatus: "COMPLETED"
        }
      }),
    ])

    // Calculate monthly trend for adoptions
    const monthlyTrend = lastMonthAdoptions > 0 
      ? ((thisMonthAdoptions - lastMonthAdoptions) / lastMonthAdoptions) * 100 
      : thisMonthAdoptions > 0 ? 100 : 0

    const reportData = {
      adoptions: {
        total: totalAdoptions,
        thisMonth: thisMonthAdoptions,
        lastMonth: lastMonthAdoptions,
        monthlyTrend: monthlyTrend
      },
      applications: {
        total: totalApplications,
        pending: pendingApplications,
        approved: approvedApplications,
        rejected: rejectedApplications
      },
      pets: {
        total: totalPets,
        available: availablePets,
        adopted: adoptedPets,
        inFoster: fosterPets
      },
      volunteers: {
        total: totalVolunteers,
        active: activeVolunteers,
        totalHours: totalVolunteerHours._sum.hours || 0
      },
      donations: {
        total: totalDonations._sum.amount || 0,
        thisMonth: thisMonthDonations._sum.amount || 0,
        averageAmount: totalDonations._avg.amount || 0
      },
      monthlyStats: [] // This would be populated with more complex queries for chart data
    }

    return NextResponse.json(reportData)

  } catch (error) {
    console.error("Error fetching report data:", error)
    return NextResponse.json(
      { error: "Failed to fetch report data" },
      { status: 500 }
    )
  }
}
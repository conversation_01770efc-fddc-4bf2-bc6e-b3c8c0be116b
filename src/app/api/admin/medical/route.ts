import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { UserRole } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check permissions
    const userRole = session.user.role as UserRole
    if (![UserRole.STAFF, UserRole.ADMIN].includes(userRole)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search") || ""
    const type = searchParams.get("type") || ""
    const sortBy = searchParams.get("sortBy") || "date"
    const sortOrder = searchParams.get("sortOrder") || "desc"

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.OR = [
        { pet: { name: { contains: search, mode: "insensitive" } } },
        { veterinarian: { contains: search, mode: "insensitive" } },
        { clinic: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { recordType: { contains: search, mode: "insensitive" } },
      ]
    }

    if (type && type !== "all") {
      where.recordType = { equals: type, mode: "insensitive" }
    }

    // Get medical records with pagination
    const [records, total] = await Promise.all([
      prisma.medicalRecord.findMany({
        where,
        include: {
          pet: {
            select: {
              id: true,
              name: true,
              species: true,
              breed: true,
              status: true,
            }
          }
        },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder
        }
      }),
      prisma.medicalRecord.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      records,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }
    })

  } catch (error) {
    console.error("Error fetching medical records:", error)
    return NextResponse.json(
      { error: "Failed to fetch medical records" },
      { status: 500 }
    )
  }
}
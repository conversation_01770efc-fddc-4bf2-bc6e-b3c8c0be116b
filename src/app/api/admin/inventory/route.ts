import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { UserRole } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || ![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role as UserRole)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search") || ""
    const category = searchParams.get("category") || ""
    const lowStock = searchParams.get("lowStock") === "true"

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { sku: { contains: search, mode: "insensitive" } },
        { supplier: { contains: search, mode: "insensitive" } }
      ]
    }

    if (category) {
      where.category = category
    }

    if (lowStock) {
      where.quantity = {
        lte: prisma.raw('minimum_quantity')
      }
    }

    // Get inventory items
    const [inventoryItems, total] = await Promise.all([
      prisma.inventoryItem.findMany({
        where,
        orderBy: { name: "asc" },
        skip,
        take: limit
      }),
      prisma.inventoryItem.count({ where })
    ])

    // Get low stock count
    const lowStockCount = await prisma.inventoryItem.count({
      where: {
        quantity: {
          lte: prisma.raw('minimum_quantity')
        }
      }
    })

    // Get total value
    const totalValue = await prisma.inventoryItem.aggregate({
      _sum: {
        totalValue: true
      }
    })

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      inventoryItems,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      stats: {
        totalItems: total,
        lowStockCount,
        totalValue: totalValue._sum.totalValue || 0
      }
    })
  } catch (error) {
    console.error("Error fetching inventory:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || ![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role as UserRole)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      description,
      sku,
      category,
      quantity,
      minimumQuantity,
      unitPrice,
      supplier,
      location,
      expirationDate,
      notes
    } = body

    // Validate required fields
    if (!name || !category || quantity === undefined || minimumQuantity === undefined) {
      return NextResponse.json(
        { error: "Name, category, quantity, and minimum quantity are required" },
        { status: 400 }
      )
    }

    // Calculate total value
    const totalValue = (unitPrice || 0) * quantity

    const inventoryItem = await prisma.inventoryItem.create({
      data: {
        name,
        description: description || null,
        sku: sku || null,
        category,
        quantity: parseInt(quantity),
        minimumQuantity: parseInt(minimumQuantity),
        unitPrice: unitPrice ? parseFloat(unitPrice) : null,
        totalValue,
        supplier: supplier || null,
        location: location || null,
        expirationDate: expirationDate ? new Date(expirationDate) : null,
        notes: notes || null
      }
    })

    return NextResponse.json(inventoryItem, { status: 201 })
  } catch (error) {
    console.error("Error creating inventory item:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || ![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role as UserRole)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { id, quantity, notes, action } = body

    if (!id) {
      return NextResponse.json(
        { error: "Item ID is required" },
        { status: 400 }
      )
    }

    // Get current item
    const currentItem = await prisma.inventoryItem.findUnique({
      where: { id }
    })

    if (!currentItem) {
      return NextResponse.json(
        { error: "Item not found" },
        { status: 404 }
      )
    }

    let newQuantity = currentItem.quantity

    if (action === "add") {
      newQuantity += parseInt(quantity || 0)
    } else if (action === "subtract") {
      newQuantity -= parseInt(quantity || 0)
      newQuantity = Math.max(0, newQuantity) // Don't allow negative quantities
    } else if (quantity !== undefined) {
      newQuantity = parseInt(quantity)
    }

    // Calculate new total value
    const totalValue = (currentItem.unitPrice || 0) * newQuantity

    const inventoryItem = await prisma.inventoryItem.update({
      where: { id },
      data: {
        quantity: newQuantity,
        totalValue,
        ...(notes && { notes }),
        updatedAt: new Date()
      }
    })

    return NextResponse.json(inventoryItem)
  } catch (error) {
    console.error("Error updating inventory item:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || ![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role as UserRole)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get("id")

    if (!id) {
      return NextResponse.json(
        { error: "Item ID is required" },
        { status: 400 }
      )
    }

    await prisma.inventoryItem.delete({
      where: { id }
    })

    return NextResponse.json({ message: "Item deleted successfully" })
  } catch (error) {
    console.error("Error deleting inventory item:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

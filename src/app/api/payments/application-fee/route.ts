import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { PaymentStatus } from "@prisma/client"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }
    
    const body = await request.json()
    const { petId, paymentMethod = "stripe" } = body
    
    if (!petId) {
      return NextResponse.json(
        { error: "Pet ID is required" },
        { status: 400 }
      )
    }
    
    // Check if pet exists and is available
    const pet = await prisma.pet.findUnique({
      where: { id: petId },
      select: { id: true, status: true, name: true }
    })
    
    if (!pet) {
      return NextResponse.json(
        { error: "Pet not found" },
        { status: 404 }
      )
    }
    
    if (pet.status !== "AVAILABLE") {
      return NextResponse.json(
        { error: "Pet is not available for adoption" },
        { status: 400 }
      )
    }
    
    // Check if user already has a pending or completed application fee payment for this pet
    const existingPayment = await prisma.payment.findFirst({
      where: {
        description: `Application fee for pet ${petId}`,
        status: {
          in: [PaymentStatus.PENDING, PaymentStatus.COMPLETED]
        }
      }
    })
    
    if (existingPayment) {
      return NextResponse.json(
        { error: "Application fee already paid or pending for this pet" },
        { status: 400 }
      )
    }
    
    // Create payment record
    const payment = await prisma.payment.create({
      data: {
        amount: 50.00,
        currency: "USD",
        status: PaymentStatus.PENDING,
        paymentMethod,
        description: `Application fee for pet ${petId}`,
        transactionDate: new Date(),
      }
    })
    
    // In a real implementation, you would integrate with Stripe or another payment processor here
    // For now, we'll simulate a successful payment
    
    // Simulate payment processing delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Update payment status to completed (in real implementation, this would be done via webhook)
    const completedPayment = await prisma.payment.update({
      where: { id: payment.id },
      data: {
        status: PaymentStatus.COMPLETED,
        stripePaymentId: `sim_${Date.now()}`, // Simulated payment ID
      }
    })
    
    return NextResponse.json({
      message: "Application fee payment successful",
      payment: completedPayment,
      paymentId: completedPayment.id
    }, { status: 201 })
    
  } catch (error) {
    console.error("Error processing application fee payment:", error)
    
    return NextResponse.json(
      { error: "Failed to process payment" },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }
    
    const { searchParams } = new URL(request.url)
    const petId = searchParams.get("petId")
    
    if (!petId) {
      return NextResponse.json(
        { error: "Pet ID is required" },
        { status: 400 }
      )
    }
    
    // Check if application fee has been paid for this pet
    const payment = await prisma.payment.findFirst({
      where: {
        description: `Application fee for pet ${petId}`,
        status: PaymentStatus.COMPLETED
      }
    })
    
    return NextResponse.json({
      hasPaid: !!payment,
      payment: payment || null
    })
    
  } catch (error) {
    console.error("Error checking application fee payment:", error)
    
    return NextResponse.json(
      { error: "Failed to check payment status" },
      { status: 500 }
    )
  }
}
import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { z } from "zod"

const paymentSchema = z.object({
  amount: z.number().positive(),
  currency: z.string().default("USD"),
  type: z.enum(["APPLICATION_FEE", "DONATION"]),
  applicationId: z.string().optional(),
  donationCampaignId: z.string().optional(),
  paymentMethod: z.enum(["CREDIT_CARD", "DEBIT_CARD", "PAYPAL", "APPLE_PAY", "GOOGLE_PAY", "BANK_TRANSFER"]),
  paymentDetails: z.object({
    cardNumber: z.string().optional(),
    expiryMonth: z.string().optional(),
    expiryYear: z.string().optional(),
    cvv: z.string().optional(),
    cardholderName: z.string().optional(),
    email: z.string().email().optional(),
  }).optional(),
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = paymentSchema.parse(body)

    // Simulate payment processing
    // In a real application, you would integrate with Stripe, PayPal, etc.
    const paymentSuccess = await simulatePaymentProcessing(validatedData)

    if (!paymentSuccess.success) {
      return NextResponse.json(
        { error: paymentSuccess.error },
        { status: 400 }
      )
    }

    // Create payment record
    const payment = await prisma.payment.create({
      data: {
        userId: session.user.id,
        amount: validatedData.amount,
        currency: validatedData.currency,
        type: validatedData.type,
        applicationId: validatedData.applicationId,
        donationCampaignId: validatedData.donationCampaignId,
        paymentMethod: validatedData.paymentMethod,
        status: "COMPLETED",
        transactionId: paymentSuccess.transactionId,
        processedAt: new Date(),
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          }
        },
        application: {
          select: {
            id: true,
            pet: {
              select: {
                name: true,
                species: true,
              }
            }
          }
        }
      }
    })

    // If this is an application fee, update the application status
    if (validatedData.type === "APPLICATION_FEE" && validatedData.applicationId) {
      await prisma.application.update({
        where: { id: validatedData.applicationId },
        data: {
          status: "SUBMITTED",
          submittedAt: new Date(),
        }
      })
    }

    return NextResponse.json({
      message: "Payment processed successfully",
      payment: {
        id: payment.id,
        amount: payment.amount,
        currency: payment.currency,
        status: payment.status,
        transactionId: payment.transactionId,
        type: payment.type,
      }
    }, { status: 201 })

  } catch (error) {
    console.error("Error processing payment:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid payment data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Failed to process payment" },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get("type")
    const limit = parseInt(searchParams.get("limit") || "10")

    const where: any = {
      userId: session.user.id,
    }

    if (type) {
      where.type = type
    }

    const payments = await prisma.payment.findMany({
      where,
      orderBy: { createdAt: "desc" },
      take: limit,
      include: {
        application: {
          select: {
            id: true,
            pet: {
              select: {
                name: true,
                species: true,
              }
            }
          }
        },
        donationCampaign: {
          select: {
            title: true,
          }
        }
      }
    })

    return NextResponse.json({ payments })

  } catch (error) {
    console.error("Error fetching payments:", error)
    return NextResponse.json(
      { error: "Failed to fetch payments" },
      { status: 500 }
    )
  }
}

// Simulate payment processing (replace with real payment gateway)
async function simulatePaymentProcessing(paymentData: any): Promise<{ success: boolean; transactionId?: string; error?: string }> {
  // Simulate processing delay
  await new Promise(resolve => setTimeout(resolve, 1000))

  // Simulate random success/failure (90% success rate)
  const success = Math.random() > 0.1

  if (success) {
    return {
      success: true,
      transactionId: `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }
  } else {
    return {
      success: false,
      error: "Payment declined. Please check your payment details and try again."
    }
  }
}

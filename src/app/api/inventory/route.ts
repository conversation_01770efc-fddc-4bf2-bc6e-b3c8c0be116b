import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { UserRole, InventoryCategory } from "@prisma/client"
import { z } from "zod"

const inventoryItemSchema = z.object({
  name: z.string().min(1, "Item name is required"),
  description: z.string().optional(),
  category: z.nativeEnum(InventoryCategory),
  currentStock: z.number().min(0, "Stock cannot be negative"),
  minimumStock: z.number().min(0, "Minimum stock cannot be negative"),
  unitCost: z.number().min(0, "Unit cost cannot be negative").optional(),
  supplier: z.string().optional(),
  location: z.string().optional(),
  expirationDate: z.string().optional(),
  notes: z.string().optional(),
})

const stockMovementSchema = z.object({
  itemId: z.string().min(1, "Item ID is required"),
  type: z.enum(["IN", "OUT", "ADJUSTMENT"]),
  quantity: z.number().min(1, "Quantity must be positive"),
  reason: z.string().min(1, "Reason is required"),
  notes: z.string().optional(),
  cost: z.number().min(0).optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if user has permission to view inventory
    if (![UserRole.STAFF, UserRole.ADMIN, UserRole.VOLUNTEER].includes(session.user.role)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "50")
    const category = searchParams.get("category")
    const lowStock = searchParams.get("lowStock") === "true"
    const search = searchParams.get("search")
    const skip = (page - 1) * limit

    let where: any = {}

    if (category) where.category = category as InventoryCategory
    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { supplier: { contains: search, mode: "insensitive" } },
      ]
    }
    if (lowStock) {
      where.currentStock = { lte: prisma.inventoryItem.fields.minimumStock }
    }

    const [items, total] = await Promise.all([
      prisma.inventoryItem.findMany({
        where,
        include: {
          movements: {
            orderBy: { createdAt: "desc" },
            take: 5,
            include: {
              createdBy: {
                select: {
                  name: true,
                }
              }
            }
          },
          _count: {
            select: {
              movements: true,
            }
          }
        },
        orderBy: [
          { currentStock: "asc" }, // Show low stock items first
          { name: "asc" }
        ],
        skip,
        take: limit,
      }),
      prisma.inventoryItem.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    // Calculate summary statistics
    const summary = await prisma.inventoryItem.aggregate({
      where,
      _count: { id: true },
      _sum: { currentStock: true },
    })

    const lowStockCount = await prisma.inventoryItem.count({
      where: {
        ...where,
        currentStock: { lte: prisma.inventoryItem.fields.minimumStock }
      }
    })

    return NextResponse.json({
      items,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      summary: {
        totalItems: summary._count.id,
        totalStock: summary._sum.currentStock || 0,
        lowStockItems: lowStockCount,
      }
    })

  } catch (error) {
    console.error("Error fetching inventory:", error)
    return NextResponse.json(
      { error: "Failed to fetch inventory" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if user has permission to manage inventory
    if (![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { action, ...data } = body

    if (action === "create_item") {
      const validatedData = inventoryItemSchema.parse(data)

      // Check if item with same name already exists
      const existingItem = await prisma.inventoryItem.findFirst({
        where: {
          name: validatedData.name,
          category: validatedData.category,
        }
      })

      if (existingItem) {
        return NextResponse.json(
          { error: "Item with this name already exists in this category" },
          { status: 400 }
        )
      }

      const item = await prisma.inventoryItem.create({
        data: {
          ...validatedData,
          expirationDate: validatedData.expirationDate ? new Date(validatedData.expirationDate) : null,
          organizationId: session.user.organizationId || "default", // TODO: Get from user's org
        },
        include: {
          movements: {
            orderBy: { createdAt: "desc" },
            take: 5,
          }
        }
      })

      // Create initial stock movement if there's initial stock
      if (validatedData.currentStock > 0) {
        await prisma.stockMovement.create({
          data: {
            itemId: item.id,
            type: "IN",
            quantity: validatedData.currentStock,
            reason: "Initial stock",
            createdById: session.user.id,
            cost: validatedData.unitCost ? validatedData.unitCost * validatedData.currentStock : null,
          }
        })
      }

      return NextResponse.json({
        message: "Inventory item created successfully",
        item
      }, { status: 201 })

    } else if (action === "stock_movement") {
      const validatedData = stockMovementSchema.parse(data)

      // Check if item exists
      const item = await prisma.inventoryItem.findUnique({
        where: { id: validatedData.itemId }
      })

      if (!item) {
        return NextResponse.json(
          { error: "Inventory item not found" },
          { status: 404 }
        )
      }

      // Calculate new stock level
      let newStock = item.currentStock
      switch (validatedData.type) {
        case "IN":
          newStock += validatedData.quantity
          break
        case "OUT":
          newStock -= validatedData.quantity
          break
        case "ADJUSTMENT":
          newStock = validatedData.quantity // For adjustments, quantity is the new total
          break
      }

      if (newStock < 0) {
        return NextResponse.json(
          { error: "Insufficient stock for this operation" },
          { status: 400 }
        )
      }

      // Create stock movement and update item in a transaction
      const result = await prisma.$transaction(async (tx) => {
        const movement = await tx.stockMovement.create({
          data: {
            ...validatedData,
            createdById: session.user.id,
          },
          include: {
            createdBy: {
              select: {
                name: true,
              }
            }
          }
        })

        const updatedItem = await tx.inventoryItem.update({
          where: { id: validatedData.itemId },
          data: { currentStock: newStock },
          include: {
            movements: {
              orderBy: { createdAt: "desc" },
              take: 5,
              include: {
                createdBy: {
                  select: {
                    name: true,
                  }
                }
              }
            }
          }
        })

        return { movement, item: updatedItem }
      })

      return NextResponse.json({
        message: "Stock movement recorded successfully",
        movement: result.movement,
        item: result.item
      }, { status: 201 })

    } else {
      return NextResponse.json(
        { error: "Invalid action" },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error("Error managing inventory:", error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: "Failed to manage inventory" },
      { status: 500 }
    )
  }
}

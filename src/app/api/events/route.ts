import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { eventSchema } from "@/lib/validations"
import { UserRole, EventType } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "20")
    const eventType = searchParams.get("eventType")
    const upcoming = searchParams.get("upcoming") === "true"
    const skip = (page - 1) * limit

    let where: any = {
      isPublic: true // Only show public events to non-authenticated users
    }

    const session = await getServerSession(authOptions)
    
    // Staff and admin can see all events
    if (session?.user && [UserRole.STAFF, UserRole.ADMIN].includes(session.user.role)) {
      delete where.isPublic
    }

    if (eventType) where.eventType = eventType as EventType
    if (upcoming) {
      where.startDate = {
        gte: new Date()
      }
    }

    const [events, total] = await Promise.all([
      prisma.event.findMany({
        where,
        include: {
          organization: {
            select: {
              name: true,
              city: true,
              state: true,
            }
          },
          _count: {
            select: {
              registrations: true,
            }
          }
        },
        orderBy: { startDate: "asc" },
        skip,
        take: limit,
      }),
      prisma.event.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      events,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }
    })

  } catch (error) {
    console.error("Error fetching events:", error)
    return NextResponse.json(
      { error: "Failed to fetch events" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if user has permission to create events
    if (![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = eventSchema.parse(body)

    // Validate dates
    const startDate = new Date(validatedData.startDate)
    const endDate = validatedData.endDate ? new Date(validatedData.endDate) : null

    if (endDate && endDate <= startDate) {
      return NextResponse.json(
        { error: "End date must be after start date" },
        { status: 400 }
      )
    }

    // Get user's organization
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true }
    })

    if (!user?.organizationId) {
      return NextResponse.json(
        { error: "User must be associated with an organization" },
        { status: 400 }
      )
    }

    const event = await prisma.event.create({
      data: {
        ...validatedData,
        startDate: startDate,
        endDate: endDate,
        organizationId: user.organizationId,
        createdById: session.user.id,
      },
      include: {
        organization: {
          select: {
            name: true,
            city: true,
            state: true,
          }
        },
        createdBy: {
          select: {
            name: true,
            email: true,
          }
        }
      }
    })

    return NextResponse.json({
      message: "Event created successfully",
      event
    }, { status: 201 })

  } catch (error) {
    console.error("Error creating event:", error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: "Failed to create event" },
      { status: 500 }
    )
  }
}

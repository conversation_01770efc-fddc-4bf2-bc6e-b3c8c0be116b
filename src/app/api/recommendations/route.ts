import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { PetMatchingService } from "@/lib/matching"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get("limit") || "10")

    // Get personalized recommendations
    const recommendations = await PetMatchingService.getRecommendationsForUser(session.user.id)

    // Limit results
    const limitedRecommendations = recommendations.slice(0, limit)

    return NextResponse.json({
      recommendations: limitedRecommendations,
      total: recommendations.length,
    })

  } catch (error) {
    console.error("Error fetching recommendations:", error)
    return NextResponse.json(
      { error: "Failed to fetch recommendations" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      preferredSpecies,
      preferredSizes,
      preferredAges,
      preferredGender,
      goodWithKids,
      goodWithDogs,
      goodWithCats,
      maxActivityLevel,
      maxTrainingNeeded,
      specialNeedsOk,
      maxAdoptionFee,
    } = body

    const criteria = {
      userId: session.user.id,
      preferredSpecies,
      preferredSizes,
      preferredAges,
      preferredGender,
      goodWithKids,
      goodWithDogs,
      goodWithCats,
      maxActivityLevel,
      maxTrainingNeeded,
      specialNeedsOk,
      maxAdoptionFee,
    }

    const matches = await PetMatchingService.findMatches(criteria)

    return NextResponse.json({
      matches,
      total: matches.length,
    })

  } catch (error) {
    console.error("Error finding matches:", error)
    return NextResponse.json(
      { error: "Failed to find matches" },
      { status: 500 }
    )
  }
}

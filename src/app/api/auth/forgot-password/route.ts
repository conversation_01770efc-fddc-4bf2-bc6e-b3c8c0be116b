import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { z } from "zod"
import crypto from "crypto"

const forgotPasswordSchema = z.object({
  email: z.string().email("Invalid email address"),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = forgotPasswordSchema.parse(body)

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email: validatedData.email },
      select: {
        id: true,
        email: true,
        name: true,
      }
    })

    // Always return success to prevent email enumeration attacks
    // But only send email if user actually exists
    if (user) {
      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString('hex')
      const resetTokenExpiry = new Date(Date.now() + 3600000) // 1 hour from now

      // Save reset token to database
      await prisma.user.update({
        where: { id: user.id },
        data: {
          resetToken,
          resetTokenExpiry,
        }
      })

      // In a real application, you would send an email here
      // For now, we'll just log the reset link
      const resetUrl = `${process.env.NEXTAUTH_URL}/auth/reset-password?token=${resetToken}`
      
      console.log(`Password reset requested for ${user.email}`)
      console.log(`Reset URL: ${resetUrl}`)
      
      // TODO: Send email with reset link
      // await sendPasswordResetEmail(user.email, user.name, resetUrl)
      
      // Simulate email sending delay
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    return NextResponse.json({
      message: "If an account with that email exists, we've sent password reset instructions."
    })

  } catch (error) {
    console.error("Error in forgot password:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid email address" },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Failed to process password reset request" },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { medicalRecordSchema } from "@/lib/validations"
import { UserRole } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const petId = searchParams.get("petId")
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "20")
    const skip = (page - 1) * limit

    let where: any = {}
    if (petId) where.petId = petId

    const [records, total] = await Promise.all([
      prisma.medicalRecord.findMany({
        where,
        include: {
          pet: {
            select: {
              name: true,
              species: true,
              breed: true,
            }
          }
        },
        orderBy: { date: "desc" },
        skip,
        take: limit,
      }),
      prisma.medicalRecord.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      records,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }
    })

  } catch (error) {
    console.error("Error fetching medical records:", error)
    return NextResponse.json(
      { error: "Failed to fetch medical records" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if user has permission to create medical records
    if (![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = medicalRecordSchema.parse(body)

    // Check if pet exists
    const pet = await prisma.pet.findUnique({
      where: { id: validatedData.petId },
      select: { id: true, name: true }
    })

    if (!pet) {
      return NextResponse.json(
        { error: "Pet not found" },
        { status: 404 }
      )
    }

    const record = await prisma.medicalRecord.create({
      data: {
        ...validatedData,
        date: new Date(validatedData.date),
        followUpDate: validatedData.followUpDate ? new Date(validatedData.followUpDate) : null,
      },
      include: {
        pet: {
          select: {
            name: true,
            species: true,
            breed: true,
          }
        }
      }
    })

    return NextResponse.json({
      message: "Medical record created successfully",
      record
    }, { status: 201 })

  } catch (error) {
    console.error("Error creating medical record:", error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: "Failed to create medical record" },
      { status: 500 }
    )
  }
}

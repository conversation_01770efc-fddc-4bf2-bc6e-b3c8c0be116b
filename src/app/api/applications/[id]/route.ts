import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { UserRole } from "@prisma/client"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    const applicationId = params.id

    // Build the where clause based on user role
    const whereClause: any = {
      id: applicationId
    }

    // Regular users can only see their own applications
    if (session.user.role === UserRole.PUBLIC || session.user.role === UserRole.ADOPTER) {
      whereClause.userId = session.user.id
    }
    // Staff and admin can see all applications (no additional restriction needed)

    const application = await prisma.application.findFirst({
      where: whereClause,
      include: {
        pet: {
          include: {
            photos: {
              orderBy: [
                { isPrimary: 'desc' },
                { createdAt: 'asc' }
              ]
            },
            organization: {
              select: {
                id: true,
                name: true,
                city: true,
                state: true,
                phone: true,
                email: true
              }
            }
          }
        },
        appointments: {
          orderBy: {
            scheduledDate: 'asc'
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!application) {
      return NextResponse.json(
        { error: "Application not found" },
        { status: 404 }
      )
    }

    // Create a timeline of status changes (simplified version)
    // In a real application, you might want to store status change history in a separate table
    const timeline = [
      {
        id: `${application.id}-submitted`,
        status: 'SUBMITTED',
        timestamp: application.submittedAt.toISOString(),
        notes: 'Application submitted successfully'
      }
    ]

    if (application.reviewedAt) {
      timeline.push({
        id: `${application.id}-reviewed`,
        status: 'UNDER_REVIEW',
        timestamp: application.reviewedAt.toISOString(),
        notes: 'Application is being reviewed by our team'
      })
    }

    if (application.approvedAt) {
      timeline.push({
        id: `${application.id}-approved`,
        status: 'APPROVED',
        timestamp: application.approvedAt.toISOString(),
        notes: 'Application approved! Please schedule pickup.'
      })
    }

    // Sort timeline by timestamp
    timeline.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())

    // Format the response to match the expected interface
    const formattedApplication = {
      id: application.id,
      status: application.status,
      submittedAt: application.submittedAt?.toISOString() || application.createdAt.toISOString(),
      reviewedAt: application.reviewedAt?.toISOString(),
      approvedAt: application.approvedAt?.toISOString(),
      staffNotes: application.staffNotes,
      rejectionReason: application.rejectionReason,
      
      // Personal Information (from schema fields)
      firstName: application.applicantName?.split(' ')[0] || application.user.name?.split(' ')[0] || '',
      lastName: application.applicantName?.split(' ').slice(1).join(' ') || application.user.name?.split(' ').slice(1).join(' ') || '',
      email: application.applicantEmail || application.user.email,
      phone: application.applicantPhone || '',
      dateOfBirth: new Date().toISOString(), // Default since not in schema
      
      // Address Information (not in current schema, using defaults)
      address: '',
      city: '',
      state: '',
      zipCode: '',
      
      // Housing Information
      housingType: application.housingType || '',
      ownRent: application.ownOrRent || '',
      landlordContact: application.landlordContact || '',
      hasYard: application.hasYard || false,
      yardFenced: application.yardFenced || false,
      
      // Experience and Lifestyle
      petExperience: application.petExperience || '',
      currentPets: application.otherPetsDetails || '',
      veterinarianInfo: application.currentVeterinarian || '',
      hoursAlonePerDay: 8, // Default
      activityLevel: 'moderate', // Default
      
      // Financial and References
      employmentStatus: application.workSchedule || '',
      annualIncome: null,
      references: application.personalReferences ? JSON.parse(application.personalReferences as string) : [],
      
      // Pet-specific questions
      whyAdopting: 'Looking for a loving companion', // Default
      petCareArrangements: application.exercisePlans || '',
      
      pet: {
        id: application.pet.id,
        name: application.pet.name,
        species: application.pet.species,
        breed: application.pet.breed || 'Mixed',
        age: application.pet.age || 0,
        gender: application.pet.gender,
        size: application.pet.size || 'MEDIUM',
        color: application.pet.color || 'Unknown',
        description: application.pet.description || '',
        photos: application.pet.photos,
        organization: application.pet.organization
      },
      
      appointments: application.appointments.map(apt => ({
        id: apt.id,
        type: apt.type,
        scheduledDate: apt.scheduledDate.toISOString(),
        status: apt.status,
        notes: apt.notes
      })),
      
      timeline
    }

    return NextResponse.json({
      application: formattedApplication
    })

  } catch (error) {
    console.error("Error fetching application details:", error)
    return NextResponse.json(
      { error: "Failed to fetch application details" },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Only staff and admin can update applications
    if (![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const applicationId = params.id
    const body = await request.json()

    const updatedApplication = await prisma.application.update({
      where: { id: applicationId },
      data: {
        status: body.status,
        staffNotes: body.staffNotes,
        rejectionReason: body.rejectionReason,
        reviewedAt: body.status === 'UNDER_REVIEW' ? new Date() : undefined,
        approvedAt: body.status === 'APPROVED' ? new Date() : undefined,
      },
      include: {
        pet: {
          include: {
            photos: true,
            organization: true
          }
        },
        appointments: true
      }
    })

    return NextResponse.json({
      application: updatedApplication
    })

  } catch (error) {
    console.error("Error updating application:", error)
    return NextResponse.json(
      { error: "Failed to update application" },
      { status: 500 }
    )
  }
}
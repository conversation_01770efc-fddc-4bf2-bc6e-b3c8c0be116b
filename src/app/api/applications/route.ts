import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { applicationSchema } from "@/lib/validations"
import { UserRole, ApplicationStatus } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }
    
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const status = searchParams.get("status")
    const petId = searchParams.get("petId")
    
    // Build where clause based on user role
    let where: any = {}
    
    if (session.user.role === UserRole.PUBLIC || session.user.role === UserRole.ADOPTER) {
      // Regular users can only see their own applications
      where.userId = session.user.id
    } else if ([UserRole.STAFF, UserRole.ADMIN].includes(session.user.role)) {
      // Staff and admin can see all applications
      if (searchParams.get("userId")) {
        where.userId = searchParams.get("userId")
      }
    }
    
    if (status) where.status = status as ApplicationStatus
    if (petId) where.petId = petId
    
    const skip = (page - 1) * limit
    
    const [applications, total] = await Promise.all([
      prisma.application.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            }
          },
          pet: {
            select: {
              id: true,
              name: true,
              species: true,
              breed: true,
              photos: {
                where: { isPrimary: true },
                take: 1,
              }
            }
          }
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.application.count({ where })
    ])
    
    const totalPages = Math.ceil(total / limit)
    
    return NextResponse.json({
      applications,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }
    })
    
  } catch (error) {
    console.error("Error fetching applications:", error)
    return NextResponse.json(
      { error: "Failed to fetch applications" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }
    
    const body = await request.json()
    
    // Validate input
    const validatedData = applicationSchema.parse(body)
    
    // Check if pet exists and is available
    const pet = await prisma.pet.findUnique({
      where: { id: validatedData.petId },
      select: { id: true, status: true, name: true }
    })
    
    if (!pet) {
      return NextResponse.json(
        { error: "Pet not found" },
        { status: 404 }
      )
    }
    
    if (pet.status !== "AVAILABLE") {
      return NextResponse.json(
        { error: "Pet is not available for adoption" },
        { status: 400 }
      )
    }
    
    // Check if user already has an active application for this pet
    const existingApplication = await prisma.application.findFirst({
      where: {
        userId: session.user.id,
        petId: validatedData.petId,
        status: {
          in: [
            ApplicationStatus.DRAFT,
            ApplicationStatus.SUBMITTED,
            ApplicationStatus.UNDER_REVIEW,
            ApplicationStatus.REFERENCE_CHECK,
            ApplicationStatus.HOME_VISIT_SCHEDULED,
            ApplicationStatus.HOME_VISIT_COMPLETED,
            ApplicationStatus.APPROVED,
            ApplicationStatus.WAITLISTED,
          ]
        }
      }
    })
    
    if (existingApplication) {
      return NextResponse.json(
        { error: "You already have an active application for this pet" },
        { status: 400 }
      )
    }
    
    // Check if application fee has been paid
    const applicationFeePayment = await prisma.payment.findFirst({
      where: {
        description: `Application fee for pet ${validatedData.petId}`,
        status: "COMPLETED"
      }
    })
    
    if (!applicationFeePayment) {
      return NextResponse.json(
        { error: "Application fee must be paid before submitting application" },
        { status: 400 }
      )
    }
    
    // Create application
    const application = await prisma.application.create({
      data: {
        ...validatedData,
        userId: session.user.id,
        status: ApplicationStatus.SUBMITTED,
        submittedAt: new Date(),
        personalReferences: validatedData.personalReferences as any,
        veterinaryReference: validatedData.veterinaryReference as any,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          }
        },
        pet: {
          select: {
            id: true,
            name: true,
            species: true,
            breed: true,
          }
        }
      }
    })
    
    // TODO: Send notification email to staff
    // TODO: Send confirmation email to applicant
    
    return NextResponse.json({
      message: "Application submitted successfully",
      application
    }, { status: 201 })
    
  } catch (error) {
    console.error("Error creating application:", error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: "Failed to submit application" },
      { status: 500 }
    )
  }
}

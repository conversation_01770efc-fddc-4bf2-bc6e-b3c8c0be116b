import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "20")
    const skip = (page - 1) * limit

    const [favorites, total] = await Promise.all([
      prisma.favorite.findMany({
        where: {
          userId: session.user.id
        },
        include: {
          pet: {
            include: {
              photos: {
                orderBy: { order: "asc" },
                take: 3,
              },
              organization: {
                select: {
                  name: true,
                  city: true,
                  state: true,
                }
              },
              _count: {
                select: {
                  applications: true,
                  favorites: true,
                }
              }
            }
          }
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.favorite.count({
        where: {
          userId: session.user.id
        }
      })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      favorites,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }
    })

  } catch (error) {
    console.error("Error fetching favorites:", error)
    return NextResponse.json(
      { error: "Failed to fetch favorites" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { petId } = body

    if (!petId) {
      return NextResponse.json(
        { error: "Pet ID is required" },
        { status: 400 }
      )
    }

    // Check if pet exists
    const pet = await prisma.pet.findUnique({
      where: { id: petId },
      select: { id: true, name: true }
    })

    if (!pet) {
      return NextResponse.json(
        { error: "Pet not found" },
        { status: 404 }
      )
    }

    // Check if already favorited
    const existingFavorite = await prisma.favorite.findUnique({
      where: {
        userId_petId: {
          userId: session.user.id,
          petId: petId
        }
      }
    })

    if (existingFavorite) {
      return NextResponse.json(
        { error: "Pet is already in favorites" },
        { status: 400 }
      )
    }

    // Create favorite
    const favorite = await prisma.favorite.create({
      data: {
        userId: session.user.id,
        petId: petId,
      },
      include: {
        pet: {
          select: {
            id: true,
            name: true,
            species: true,
            breed: true,
          }
        }
      }
    })

    return NextResponse.json({
      message: "Pet added to favorites",
      favorite
    }, { status: 201 })

  } catch (error) {
    console.error("Error adding favorite:", error)
    return NextResponse.json(
      { error: "Failed to add favorite" },
      { status: 500 }
    )
  }
}

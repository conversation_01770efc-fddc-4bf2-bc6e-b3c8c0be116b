"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Heart, 
  DollarSign, 
  Shield, 
  Users, 
  Stethoscope,
  Home,
  Car,
  Utensils,
  Gift,
  CreditCard,
  Check
} from "lucide-react"
import Link from "next/link"
import { toast } from "react-hot-toast"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { donationSchema, type DonationInput } from "@/lib/validations"

const DONATION_AMOUNTS = [25, 50, 100, 250, 500, 1000]

const DONATION_IMPACTS = [
  {
    amount: 25,
    icon: Utensils,
    title: "Feed a Pet",
    description: "Provides food for a pet for one week",
    color: "bg-green-100 text-green-600"
  },
  {
    amount: 50,
    icon: Stethoscope,
    title: "Basic Vet Care",
    description: "Covers basic medical examination and vaccines",
    color: "bg-blue-100 text-blue-600"
  },
  {
    amount: 100,
    icon: Home,
    title: "Shelter a Pet",
    description: "Provides shelter and care for a pet for one month",
    color: "bg-purple-100 text-purple-600"
  },
  {
    amount: 250,
    icon: Car,
    title: "Emergency Transport",
    description: "Covers emergency transport and urgent medical care",
    color: "bg-red-100 text-red-600"
  },
  {
    amount: 500,
    icon: Heart,
    title: "Complete Care Package",
    description: "Full medical care, spay/neuter, and microchipping",
    color: "bg-pink-100 text-pink-600"
  },
  {
    amount: 1000,
    icon: Users,
    title: "Save Multiple Lives",
    description: "Comprehensive care for multiple pets in need",
    color: "bg-orange-100 text-orange-600"
  }
]

export default function DonatePage() {
  const { data: session } = useSession()
  const [selectedAmount, setSelectedAmount] = useState<number | null>(null)
  const [customAmount, setCustomAmount] = useState("")
  const [loading, setLoading] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<DonationInput>({
    resolver: zodResolver(donationSchema),
    defaultValues: {
      isAnonymous: false,
      isMemorial: false,
      isRecurring: false,
    },
  })

  const watchedValues = watch()

  const handleAmountSelect = (amount: number) => {
    setSelectedAmount(amount)
    setCustomAmount("")
    setValue("amount", amount)
  }

  const handleCustomAmount = (value: string) => {
    setCustomAmount(value)
    setSelectedAmount(null)
    const numValue = parseFloat(value)
    if (!isNaN(numValue) && numValue > 0) {
      setValue("amount", numValue)
    }
  }

  const onSubmit = async (data: DonationInput) => {
    setLoading(true)
    try {
      const response = await fetch("/api/donations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        const result = await response.json()
        // Redirect to Stripe Checkout
        if (result.checkoutUrl) {
          window.location.href = result.checkoutUrl
        } else {
          toast.success("Donation processed successfully!")
        }
      } else {
        const errorData = await response.json()
        toast.error(errorData.error || "Failed to process donation")
      }
    } catch (error) {
      toast.error("Something went wrong. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const getCurrentAmount = () => {
    if (customAmount) {
      const num = parseFloat(customAmount)
      return !isNaN(num) && num > 0 ? num : 0
    }
    return selectedAmount || 0
  }

  const getImpactForAmount = (amount: number) => {
    return DONATION_IMPACTS.find(impact => amount >= impact.amount) || DONATION_IMPACTS[0]
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100">
      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-red-100 rounded-full mb-6">
            <Heart className="h-10 w-10 text-red-600" />
          </div>
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Help Save Lives
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Your donation directly impacts the lives of animals in need. Every dollar helps provide 
            food, medical care, shelter, and love to pets waiting for their forever homes.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8">
            {/* Donation Form */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Gift className="h-5 w-5 mr-2" />
                  Make a Donation
                </CardTitle>
                <CardDescription>
                  Choose your donation amount and help us save more lives
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  {/* Amount Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Donation Amount *
                    </label>
                    <div className="grid grid-cols-3 gap-3 mb-4">
                      {DONATION_AMOUNTS.map((amount) => (
                        <button
                          key={amount}
                          type="button"
                          onClick={() => handleAmountSelect(amount)}
                          className={`p-3 border rounded-lg text-center transition-colors ${
                            selectedAmount === amount
                              ? 'border-red-500 bg-red-50 text-red-700'
                              : 'border-gray-300 hover:border-gray-400'
                          }`}
                        >
                          ${amount}
                        </button>
                      ))}
                    </div>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        type="number"
                        min="1"
                        step="0.01"
                        placeholder="Custom amount"
                        value={customAmount}
                        onChange={(e) => handleCustomAmount(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    {errors.amount && (
                      <p className="text-red-500 text-sm mt-1">{errors.amount.message}</p>
                    )}
                  </div>

                  {/* Donation Type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Donation Type
                    </label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          {...register("isRecurring", { 
                            setValueAs: (value) => value === "true" 
                          })}
                          value="false"
                          className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm">One-time donation</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          {...register("isRecurring", { 
                            setValueAs: (value) => value === "true" 
                          })}
                          value="true"
                          className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm">Monthly recurring donation</span>
                      </label>
                    </div>
                  </div>

                  {/* Donor Information */}
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        {...register("isAnonymous")}
                        className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                      />
                      <label className="ml-2 text-sm text-gray-700">
                        Make this donation anonymous
                      </label>
                    </div>

                    {!watchedValues.isAnonymous && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Name
                          </label>
                          <Input
                            {...register("donorName")}
                            placeholder="Your name"
                            defaultValue={session?.user?.name || ""}
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Email
                          </label>
                          <Input
                            type="email"
                            {...register("donorEmail")}
                            placeholder="Your email"
                            defaultValue={session?.user?.email || ""}
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Memorial/Tribute */}
                  <div>
                    <div className="flex items-center mb-3">
                      <input
                        type="checkbox"
                        {...register("isMemorial")}
                        className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                      />
                      <label className="ml-2 text-sm text-gray-700">
                        This is a memorial or tribute donation
                      </label>
                    </div>

                    {watchedValues.isMemorial && (
                      <div className="space-y-3 pl-6 border-l-2 border-gray-200">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            In memory/honor of
                          </label>
                          <Input
                            {...register("memorialName")}
                            placeholder="Name"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Memorial message
                          </label>
                          <textarea
                            {...register("memorialMessage")}
                            className="w-full p-2 border border-gray-300 rounded-md"
                            rows={3}
                            placeholder="Optional message"
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Designation */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Designation (Optional)
                    </label>
                    <select
                      {...register("designation")}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="">General Fund</option>
                      <option value="medical">Medical Care</option>
                      <option value="food">Food & Supplies</option>
                      <option value="transport">Transportation</option>
                      <option value="facility">Facility Improvements</option>
                      <option value="emergency">Emergency Fund</option>
                    </select>
                  </div>

                  {/* Submit Button */}
                  <Button
                    type="submit"
                    disabled={loading || getCurrentAmount() === 0}
                    className="w-full bg-red-600 hover:bg-red-700"
                    size="lg"
                  >
                    {loading ? (
                      "Processing..."
                    ) : (
                      <>
                        <CreditCard className="h-4 w-4 mr-2" />
                        Donate ${getCurrentAmount().toFixed(2)}
                      </>
                    )}
                  </Button>

                  {/* Security Notice */}
                  <div className="flex items-center justify-center text-sm text-gray-600">
                    <Shield className="h-4 w-4 mr-2" />
                    Secure payment powered by Stripe
                  </div>
                </form>
              </CardContent>
            </Card>

            {/* Impact Information */}
            <div className="space-y-6">
              {/* Current Impact */}
              {getCurrentAmount() > 0 && (
                <Card className="border-red-200 bg-red-50">
                  <CardHeader>
                    <CardTitle className="text-red-800">Your Impact</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {(() => {
                      const impact = getImpactForAmount(getCurrentAmount())
                      const Icon = impact.icon
                      return (
                        <div className="flex items-center">
                          <div className={`p-3 rounded-full ${impact.color} mr-4`}>
                            <Icon className="h-6 w-6" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-red-800">{impact.title}</h3>
                            <p className="text-sm text-red-700">{impact.description}</p>
                          </div>
                        </div>
                      )
                    })()}
                  </CardContent>
                </Card>
              )}

              {/* Impact Examples */}
              <Card>
                <CardHeader>
                  <CardTitle>How Your Donation Helps</CardTitle>
                  <CardDescription>
                    See the direct impact of different donation amounts
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {DONATION_IMPACTS.slice(0, 4).map((impact) => {
                      const Icon = impact.icon
                      return (
                        <div key={impact.amount} className="flex items-center">
                          <div className={`p-2 rounded-lg ${impact.color} mr-3`}>
                            <Icon className="h-4 w-4" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <span className="font-medium">${impact.amount}</span>
                              <span className="text-sm text-gray-600">{impact.title}</span>
                            </div>
                            <p className="text-xs text-gray-500">{impact.description}</p>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>

              {/* Tax Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Check className="h-5 w-5 mr-2 text-green-600" />
                    Tax Deductible
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">
                    We are a 501(c)(3) non-profit organization. Your donation is tax-deductible 
                    to the full extent allowed by law. You will receive a receipt for your records.
                  </p>
                </CardContent>
              </Card>

              {/* Other Ways to Help */}
              <Card>
                <CardHeader>
                  <CardTitle>Other Ways to Help</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <Link href="/volunteer" className="block">
                      <Button variant="outline" className="w-full justify-start">
                        <Users className="h-4 w-4 mr-2" />
                        Volunteer Your Time
                      </Button>
                    </Link>
                    <Link href="/foster" className="block">
                      <Button variant="outline" className="w-full justify-start">
                        <Home className="h-4 w-4 mr-2" />
                        Become a Foster Family
                      </Button>
                    </Link>
                    <Link href="/pets" className="block">
                      <Button variant="outline" className="w-full justify-start">
                        <Heart className="h-4 w-4 mr-2" />
                        Adopt a Pet
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Search, Filter, Heart, MapPin, Calendar, Info } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

interface Pet {
  id: string
  name: string
  species: string
  breed: string
  age: number
  size: string
  gender: string
  description: string
  photos: Array<{ url: string; isPrimary: boolean }>
  organization: {
    name: string
    city: string
    state: string
  }
  adoptionFee: number
  goodWithKids: boolean
  goodWithDogs: boolean
  goodWithCats: boolean
  _count: {
    favorites: number
    applications: number
  }
}

interface PetsResponse {
  pets: Pet[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export default function PetsPage() {
  const searchParams = useSearchParams()
  const [pets, setPets] = useState<Pet[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState<PetsResponse['pagination'] | null>(null)
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '')
  const [filters, setFilters] = useState({
    species: searchParams.get('species') || '',
    size: searchParams.get('size') || '',
    age: searchParams.get('age') || '',
    gender: searchParams.get('gender') || '',
    goodWithKids: searchParams.get('goodWithKids') || '',
    goodWithDogs: searchParams.get('goodWithDogs') || '',
    goodWithCats: searchParams.get('goodWithCats') || '',
  })
  const [showFilters, setShowFilters] = useState(false)

  const fetchPets = async (page = 1) => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '12',
        ...(searchTerm && { search: searchTerm }),
        ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value))
      })

      const response = await fetch(`/api/pets?${params}`)
      if (response.ok) {
        const data: PetsResponse = await response.json()
        setPets(data.pets)
        setPagination(data.pagination)
      }
    } catch (error) {
      console.error('Error fetching pets:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPets()
  }, [searchTerm, filters])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchPets(1)
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const clearFilters = () => {
    setFilters({
      species: '',
      size: '',
      age: '',
      gender: '',
      goodWithKids: '',
      goodWithDogs: '',
      goodWithCats: '',
    })
    setSearchTerm('')
  }

  const formatAge = (ageInMonths: number) => {
    if (ageInMonths < 12) {
      return `${ageInMonths} month${ageInMonths !== 1 ? 's' : ''} old`
    } else {
      const years = Math.floor(ageInMonths / 12)
      const months = ageInMonths % 12
      if (months === 0) {
        return `${years} year${years !== 1 ? 's' : ''} old`
      } else {
        return `${years}y ${months}m old`
      }
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
          Find Your Perfect Pet
        </h1>
        <p className="text-xl text-gray-600">
          Browse our available pets and find your new best friend.
        </p>
      </div>

      {/* Search and Filters */}
      <div className="mb-8 space-y-4">
        {/* Search Bar */}
        <form onSubmit={handleSearch} className="flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              type="text"
              placeholder="Search by name, breed, or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button type="submit">Search</Button>
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
        </form>

        {/* Filters Panel */}
        {showFilters && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Filter Options</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Species</label>
                  <select
                    value={filters.species}
                    onChange={(e) => handleFilterChange('species', e.target.value)}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="">All</option>
                    <option value="Dog">Dogs</option>
                    <option value="Cat">Cats</option>
                    <option value="Rabbit">Rabbits</option>
                    <option value="Bird">Birds</option>
                    <option value="Other">Other</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Size</label>
                  <select
                    value={filters.size}
                    onChange={(e) => handleFilterChange('size', e.target.value)}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="">All</option>
                    <option value="EXTRA_SMALL">Extra Small</option>
                    <option value="SMALL">Small</option>
                    <option value="MEDIUM">Medium</option>
                    <option value="LARGE">Large</option>
                    <option value="EXTRA_LARGE">Extra Large</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Age</label>
                  <select
                    value={filters.age}
                    onChange={(e) => handleFilterChange('age', e.target.value)}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="">All</option>
                    <option value="puppy">Puppy/Kitten</option>
                    <option value="young">Young</option>
                    <option value="adult">Adult</option>
                    <option value="senior">Senior</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Gender</label>
                  <select
                    value={filters.gender}
                    onChange={(e) => handleFilterChange('gender', e.target.value)}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="">All</option>
                    <option value="MALE">Male</option>
                    <option value="FEMALE">Female</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Good with Kids</label>
                  <select
                    value={filters.goodWithKids}
                    onChange={(e) => handleFilterChange('goodWithKids', e.target.value)}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="">All</option>
                    <option value="true">Yes</option>
                    <option value="false">No</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Good with Dogs</label>
                  <select
                    value={filters.goodWithDogs}
                    onChange={(e) => handleFilterChange('goodWithDogs', e.target.value)}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="">All</option>
                    <option value="true">Yes</option>
                    <option value="false">No</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Good with Cats</label>
                  <select
                    value={filters.goodWithCats}
                    onChange={(e) => handleFilterChange('goodWithCats', e.target.value)}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="">All</option>
                    <option value="true">Yes</option>
                    <option value="false">No</option>
                  </select>
                </div>
              </div>

              <div className="mt-4 flex gap-2">
                <Button onClick={clearFilters} variant="outline">
                  Clear All Filters
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Results Count */}
      {pagination && (
        <div className="mb-6">
          <p className="text-gray-600">
            Showing {pets.length} of {pagination.total} pets
          </p>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <div className="aspect-square bg-gray-200 rounded-t-lg"></div>
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pet Grid */}
      {!loading && pets.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {pets.map((pet) => (
            <Link key={pet.id} href={`/pets/${pet.id}`}>
              <Card className="h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 cursor-pointer">
                <div className="relative aspect-square overflow-hidden rounded-t-lg">
                  {pet.photos.length > 0 ? (
                    <Image
                      src={pet.photos.find(p => p.isPrimary)?.url || pet.photos[0].url}
                      alt={pet.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                      <span className="text-4xl">🐾</span>
                    </div>
                  )}
                  
                  {/* Favorite Button */}
                  <button className="absolute top-2 right-2 p-2 bg-white/80 rounded-full hover:bg-white transition-colors">
                    <Heart className="h-4 w-4" />
                  </button>

                  {/* Quick Info Badges */}
                  <div className="absolute bottom-2 left-2 flex gap-1">
                    {pet.goodWithKids && (
                      <span className="px-2 py-1 bg-green-500 text-white text-xs rounded-full">
                        Kids ✓
                      </span>
                    )}
                    {pet.goodWithDogs && (
                      <span className="px-2 py-1 bg-blue-500 text-white text-xs rounded-full">
                        Dogs ✓
                      </span>
                    )}
                  </div>
                </div>

                <CardHeader className="pb-2">
                  <CardTitle className="text-xl">{pet.name}</CardTitle>
                  <CardDescription>
                    {pet.breed} • {formatAge(pet.age)} • {pet.gender}
                  </CardDescription>
                </CardHeader>

                <CardContent className="pt-0">
                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-3 w-3 mr-1" />
                      {pet.organization.city}, {pet.organization.state}
                    </div>
                    
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {pet.description}
                    </p>

                    {pet.adoptionFee && (
                      <div className="text-lg font-semibold text-green-600">
                        ${pet.adoptionFee} adoption fee
                      </div>
                    )}

                    <div className="flex justify-between items-center text-xs text-gray-500">
                      <span>{pet._count.favorites} favorites</span>
                      <span>{pet._count.applications} applications</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      )}

      {/* No Results */}
      {!loading && pets.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🐾</div>
          <h3 className="text-2xl font-semibold text-gray-900 mb-2">No pets found</h3>
          <p className="text-gray-600 mb-4">
            Try adjusting your search criteria or check back later for new arrivals.
          </p>
          <Button onClick={clearFilters}>Clear Filters</Button>
        </div>
      )}

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="mt-8 flex justify-center items-center space-x-2">
          <Button
            variant="outline"
            disabled={!pagination.hasPrev}
            onClick={() => fetchPets(pagination.page - 1)}
          >
            Previous
          </Button>
          
          <span className="px-4 py-2 text-sm text-gray-600">
            Page {pagination.page} of {pagination.totalPages}
          </span>
          
          <Button
            variant="outline"
            disabled={!pagination.hasNext}
            onClick={() => fetchPets(pagination.page + 1)}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}

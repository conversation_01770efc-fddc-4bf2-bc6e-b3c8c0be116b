"use client"

import { useState, useEffect } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { UserRole } from "@prisma/client"
import {
  BarChart3,
  Download,
  Calendar,
  TrendingUp,
  Users,
  Heart,
  DollarSign,
  FileText,
  PieChart,
  Activity,
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import toast from "react-hot-toast"

interface ReportData {
  adoptions: {
    total: number
    thisMonth: number
    lastMonth: number
    monthlyTrend: number
  }
  applications: {
    total: number
    pending: number
    approved: number
    rejected: number
  }
  pets: {
    total: number
    available: number
    adopted: number
    inFoster: number
  }
  volunteers: {
    total: number
    active: number
    totalHours: number
  }
  donations: {
    total: number
    thisMonth: number
    averageAmount: number
  }
  monthlyStats: Array<{
    month: string
    adoptions: number
    applications: number
    donations: number
  }>
}

export default function ReportsPage() {
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState("12months")
  const [selectedReport, setSelectedReport] = useState("overview")

  const fetchReportData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/reports?period=${selectedPeriod}&type=${selectedReport}`)
      
      if (!response.ok) {
        throw new Error("Failed to fetch report data")
      }

      const data = await response.json()
      setReportData(data)
    } catch (error) {
      console.error("Error fetching report data:", error)
      toast.error("Failed to fetch report data")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchReportData()
  }, [selectedPeriod, selectedReport])

  const handleExportReport = async (format: 'pdf' | 'csv') => {
    try {
      const response = await fetch(`/api/admin/reports/export?format=${format}&period=${selectedPeriod}&type=${selectedReport}`)
      
      if (!response.ok) {
        throw new Error("Failed to export report")
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = `report-${selectedReport}-${selectedPeriod}.${format}`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      
      toast.success(`Report exported as ${format.toUpperCase()}`)
    } catch (error) {
      console.error("Error exporting report:", error)
      toast.error("Failed to export report")
    }
  }

  const calculatePercentageChange = (current: number, previous: number) => {
    if (previous === 0) return current > 0 ? 100 : 0
    return ((current - previous) / previous) * 100
  }

  return (
    <AdminLayout
      title="Reports & Analytics"
      description="View comprehensive reports and analytics"
      requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
    >
      <div className="space-y-6">
        {/* Header Controls */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center space-x-2">
            <BarChart3 className="h-6 w-6 text-gray-600" />
            <span className="text-lg font-medium text-gray-900">
              Analytics Dashboard
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1month">Last Month</SelectItem>
                <SelectItem value="3months">Last 3 Months</SelectItem>
                <SelectItem value="6months">Last 6 Months</SelectItem>
                <SelectItem value="12months">Last 12 Months</SelectItem>
                <SelectItem value="ytd">Year to Date</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedReport} onValueChange={setSelectedReport}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Select report" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="overview">Overview</SelectItem>
                <SelectItem value="adoptions">Adoptions</SelectItem>
                <SelectItem value="applications">Applications</SelectItem>
                <SelectItem value="volunteers">Volunteers</SelectItem>
                <SelectItem value="donations">Donations</SelectItem>
                <SelectItem value="pets">Pet Management</SelectItem>
              </SelectContent>
            </Select>

            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handleExportReport('csv')}
            >
              <Download className="h-4 w-4 mr-2" />
              CSV
            </Button>
            
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handleExportReport('pdf')}
            >
              <Download className="h-4 w-4 mr-2" />
              PDF
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="animate-pulse">
                    <div className="h-8 bg-gray-200 rounded mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : reportData ? (
          <>
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Heart className="h-8 w-8 text-red-500" />
                    <div className="ml-4">
                      <p className="text-2xl font-bold">{reportData.adoptions.total}</p>
                      <p className="text-sm text-gray-600">Total Adoptions</p>
                      <div className="flex items-center mt-1">
                        <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                        <span className="text-xs text-green-600">
                          {reportData.adoptions.monthlyTrend > 0 ? '+' : ''}{reportData.adoptions.monthlyTrend.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <FileText className="h-8 w-8 text-blue-500" />
                    <div className="ml-4">
                      <p className="text-2xl font-bold">{reportData.applications.total}</p>
                      <p className="text-sm text-gray-600">Applications</p>
                      <p className="text-xs text-gray-500">
                        {reportData.applications.pending} pending
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Users className="h-8 w-8 text-green-500" />
                    <div className="ml-4">
                      <p className="text-2xl font-bold">{reportData.volunteers.active}</p>
                      <p className="text-sm text-gray-600">Active Volunteers</p>
                      <p className="text-xs text-gray-500">
                        {reportData.volunteers.totalHours} hours logged
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <DollarSign className="h-8 w-8 text-purple-500" />
                    <div className="ml-4">
                      <p className="text-2xl font-bold">${reportData.donations.total.toLocaleString()}</p>
                      <p className="text-sm text-gray-600">Total Donations</p>
                      <p className="text-xs text-gray-500">
                        Avg: ${reportData.donations.averageAmount.toFixed(0)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Pet Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Activity className="h-8 w-8 text-blue-500" />
                    <div className="ml-4">
                      <p className="text-2xl font-bold">{reportData.pets.total}</p>
                      <p className="text-sm text-gray-600">Total Pets</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Heart className="h-8 w-8 text-green-500" />
                    <div className="ml-4">
                      <p className="text-2xl font-bold">{reportData.pets.available}</p>
                      <p className="text-sm text-gray-600">Available</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Users className="h-8 w-8 text-purple-500" />
                    <div className="ml-4">
                      <p className="text-2xl font-bold">{reportData.pets.adopted}</p>
                      <p className="text-sm text-gray-600">Adopted</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Users className="h-8 w-8 text-orange-500" />
                    <div className="ml-4">
                      <p className="text-2xl font-bold">{reportData.pets.inFoster}</p>
                      <p className="text-sm text-gray-600">In Foster Care</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Monthly Trends Chart Placeholder */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PieChart className="h-5 w-5 mr-2" />
                  Monthly Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">Chart visualization would be implemented here</p>
                    <p className="text-sm text-gray-400">
                      Integration with charting library (Chart.js, Recharts, etc.)
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Reports */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Application Status Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Pending Review</span>
                      <span className="font-medium">{reportData.applications.pending}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Approved</span>
                      <span className="font-medium text-green-600">{reportData.applications.approved}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Rejected</span>
                      <span className="font-medium text-red-600">{reportData.applications.rejected}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Monthly Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">This Month Adoptions</span>
                      <span className="font-medium">{reportData.adoptions.thisMonth}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Last Month Adoptions</span>
                      <span className="font-medium">{reportData.adoptions.lastMonth}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Monthly Donations</span>
                      <span className="font-medium">${reportData.donations.thisMonth.toLocaleString()}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No report data available</p>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
 "use client"

import { useState, useEffect } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { UserRole, FosterStatus, PetSize } from "@prisma/client"
import {
  Search,
  Plus,
  Edit,
  Eye,
  Filter,
  Download,
  MoreHorizontal,
  Home,
  Heart,
  Users,
  CheckCircle,
  AlertCircle,
  Clock,
  UserCheck,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import Link from "next/link"
import toast from "react-hot-toast"

interface FosterProfile {
  id: string
  userId: string
  status: FosterStatus
  maxPets: number
  currentPets: number
  preferredSpecies: string[]
  preferredAges: string[]
  preferredSizes: PetSize[]
  fosterExperience: string | null
  specialNeedsExperience: string | null
  emergencyFoster: boolean
  longTermFoster: boolean
  shortTermFoster: boolean
  hasYard: boolean | null
  yardFenced: boolean | null
  hasOtherPets: boolean | null
  otherPetsDetails: string | null
  hasChildren: boolean | null
  childrenAges: string | null
  approved: boolean
  approvedDate: string | null
  approvedBy: string | null
  backgroundCheckDate: string | null
  homeVisitDate: string | null
  trainingCompleted: boolean
  trainingDate: string | null
  certificationExpiry: string | null
  createdAt: string
  updatedAt: string
  user: {
    id: string
    name: string | null
    email: string
    phone: string | null
    firstName: string | null
    lastName: string | null
  }
  _count: {
    placements: number
  }
}

interface FosterResponse {
  fosters: FosterProfile[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export default function FosterManagement() {
  const [fosters, setFosters] = useState<FosterProfile[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState<FosterResponse['pagination'] | null>(null)

  const fetchFosters = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        ...(searchTerm && { search: searchTerm }),
        ...(selectedStatus !== "all" && { status: selectedStatus }),
      })

      const response = await fetch(`/api/admin/foster?${params}`)
      
      if (!response.ok) {
        throw new Error("Failed to fetch foster families")
      }

      const data: FosterResponse = await response.json()
      setFosters(data.fosters)
      setPagination(data.pagination)
    } catch (error) {
      console.error("Error fetching foster families:", error)
      toast.error("Failed to fetch foster families")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchFosters()
  }, [currentPage, searchTerm, selectedStatus])

  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setCurrentPage(1)
  }

  const handleStatusFilter = (value: string) => {
    setSelectedStatus(value)
    setCurrentPage(1)
  }

  const handleUpdateStatus = async (fosterId: string, newStatus: FosterStatus) => {
    try {
      const response = await fetch(`/api/admin/foster/${fosterId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (!response.ok) {
        throw new Error("Failed to update foster status")
      }

      toast.success("Foster status updated successfully")
      fetchFosters()
    } catch (error) {
      console.error("Error updating foster status:", error)
      toast.error("Failed to update foster status")
    }
  }

  const handleApprove = async (fosterId: string) => {
    try {
      const response = await fetch(`/api/admin/foster/${fosterId}/approve`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (!response.ok) {
        throw new Error("Failed to approve foster family")
      }

      toast.success("Foster family approved successfully")
      fetchFosters()
    } catch (error) {
      console.error("Error approving foster family:", error)
      toast.error("Failed to approve foster family")
    }
  }

  const getStatusBadgeColor = (status: FosterStatus) => {
    switch (status) {
      case FosterStatus.ACTIVE:
        return "bg-green-100 text-green-800"
      case FosterStatus.INACTIVE:
        return "bg-gray-100 text-gray-800"
      case FosterStatus.EMERGENCY_ONLY:
        return "bg-orange-100 text-orange-800"
      case FosterStatus.FULL_CAPACITY:
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: FosterStatus) => {
    switch (status) {
      case FosterStatus.ACTIVE:
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case FosterStatus.INACTIVE:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
      case FosterStatus.EMERGENCY_ONLY:
        return <Clock className="h-4 w-4 text-orange-500" />
      case FosterStatus.FULL_CAPACITY:
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <AdminLayout
      title="Foster Family Management"
      description="Manage foster families, applications, and placements"
      requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center space-x-2">
            <Home className="h-6 w-6 text-gray-600" />
            <span className="text-lg font-medium text-gray-900">
              {pagination?.total || 0} Foster Families
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Link href="/admin/foster/new">
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Foster Family
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">
                    {fosters.filter(f => f.status === FosterStatus.ACTIVE).length}
                  </p>
                  <p className="text-sm text-gray-600">Active Fosters</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <UserCheck className="h-8 w-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">
                    {fosters.filter(f => f.approved).length}
                  </p>
                  <p className="text-sm text-gray-600">Approved Families</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Heart className="h-8 w-8 text-purple-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">
                    {fosters.reduce((total, f) => total + f.currentPets, 0)}
                  </p>
                  <p className="text-sm text-gray-600">Pets in Foster</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-orange-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">
                    {fosters.filter(f => f.emergencyFoster).length}
                  </p>
                  <p className="text-sm text-gray-600">Emergency Fosters</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search foster families by name, email..."
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <Select value={selectedStatus} onValueChange={handleStatusFilter}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value={FosterStatus.ACTIVE}>Active</SelectItem>
                  <SelectItem value={FosterStatus.INACTIVE}>Inactive</SelectItem>
                  <SelectItem value={FosterStatus.EMERGENCY_ONLY}>Emergency Only</SelectItem>
                  <SelectItem value={FosterStatus.FULL_CAPACITY}>Full Capacity</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Foster Families List */}
        <Card>
          <CardHeader>
            <CardTitle>Foster Families</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-24 bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>
            ) : fosters.length === 0 ? (
              <div className="text-center py-8">
                <Home className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No foster families found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {fosters.map((foster) => (
                  <div
                    key={foster.id}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-purple-700">
                          {foster.user.name?.charAt(0).toUpperCase() || foster.user.email.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="font-medium text-gray-900">
                            {foster.user.name || `${foster.user.firstName || ''} ${foster.user.lastName || ''}`.trim() || 'No name'}
                          </p>
                          {getStatusIcon(foster.status)}
                          <Badge className={getStatusBadgeColor(foster.status)}>
                            {foster.status.replace('_', ' ')}
                          </Badge>
                          {foster.approved && (
                            <Badge className="bg-green-100 text-green-800">
                              Approved
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-500">{foster.user.email}</p>
                        <div className="flex items-center space-x-4 text-xs text-gray-400 mt-1">
                          <span>{foster.currentPets}/{foster.maxPets} pets</span>
                          <span>{foster._count.placements} total placements</span>
                          {foster.emergencyFoster && (
                            <span className="text-orange-600">Emergency Foster</span>
                          )}
                          {foster.trainingCompleted && (
                            <span className="text-green-600">✓ Training Complete</span>
                          )}
                        </div>
                        {foster.preferredSpecies.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {foster.preferredSpecies.slice(0, 3).map((species, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {species}
                              </Badge>
                            ))}
                            {foster.preferredSpecies.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{foster.preferredSpecies.length - 3} more
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/foster/${foster.id}`}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/foster/${foster.id}/edit`}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Profile
                          </Link>
                        </DropdownMenuItem>
                        {!foster.approved && (
                          <DropdownMenuItem
                            onClick={() => handleApprove(foster.id)}
                          >
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Approve
                          </DropdownMenuItem>
                        )}
                        {foster.status === FosterStatus.INACTIVE && (
                          <DropdownMenuItem
                            onClick={() => handleUpdateStatus(foster.id, FosterStatus.ACTIVE)}
                          >
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Activate
                          </DropdownMenuItem>
                        )}
                        {foster.status === FosterStatus.ACTIVE && foster.currentPets >= foster.maxPets && (
                          <DropdownMenuItem
                            onClick={() => handleUpdateStatus(foster.id, FosterStatus.FULL_CAPACITY)}
                          >
                            <AlertCircle className="h-4 w-4 mr-2" />
                            Mark Full Capacity
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {pagination && pagination.totalPages > 1 && (
              <div className="flex items-center justify-between mt-6">
                <p className="text-sm text-gray-500">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                  {pagination.total} foster families
                </p>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(pagination.page - 1)}
                    disabled={!pagination.hasPrev}
                  >
                    Previous
                  </Button>
                  <span className="text-sm text-gray-500">
                    Page {pagination.page} of {pagination.totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(pagination.page + 1)}
                    disabled={!pagination.hasNext}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
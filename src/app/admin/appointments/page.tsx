"use client"

import { useState, useEffect } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { UserRole, AppointmentType, AppointmentStatus } from "@prisma/client"
import {
  Search,
  Plus,
  Edit,
  Eye,
  Filter,
  Download,
  MoreHorizontal,
  Calendar,
  Clock,
  User,
  CheckCircle,
  AlertCircle,
  XCircle,
  CalendarDays,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import Link from "next/link"
import toast from "react-hot-toast"

interface Appointment {
  id: string
  userId: string
  petId: string | null
  applicationId: string | null
  type: AppointmentType
  status: AppointmentStatus
  title: string
  description: string | null
  scheduledDate: string
  scheduledTime: string
  duration: number
  location: string | null
  address: string | null
  isVirtual: boolean
  meetingLink: string | null
  assignedStaff: string | null
  notes: string | null
  completionNotes: string | null
  reminderSent: boolean
  reminderSentAt: string | null
  createdAt: string
  updatedAt: string
  user: {
    id: string
    name: string | null
    email: string
    phone: string | null
  }
  application?: {
    id: string
    pet: {
      name: string
      species: string
    }
  }
}

interface AppointmentsResponse {
  appointments: Appointment[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export default function AppointmentsManagement() {
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState<string>("all")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState<AppointmentsResponse['pagination'] | null>(null)

  const fetchAppointments = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        ...(searchTerm && { search: searchTerm }),
        ...(selectedType !== "all" && { type: selectedType }),
        ...(selectedStatus !== "all" && { status: selectedStatus }),
      })

      const response = await fetch(`/api/admin/appointments?${params}`)
      
      if (!response.ok) {
        throw new Error("Failed to fetch appointments")
      }

      const data: AppointmentsResponse = await response.json()
      setAppointments(data.appointments)
      setPagination(data.pagination)
    } catch (error) {
      console.error("Error fetching appointments:", error)
      toast.error("Failed to fetch appointments")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAppointments()
  }, [currentPage, searchTerm, selectedType, selectedStatus])

  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setCurrentPage(1)
  }

  const handleTypeFilter = (value: string) => {
    setSelectedType(value)
    setCurrentPage(1)
  }

  const handleStatusFilter = (value: string) => {
    setSelectedStatus(value)
    setCurrentPage(1)
  }

  const handleUpdateStatus = async (appointmentId: string, newStatus: AppointmentStatus) => {
    try {
      const response = await fetch(`/api/admin/appointments/${appointmentId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (!response.ok) {
        throw new Error("Failed to update appointment status")
      }

      toast.success("Appointment status updated successfully")
      fetchAppointments()
    } catch (error) {
      console.error("Error updating appointment status:", error)
      toast.error("Failed to update appointment status")
    }
  }

  const getStatusBadgeColor = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.SCHEDULED:
        return "bg-blue-100 text-blue-800"
      case AppointmentStatus.CONFIRMED:
        return "bg-green-100 text-green-800"
      case AppointmentStatus.COMPLETED:
        return "bg-purple-100 text-purple-800"
      case AppointmentStatus.CANCELLED:
        return "bg-red-100 text-red-800"
      case AppointmentStatus.NO_SHOW:
        return "bg-orange-100 text-orange-800"
      case AppointmentStatus.RESCHEDULED:
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.SCHEDULED:
        return <Clock className="h-4 w-4 text-blue-500" />
      case AppointmentStatus.CONFIRMED:
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case AppointmentStatus.COMPLETED:
        return <CheckCircle className="h-4 w-4 text-purple-500" />
      case AppointmentStatus.CANCELLED:
        return <XCircle className="h-4 w-4 text-red-500" />
      case AppointmentStatus.NO_SHOW:
        return <AlertCircle className="h-4 w-4 text-orange-500" />
      case AppointmentStatus.RESCHEDULED:
        return <Calendar className="h-4 w-4 text-yellow-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getTypeBadgeColor = (type: AppointmentType) => {
    switch (type) {
      case AppointmentType.MEET_GREET:
        return "bg-blue-100 text-blue-800"
      case AppointmentType.HOME_VISIT:
        return "bg-green-100 text-green-800"
      case AppointmentType.ADOPTION_PICKUP:
        return "bg-purple-100 text-purple-800"
      case AppointmentType.VETERINARY:
        return "bg-red-100 text-red-800"
      case AppointmentType.TRAINING:
        return "bg-orange-100 text-orange-800"
      case AppointmentType.VOLUNTEER_ORIENTATION:
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const formatDateTime = (date: string, time: string) => {
    const appointmentDate = new Date(date)
    return `${appointmentDate.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })} at ${time}`
  }

  const isUpcoming = (date: string) => {
    return new Date(date) > new Date()
  }

  return (
    <AdminLayout
      title="Appointments Management"
      description="Manage appointments, visits, and meetings"
      requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center space-x-2">
            <Calendar className="h-6 w-6 text-gray-600" />
            <span className="text-lg font-medium text-gray-900">
              {pagination?.total || 0} Appointments
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Link href="/admin/appointments/new">
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Schedule Appointment
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">
                    {appointments.filter(a => a.status === AppointmentStatus.SCHEDULED).length}
                  </p>
                  <p className="text-sm text-gray-600">Scheduled</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">
                    {appointments.filter(a => a.status === AppointmentStatus.CONFIRMED).length}
                  </p>
                  <p className="text-sm text-gray-600">Confirmed</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CalendarDays className="h-8 w-8 text-purple-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">
                    {appointments.filter(a => isUpcoming(a.scheduledDate)).length}
                  </p>
                  <p className="text-sm text-gray-600">Upcoming</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertCircle className="h-8 w-8 text-orange-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">
                    {appointments.filter(a => a.status === AppointmentStatus.NO_SHOW).length}
                  </p>
                  <p className="text-sm text-gray-600">No Shows</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by user name, title, or notes..."
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <Select value={selectedType} onValueChange={handleTypeFilter}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value={AppointmentType.MEET_GREET}>Meet & Greet</SelectItem>
                  <SelectItem value={AppointmentType.HOME_VISIT}>Home Visit</SelectItem>
                  <SelectItem value={AppointmentType.ADOPTION_PICKUP}>Adoption Pickup</SelectItem>
                  <SelectItem value={AppointmentType.VETERINARY}>Veterinary</SelectItem>
                  <SelectItem value={AppointmentType.TRAINING}>Training</SelectItem>
                  <SelectItem value={AppointmentType.VOLUNTEER_ORIENTATION}>Volunteer Orientation</SelectItem>
                </SelectContent>
              </Select>

              <Select value={selectedStatus} onValueChange={handleStatusFilter}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value={AppointmentStatus.SCHEDULED}>Scheduled</SelectItem>
                  <SelectItem value={AppointmentStatus.CONFIRMED}>Confirmed</SelectItem>
                  <SelectItem value={AppointmentStatus.COMPLETED}>Completed</SelectItem>
                  <SelectItem value={AppointmentStatus.CANCELLED}>Cancelled</SelectItem>
                  <SelectItem value={AppointmentStatus.NO_SHOW}>No Show</SelectItem>
                  <SelectItem value={AppointmentStatus.RESCHEDULED}>Rescheduled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Appointments List */}
        <Card>
          <CardHeader>
            <CardTitle>Appointments</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-20 bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>
            ) : appointments.length === 0 ? (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No appointments found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {appointments.map((appointment) => (
                  <div
                    key={appointment.id}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                        {getStatusIcon(appointment.status)}
                      </div>
                      
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="font-medium text-gray-900">
                            {appointment.title}
                          </p>
                          <Badge className={getTypeBadgeColor(appointment.type)}>
                            {appointment.type.replace('_', ' ')}
                          </Badge>
                          <Badge className={getStatusBadgeColor(appointment.status)}>
                            {appointment.status.replace('_', ' ')}
                          </Badge>
                          {appointment.isVirtual && (
                            <Badge variant="outline">Virtual</Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-500">
                          {appointment.user.name || appointment.user.email}
                        </p>
                        <div className="flex items-center space-x-4 text-xs text-gray-400 mt-1">
                          <span>{formatDateTime(appointment.scheduledDate, appointment.scheduledTime)}</span>
                          <span>{appointment.duration} minutes</span>
                          {appointment.location && (
                            <span>Location: {appointment.location}</span>
                          )}
                          {appointment.assignedStaff && (
                            <span>Staff: {appointment.assignedStaff}</span>
                          )}
                        </div>
                        {appointment.application && (
                          <p className="text-sm text-blue-600 mt-1">
                            Pet: {appointment.application.pet.name} ({appointment.application.pet.species})
                          </p>
                        )}
                      </div>
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/appointments/${appointment.id}`}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/appointments/${appointment.id}/edit`}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Appointment
                          </Link>
                        </DropdownMenuItem>
                        {appointment.status === AppointmentStatus.SCHEDULED && (
                          <DropdownMenuItem
                            onClick={() => handleUpdateStatus(appointment.id, AppointmentStatus.CONFIRMED)}
                          >
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Confirm
                          </DropdownMenuItem>
                        )}
                        {[AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED].includes(appointment.status) && (
                          <DropdownMenuItem
                            onClick={() => handleUpdateStatus(appointment.id, AppointmentStatus.COMPLETED)}
                          >
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Mark Complete
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {pagination && pagination.totalPages > 1 && (
              <div className="flex items-center justify-between mt-6">
                <p className="text-sm text-gray-500">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                  {pagination.total} appointments
                </p>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(pagination.page - 1)}
                    disabled={!pagination.hasPrev}
                  >
                    Previous
                  </Button>
                  <span className="text-sm text-gray-500">
                    Page {pagination.page} of {pagination.totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(pagination.page + 1)}
                    disabled={!pagination.hasNext}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
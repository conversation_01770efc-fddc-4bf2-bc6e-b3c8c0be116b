"use client"

import { useState, useEffect } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { UserRole } from "@prisma/client"
import {
  Settings,
  Save,
  Globe,
  Mail,
  Bell,
  Shield,
  Database,
  Palette,
} from "lucide-react"
import {
  Ta<PERSON>,
  Ta<PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import toast from "react-hot-toast"

interface SystemSettings {
  organizationName: string
  organizationDescription: string
  contactEmail: string
  contactPhone: string
  address: string
  city: string
  state: string
  zipCode: string
  website: string
  socialMedia: {
    facebook: string
    twitter: string
    instagram: string
  }
  emailSettings: {
    smtpHost: string
    smtpPort: number
    smtpUser: string
    smtpPassword: string
    fromEmail: string
    fromName: string
  }
  notificationSettings: {
    emailNotifications: boolean
    smsNotifications: boolean
    newApplicationAlert: boolean
    adoptionAlert: boolean
    donationAlert: boolean
  }
  adoptionSettings: {
    adoptionFeeRequired: boolean
    homeVisitRequired: boolean
    referenceCheckRequired: boolean
    backgroundCheckRequired: boolean
    adoptionContract: string
  }
  donationSettings: {
    acceptDonations: boolean
    recurringDonations: boolean
    donationGoal: number
    taxDeductible: boolean
  }
}

export default function SettingsPage() {
  const [settings, setSettings] = useState<SystemSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  const fetchSettings = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/admin/settings")
      
      if (!response.ok) {
        throw new Error("Failed to fetch settings")
      }

      const data = await response.json()
      setSettings(data)
    } catch (error) {
      console.error("Error fetching settings:", error)
      toast.error("Failed to fetch settings")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchSettings()
  }, [])

  const handleSaveSettings = async () => {
    if (!settings) return

    try {
      setSaving(true)
      const response = await fetch("/api/admin/settings", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(settings),
      })

      if (!response.ok) {
        throw new Error("Failed to save settings")
      }

      toast.success("Settings saved successfully")
    } catch (error) {
      console.error("Error saving settings:", error)
      toast.error("Failed to save settings")
    } finally {
      setSaving(false)
    }
  }

  const updateSettings = (section: keyof SystemSettings, field: string, value: any) => {
    if (!settings) return

    setSettings(prev => {
      if (!prev) return prev
      
      if (typeof prev[section] === 'object' && prev[section] !== null) {
        return {
          ...prev,
          [section]: {
            ...prev[section] as object,
            [field]: value
          }
        }
      } else {
        return {
          ...prev,
          [section]: value
        }
      }
    })
  }

  if (loading) {
    return (
      <AdminLayout
        title="System Settings"
        description="Configure system-wide settings and preferences"
        requiredRoles={[UserRole.ADMIN]}
      >
        <div className="animate-pulse space-y-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-32 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </AdminLayout>
    )
  }

  if (!settings) {
    return (
      <AdminLayout
        title="System Settings"
        description="Configure system-wide settings and preferences"
        requiredRoles={[UserRole.ADMIN]}
      >
        <div className="text-center py-12">
          <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">Failed to load settings</p>
          <Button onClick={fetchSettings} className="mt-4">
            Retry
          </Button>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout
      title="System Settings"
      description="Configure system-wide settings and preferences"
      requiredRoles={[UserRole.ADMIN]}
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <Settings className="h-6 w-6 text-gray-600" />
            <span className="text-lg font-medium text-gray-900">
              System Configuration
            </span>
          </div>
          
          <Button onClick={handleSaveSettings} disabled={saving}>
            <Save className="h-4 w-4 mr-2" />
            {saving ? "Saving..." : "Save Changes"}
          </Button>
        </div>

        <Tabs defaultValue="organization" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="organization">Organization</TabsTrigger>
            <TabsTrigger value="email">Email</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
            <TabsTrigger value="adoption">Adoption</TabsTrigger>
            <TabsTrigger value="donations">Donations</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
          </TabsList>

          <TabsContent value="organization" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Globe className="h-5 w-5 mr-2" />
                  Organization Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="orgName">Organization Name</Label>
                    <Input
                      id="orgName"
                      value={settings.organizationName}
                      onChange={(e) => updateSettings('organizationName', '', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="website">Website</Label>
                    <Input
                      id="website"
                      value={settings.website}
                      onChange={(e) => updateSettings('website', '', e.target.value)}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={settings.organizationDescription}
                    onChange={(e) => updateSettings('organizationDescription', '', e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email">Contact Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={settings.contactEmail}
                      onChange={(e) => updateSettings('contactEmail', '', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Contact Phone</Label>
                    <Input
                      id="phone"
                      value={settings.contactPhone}
                      onChange={(e) => updateSettings('contactPhone', '', e.target.value)}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    value={settings.address}
                    onChange={(e) => updateSettings('address', '', e.target.value)}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      value={settings.city}
                      onChange={(e) => updateSettings('city', '', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="state">State</Label>
                    <Input
                      id="state"
                      value={settings.state}
                      onChange={(e) => updateSettings('state', '', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="zipCode">ZIP Code</Label>
                    <Input
                      id="zipCode"
                      value={settings.zipCode}
                      onChange={(e) => updateSettings('zipCode', '', e.target.value)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Social Media</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="facebook">Facebook</Label>
                    <Input
                      id="facebook"
                      value={settings.socialMedia.facebook}
                      onChange={(e) => updateSettings('socialMedia', 'facebook', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="twitter">Twitter</Label>
                    <Input
                      id="twitter"
                      value={settings.socialMedia.twitter}
                      onChange={(e) => updateSettings('socialMedia', 'twitter', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="instagram">Instagram</Label>
                    <Input
                      id="instagram"
                      value={settings.socialMedia.instagram}
                      onChange={(e) => updateSettings('socialMedia', 'instagram', e.target.value)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="email" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Mail className="h-5 w-5 mr-2" />
                  Email Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="smtpHost">SMTP Host</Label>
                    <Input
                      id="smtpHost"
                      value={settings.emailSettings.smtpHost}
                      onChange={(e) => updateSettings('emailSettings', 'smtpHost', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="smtpPort">SMTP Port</Label>
                    <Input
                      id="smtpPort"
                      type="number"
                      value={settings.emailSettings.smtpPort}
                      onChange={(e) => updateSettings('emailSettings', 'smtpPort', parseInt(e.target.value))}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="smtpUser">SMTP Username</Label>
                    <Input
                      id="smtpUser"
                      value={settings.emailSettings.smtpUser}
                      onChange={(e) => updateSettings('emailSettings', 'smtpUser', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="smtpPassword">SMTP Password</Label>
                    <Input
                      id="smtpPassword"
                      type="password"
                      value={settings.emailSettings.smtpPassword}
                      onChange={(e) => updateSettings('emailSettings', 'smtpPassword', e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="fromEmail">From Email</Label>
                    <Input
                      id="fromEmail"
                      type="email"
                      value={settings.emailSettings.fromEmail}
                      onChange={(e) => updateSettings('emailSettings', 'fromEmail', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="fromName">From Name</Label>
                    <Input
                      id="fromName"
                      value={settings.emailSettings.fromName}
                      onChange={(e) => updateSettings('emailSettings', 'fromName', e.target.value)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notifications" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Bell className="h-5 w-5 mr-2" />
                  Notification Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="emailNotifications">Email Notifications</Label>
                    <p className="text-sm text-gray-500">Send email notifications for important events</p>
                  </div>
                  <Switch
                    id="emailNotifications"
                    checked={settings.notificationSettings.emailNotifications}
                    onCheckedChange={(checked) => updateSettings('notificationSettings', 'emailNotifications', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="newApplicationAlert">New Application Alerts</Label>
                    <p className="text-sm text-gray-500">Get notified when new applications are submitted</p>
                  </div>
                  <Switch
                    id="newApplicationAlert"
                    checked={settings.notificationSettings.newApplicationAlert}
                    onCheckedChange={(checked) => updateSettings('notificationSettings', 'newApplicationAlert', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="adoptionAlert">Adoption Alerts</Label>
                    <p className="text-sm text-gray-500">Get notified when adoptions are completed</p>
                  </div>
                  <Switch
                    id="adoptionAlert"
                    checked={settings.notificationSettings.adoptionAlert}
                    onCheckedChange={(checked) => updateSettings('notificationSettings', 'adoptionAlert', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="donationAlert">Donation Alerts</Label>
                    <p className="text-sm text-gray-500">Get notified when donations are received</p>
                  </div>
                  <Switch
                    id="donationAlert"
                    checked={settings.notificationSettings.donationAlert}
                    onCheckedChange={(checked) => updateSettings('notificationSettings', 'donationAlert', checked)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="adoption" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Adoption Process Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="homeVisitRequired">Home Visit Required</Label>
                    <p className="text-sm text-gray-500">Require home visits for all adoptions</p>
                  </div>
                  <Switch
                    id="homeVisitRequired"
                    checked={settings.adoptionSettings.homeVisitRequired}
                    onCheckedChange={(checked) => updateSettings('adoptionSettings', 'homeVisitRequired', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="referenceCheckRequired">Reference Check Required</Label>
                    <p className="text-sm text-gray-500">Require reference checks for all adoptions</p>
                  </div>
                  <Switch
                    id="referenceCheckRequired"
                    checked={settings.adoptionSettings.referenceCheckRequired}
                    onCheckedChange={(checked) => updateSettings('adoptionSettings', 'referenceCheckRequired', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="backgroundCheckRequired">Background Check Required</Label>
                    <p className="text-sm text-gray-500">Require background checks for all adoptions</p>
                  </div>
                  <Switch
                    id="backgroundCheckRequired"
                    checked={settings.adoptionSettings.backgroundCheckRequired}
                    onCheckedChange={(checked) => updateSettings('adoptionSettings', 'backgroundCheckRequired', checked)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="donations" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Donation Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="acceptDonations">Accept Donations</Label>
                    <p className="text-sm text-gray-500">Enable donation functionality on the website</p>
                  </div>
                  <Switch
                    id="acceptDonations"
                    checked={settings.donationSettings.acceptDonations}
                    onCheckedChange={(checked) => updateSettings('donationSettings', 'acceptDonations', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="recurringDonations">Recurring Donations</Label>
                    <p className="text-sm text-gray-500">Allow donors to set up recurring donations</p>
                  </div>
                  <Switch
                    id="recurringDonations"
                    checked={settings.donationSettings.recurringDonations}
                    onCheckedChange={(checked) => updateSettings('donationSettings', 'recurringDonations', checked)}
                  />
                </div>

                <div>
                  <Label htmlFor="donationGoal">Monthly Donation Goal ($)</Label>
                  <Input
                    id="donationGoal"
                    type="number"
                    value={settings.donationSettings.donationGoal}
                    onChange={(e) => updateSettings('donationSettings', 'donationGoal', parseFloat(e.target.value))}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="h-5 w-5 mr-2" />
                  Security Settings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Security settings will be implemented in a future update</p>
                  <p className="text-sm text-gray-400">
                    This will include password policies, session management, and audit logs
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  )
}
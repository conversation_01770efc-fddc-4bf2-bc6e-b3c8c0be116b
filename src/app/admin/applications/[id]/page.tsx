"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { AdminLayout } from "@/components/admin/admin-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { UserRole, ApplicationStatus } from "@prisma/client"
import {
  FileText,
  User,
  Heart,
  Home,
  Phone,
  Mail,
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  ArrowLeft,
  Edit,
  MessageSquare,
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import Link from "next/link"
import toast from "react-hot-toast"

interface Application {
  id: string
  userId: string
  petId: string
  status: ApplicationStatus
  applicantName: string | null
  applicantEmail: string | null
  applicantPhone: string | null
  housingType: string | null
  ownOrRent: string | null
  landlordContact: string | null
  hasYard: boolean | null
  yardFenced: boolean | null
  yardSize: string | null
  householdSize: number | null
  hasChildren: boolean | null
  childrenAges: string | null
  hasOtherPets: boolean | null
  otherPetsDetails: string | null
  petExperience: string | null
  previousPets: string | null
  currentVeterinarian: string | null
  vetContact: string | null
  workSchedule: string | null
  travelFrequency: string | null
  exercisePlans: string | null
  trainingPlans: string | null
  personalReferences: any
  veterinaryReference: any
  agreesToTerms: boolean
  agreesToHomeVisit: boolean
  staffNotes: string | null
  rejectionReason: string | null
  submittedAt: string | null
  reviewedAt: string | null
  approvedAt: string | null
  createdAt: string
  updatedAt: string
  user: {
    id: string
    name: string | null
    email: string
    phone: string | null
    firstName: string | null
    lastName: string | null
  }
  pet: {
    id: string
    name: string
    species: string
    breed: string
    age: number | null
    status: string
    photos: Array<{
      id: string
      url: string
      isPrimary: boolean
    }>
  }
}

export default function ApplicationDetails() {
  const params = useParams()
  const router = useRouter()
  const [application, setApplication] = useState<Application | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [staffNotes, setStaffNotes] = useState("")
  const [rejectionReason, setRejectionReason] = useState("")

  const applicationId = params.id as string

  const fetchApplication = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/applications/${applicationId}`)
      
      if (!response.ok) {
        throw new Error("Failed to fetch application")
      }

      const data = await response.json()
      setApplication(data.application)
      setStaffNotes(data.application.staffNotes || "")
      setRejectionReason(data.application.rejectionReason || "")
    } catch (error) {
      console.error("Error fetching application:", error)
      toast.error("Failed to fetch application details")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (applicationId) {
      fetchApplication()
    }
  }, [applicationId])

  const handleStatusUpdate = async (newStatus: ApplicationStatus) => {
    if (!application) return

    try {
      setUpdating(true)
      const response = await fetch(`/api/admin/applications/${applicationId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: newStatus,
          staffNotes,
          rejectionReason: newStatus === ApplicationStatus.REJECTED ? rejectionReason : null,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to update application")
      }

      toast.success("Application status updated successfully")
      fetchApplication()
    } catch (error) {
      console.error("Error updating application:", error)
      toast.error("Failed to update application status")
    } finally {
      setUpdating(false)
    }
  }

  const getStatusBadgeColor = (status: ApplicationStatus) => {
    switch (status) {
      case ApplicationStatus.APPROVED:
        return "bg-green-100 text-green-800"
      case ApplicationStatus.REJECTED:
        return "bg-red-100 text-red-800"
      case ApplicationStatus.UNDER_REVIEW:
        return "bg-blue-100 text-blue-800"
      case ApplicationStatus.REFERENCE_CHECK:
        return "bg-yellow-100 text-yellow-800"
      case ApplicationStatus.HOME_VISIT_SCHEDULED:
        return "bg-purple-100 text-purple-800"
      case ApplicationStatus.HOME_VISIT_COMPLETED:
        return "bg-indigo-100 text-indigo-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: ApplicationStatus) => {
    switch (status) {
      case ApplicationStatus.APPROVED:
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case ApplicationStatus.REJECTED:
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-blue-500" />
    }
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Not set"
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <AdminLayout
        title="Application Details"
        description="Review adoption application"
        requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
      >
        <div className="animate-pulse space-y-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-32 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </AdminLayout>
    )
  }

  if (!application) {
    return (
      <AdminLayout
        title="Application Not Found"
        description="The requested application could not be found"
        requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
      >
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Application Not Found
          </h3>
          <p className="text-gray-500 mb-6">
            The application you're looking for doesn't exist or has been removed.
          </p>
          <Link href="/admin/applications">
            <Button>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Applications
            </Button>
          </Link>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout
      title="Application Details"
      description={`Review application for ${application.pet.name}`}
      requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/admin/applications">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Application #{application.id.slice(-8)}
              </h1>
              <div className="flex items-center space-x-2 mt-1">
                {getStatusIcon(application.status)}
                <Badge className={getStatusBadgeColor(application.status)}>
                  {application.status.replace('_', ' ')}
                </Badge>
                <span className="text-sm text-gray-500">
                  Submitted {formatDate(application.submittedAt)}
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Link href={`/admin/applications/${application.id}/edit`}>
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Pet Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Heart className="h-5 w-5 mr-2" />
                  Pet Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start space-x-4">
                  {application.pet.photos.length > 0 && (
                    <img
                      src={application.pet.photos[0].url}
                      alt={application.pet.name}
                      className="w-20 h-20 rounded-lg object-cover"
                    />
                  )}
                  <div>
                    <h3 className="text-lg font-semibold">{application.pet.name}</h3>
                    <p className="text-gray-600">
                      {application.pet.species} • {application.pet.breed}
                    </p>
                    {application.pet.age && (
                      <p className="text-sm text-gray-500">
                        {Math.floor(application.pet.age / 12)} years, {application.pet.age % 12} months old
                      </p>
                    )}
                    <Link href={`/admin/pets/${application.pet.id}`}>
                      <Button variant="outline" size="sm" className="mt-2">
                        View Pet Details
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Applicant Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  Applicant Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Name</Label>
                    <p className="text-gray-900">
                      {application.applicantName || application.user.name || 'Not provided'}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Email</Label>
                    <p className="text-gray-900 flex items-center">
                      <Mail className="h-4 w-4 mr-1" />
                      {application.applicantEmail || application.user.email}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Phone</Label>
                    <p className="text-gray-900 flex items-center">
                      <Phone className="h-4 w-4 mr-1" />
                      {application.applicantPhone || application.user.phone || 'Not provided'}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Household Size</Label>
                    <p className="text-gray-900">
                      {application.householdSize ? `${application.householdSize} people` : 'Not provided'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Housing Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Home className="h-5 w-5 mr-2" />
                  Housing Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Housing Type</Label>
                    <p className="text-gray-900">{application.housingType || 'Not provided'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Own or Rent</Label>
                    <p className="text-gray-900">{application.ownOrRent || 'Not provided'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Has Yard</Label>
                    <p className="text-gray-900">
                      {application.hasYard === null ? 'Not provided' : application.hasYard ? 'Yes' : 'No'}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Yard Fenced</Label>
                    <p className="text-gray-900">
                      {application.yardFenced === null ? 'Not provided' : application.yardFenced ? 'Yes' : 'No'}
                    </p>
                  </div>
                </div>
                
                {application.landlordContact && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Landlord Contact</Label>
                    <p className="text-gray-900">{application.landlordContact}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Experience & Lifestyle */}
            <Card>
              <CardHeader>
                <CardTitle>Experience & Lifestyle</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {application.petExperience && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Pet Experience</Label>
                    <p className="text-gray-900">{application.petExperience}</p>
                  </div>
                )}
                
                {application.workSchedule && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Work Schedule</Label>
                    <p className="text-gray-900">{application.workSchedule}</p>
                  </div>
                )}
                
                {application.exercisePlans && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Exercise Plans</Label>
                    <p className="text-gray-900">{application.exercisePlans}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Status Management */}
            <Card>
              <CardHeader>
                <CardTitle>Application Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="status">Update Status</Label>
                  <Select
                    value={application.status}
                    onValueChange={(value) => handleStatusUpdate(value as ApplicationStatus)}
                    disabled={updating}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={ApplicationStatus.SUBMITTED}>Submitted</SelectItem>
                      <SelectItem value={ApplicationStatus.UNDER_REVIEW}>Under Review</SelectItem>
                      <SelectItem value={ApplicationStatus.REFERENCE_CHECK}>Reference Check</SelectItem>
                      <SelectItem value={ApplicationStatus.HOME_VISIT_SCHEDULED}>Home Visit Scheduled</SelectItem>
                      <SelectItem value={ApplicationStatus.HOME_VISIT_COMPLETED}>Home Visit Completed</SelectItem>
                      <SelectItem value={ApplicationStatus.APPROVED}>Approved</SelectItem>
                      <SelectItem value={ApplicationStatus.REJECTED}>Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="staffNotes">Staff Notes</Label>
                  <Textarea
                    id="staffNotes"
                    value={staffNotes}
                    onChange={(e) => setStaffNotes(e.target.value)}
                    placeholder="Add internal notes about this application..."
                    rows={4}
                  />
                </div>

                {application.status === ApplicationStatus.REJECTED && (
                  <div className="space-y-2">
                    <Label htmlFor="rejectionReason">Rejection Reason</Label>
                    <Textarea
                      id="rejectionReason"
                      value={rejectionReason}
                      onChange={(e) => setRejectionReason(e.target.value)}
                      placeholder="Reason for rejection (will be sent to applicant)..."
                      rows={3}
                    />
                  </div>
                )}

                <Button
                  onClick={() => handleStatusUpdate(application.status)}
                  disabled={updating}
                  className="w-full"
                >
                  {updating ? "Updating..." : "Save Notes"}
                </Button>
              </CardContent>
            </Card>

            {/* Timeline */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Timeline
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center space-x-2 text-sm">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-500">Submitted:</span>
                  <span>{formatDate(application.submittedAt)}</span>
                </div>
                
                {application.reviewedAt && (
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <span className="text-gray-500">Reviewed:</span>
                    <span>{formatDate(application.reviewedAt)}</span>
                  </div>
                )}
                
                {application.approvedAt && (
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-gray-500">Approved:</span>
                    <span>{formatDate(application.approvedAt)}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Contact Applicant
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule Home Visit
                </Button>
                <Link href={`/admin/users/${application.userId}`}>
                  <Button variant="outline" className="w-full justify-start">
                    <User className="h-4 w-4 mr-2" />
                    View User Profile
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
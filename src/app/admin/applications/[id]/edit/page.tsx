"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { AdminLayout } from "@/components/admin/admin-layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { UserRole, ApplicationStatus } from "@prisma/client"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { FileText, Save, ArrowLeft } from "lucide-react"
import Link from "next/link"
import toast from "react-hot-toast"

interface ApplicationFormData {
  applicantName: string
  applicantEmail: string
  applicantPhone: string
  housingType: string
  ownOrRent: string
  landlordContact: string
  hasYard: boolean | null
  yardFenced: boolean | null
  yardSize: string
  householdSize: number | null
  hasChildren: boolean | null
  childrenAges: string
  hasOtherPets: boolean | null
  otherPetsDetails: string
  petExperience: string
  previousPets: string
  currentVeterinarian: string
  vetContact: string
  workSchedule: string
  travelFrequency: string
  exercisePlans: string
  trainingPlans: string
  agreesToTerms: boolean
  agreesToHomeVisit: boolean
  staffNotes: string
  rejectionReason: string
  status: ApplicationStatus
}

interface Application extends ApplicationFormData {
  id: string
  userId: string
  petId: string
  submittedAt: string | null
  reviewedAt: string | null
  approvedAt: string | null
  createdAt: string
  updatedAt: string
  user: {
    id: string
    name: string | null
    email: string
  }
  pet: {
    id: string
    name: string
    species: string
    breed: string
  }
}

export default function EditApplication() {
  const params = useParams()
  const router = useRouter()
  const [application, setApplication] = useState<Application | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState<ApplicationFormData>({
    applicantName: "",
    applicantEmail: "",
    applicantPhone: "",
    housingType: "",
    ownOrRent: "",
    landlordContact: "",
    hasYard: null,
    yardFenced: null,
    yardSize: "",
    householdSize: null,
    hasChildren: null,
    childrenAges: "",
    hasOtherPets: null,
    otherPetsDetails: "",
    petExperience: "",
    previousPets: "",
    currentVeterinarian: "",
    vetContact: "",
    workSchedule: "",
    travelFrequency: "",
    exercisePlans: "",
    trainingPlans: "",
    agreesToTerms: false,
    agreesToHomeVisit: false,
    staffNotes: "",
    rejectionReason: "",
    status: ApplicationStatus.SUBMITTED,
  })

  const applicationId = params.id as string

  const fetchApplication = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/applications/${applicationId}`)
      
      if (!response.ok) {
        throw new Error("Failed to fetch application")
      }

      const data = await response.json()
      setApplication(data.application)
      
      // Populate form data
      const app = data.application
      setFormData({
        applicantName: app.applicantName || "",
        applicantEmail: app.applicantEmail || "",
        applicantPhone: app.applicantPhone || "",
        housingType: app.housingType || "",
        ownOrRent: app.ownOrRent || "",
        landlordContact: app.landlordContact || "",
        hasYard: app.hasYard,
        yardFenced: app.yardFenced,
        yardSize: app.yardSize || "",
        householdSize: app.householdSize,
        hasChildren: app.hasChildren,
        childrenAges: app.childrenAges || "",
        hasOtherPets: app.hasOtherPets,
        otherPetsDetails: app.otherPetsDetails || "",
        petExperience: app.petExperience || "",
        previousPets: app.previousPets || "",
        currentVeterinarian: app.currentVeterinarian || "",
        vetContact: app.vetContact || "",
        workSchedule: app.workSchedule || "",
        travelFrequency: app.travelFrequency || "",
        exercisePlans: app.exercisePlans || "",
        trainingPlans: app.trainingPlans || "",
        agreesToTerms: app.agreesToTerms || false,
        agreesToHomeVisit: app.agreesToHomeVisit || false,
        staffNotes: app.staffNotes || "",
        rejectionReason: app.rejectionReason || "",
        status: app.status,
      })
    } catch (error) {
      console.error("Error fetching application:", error)
      toast.error("Failed to fetch application details")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (applicationId) {
      fetchApplication()
    }
  }, [applicationId])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      setSaving(true)
      const response = await fetch(`/api/admin/applications/${applicationId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        throw new Error("Failed to update application")
      }

      toast.success("Application updated successfully")
      router.push(`/admin/applications/${applicationId}`)
    } catch (error) {
      console.error("Error updating application:", error)
      toast.error("Failed to update application")
    } finally {
      setSaving(false)
    }
  }

  const updateFormData = (field: keyof ApplicationFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  if (loading) {
    return (
      <AdminLayout
        title="Edit Application"
        description="Edit adoption application details"
        requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
      >
        <div className="animate-pulse space-y-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-32 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </AdminLayout>
    )
  }

  if (!application) {
    return (
      <AdminLayout
        title="Application Not Found"
        description="The requested application could not be found"
        requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
      >
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Application Not Found
          </h3>
          <p className="text-gray-500 mb-6">
            The application you're trying to edit doesn't exist or has been removed.
          </p>
          <Link href="/admin/applications">
            <Button>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Applications
            </Button>
          </Link>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout
      title="Edit Application"
      description={`Edit application for ${application.pet.name}`}
      requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href={`/admin/applications/${applicationId}`}>
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Details
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Edit Application #{application.id.slice(-8)}
              </h1>
              <p className="text-gray-600">
                Application for {application.pet.name} by {application.user.name || application.user.email}
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Status and Internal Notes */}
          <Card>
            <CardHeader>
              <CardTitle>Application Status & Notes</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => updateFormData('status', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={ApplicationStatus.SUBMITTED}>Submitted</SelectItem>
                    <SelectItem value={ApplicationStatus.UNDER_REVIEW}>Under Review</SelectItem>
                    <SelectItem value={ApplicationStatus.REFERENCE_CHECK}>Reference Check</SelectItem>
                    <SelectItem value={ApplicationStatus.HOME_VISIT_SCHEDULED}>Home Visit Scheduled</SelectItem>
                    <SelectItem value={ApplicationStatus.HOME_VISIT_COMPLETED}>Home Visit Completed</SelectItem>
                    <SelectItem value={ApplicationStatus.APPROVED}>Approved</SelectItem>
                    <SelectItem value={ApplicationStatus.REJECTED}>Rejected</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="staffNotes">Staff Notes</Label>
                <Textarea
                  id="staffNotes"
                  value={formData.staffNotes}
                  onChange={(e) => updateFormData('staffNotes', e.target.value)}
                  rows={4}
                  placeholder="Internal notes about this application..."
                />
              </div>

              {formData.status === ApplicationStatus.REJECTED && (
                <div>
                  <Label htmlFor="rejectionReason">Rejection Reason</Label>
                  <Textarea
                    id="rejectionReason"
                    value={formData.rejectionReason}
                    onChange={(e) => updateFormData('rejectionReason', e.target.value)}
                    rows={3}
                    placeholder="Reason for rejection (will be sent to applicant)..."
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Applicant Information */}
          <Card>
            <CardHeader>
              <CardTitle>Applicant Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="applicantName">Full Name</Label>
                  <Input
                    id="applicantName"
                    value={formData.applicantName}
                    onChange={(e) => updateFormData('applicantName', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="applicantEmail">Email</Label>
                  <Input
                    id="applicantEmail"
                    type="email"
                    value={formData.applicantEmail}
                    onChange={(e) => updateFormData('applicantEmail', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="applicantPhone">Phone</Label>
                  <Input
                    id="applicantPhone"
                    value={formData.applicantPhone}
                    onChange={(e) => updateFormData('applicantPhone', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="householdSize">Household Size</Label>
                  <Input
                    id="householdSize"
                    type="number"
                    value={formData.householdSize || ""}
                    onChange={(e) => updateFormData('householdSize', e.target.value ? parseInt(e.target.value) : null)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Housing Information */}
          <Card>
            <CardHeader>
              <CardTitle>Housing Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="housingType">Housing Type</Label>
                  <Select value={formData.housingType} onValueChange={(value) => updateFormData('housingType', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select housing type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="House">House</SelectItem>
                      <SelectItem value="Apartment">Apartment</SelectItem>
                      <SelectItem value="Condo">Condo</SelectItem>
                      <SelectItem value="Townhouse">Townhouse</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="ownOrRent">Own or Rent</Label>
                  <Select value={formData.ownOrRent} onValueChange={(value) => updateFormData('ownOrRent', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select ownership" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Own">Own</SelectItem>
                      <SelectItem value="Rent">Rent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="hasYard"
                    checked={formData.hasYard === true}
                    onCheckedChange={(checked) => updateFormData('hasYard', checked)}
                  />
                  <Label htmlFor="hasYard">Has Yard</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="yardFenced"
                    checked={formData.yardFenced === true}
                    onCheckedChange={(checked) => updateFormData('yardFenced', checked)}
                    disabled={!formData.hasYard}
                  />
                  <Label htmlFor="yardFenced">Yard Fenced</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="hasChildren"
                    checked={formData.hasChildren === true}
                    onCheckedChange={(checked) => updateFormData('hasChildren', checked)}
                  />
                  <Label htmlFor="hasChildren">Has Children</Label>
                </div>
              </div>

              {formData.hasChildren && (
                <div>
                  <Label htmlFor="childrenAges">Children Ages</Label>
                  <Input
                    id="childrenAges"
                    value={formData.childrenAges}
                    onChange={(e) => updateFormData('childrenAges', e.target.value)}
                    placeholder="e.g., 5, 8, 12"
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Experience & Lifestyle */}
          <Card>
            <CardHeader>
              <CardTitle>Experience & Lifestyle</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="petExperience">Pet Experience</Label>
                <Textarea
                  id="petExperience"
                  value={formData.petExperience}
                  onChange={(e) => updateFormData('petExperience', e.target.value)}
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="workSchedule">Work Schedule</Label>
                <Textarea
                  id="workSchedule"
                  value={formData.workSchedule}
                  onChange={(e) => updateFormData('workSchedule', e.target.value)}
                  rows={2}
                />
              </div>

              <div>
                <Label htmlFor="exercisePlans">Exercise Plans</Label>
                <Textarea
                  id="exercisePlans"
                  value={formData.exercisePlans}
                  onChange={(e) => updateFormData('exercisePlans', e.target.value)}
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-4">
            <Link href={`/admin/applications/${applicationId}`}>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </Link>
            <Button type="submit" disabled={saving}>
              <Save className="h-4 w-4 mr-2" />
              {saving ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </form>
      </div>
    </AdminLayout>
  )
}
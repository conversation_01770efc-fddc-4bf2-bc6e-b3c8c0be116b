"use client"

import { useState, useEffect } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { UserRole, ApplicationStatus } from "@prisma/client"
import { Label } from "@/components/ui/label"
import { 
  Search, 
  Eye, 
  Filter,
  MoreHorizontal,
  FileText,
  User,
  Heart,
  Calendar,
  Phone,
  Mail,
  MapPin,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  RefreshCw,
  Download,
  Upload,
  SortAsc,
  SortDesc,
  Grid,
  List,
  Settings,
  MessageSquare,
  Star,
  Flag,
  Archive,
  Trash2,
  Edit,
  Send,
  UserCheck,
  Home,
  PawPrint
} from "lucide-react"
import Link from "next/link"
import { toast } from "react-hot-toast"

interface Application {
  id: string
  status: string
  submittedAt: string
  applicantName: string
  applicantEmail: string
  applicantPhone: string
  pet: {
    id: string
    name: string
    species: string
    breed: string
    photos: Array<{ url: string; isPrimary: boolean }>
  }
  user: {
    id: string
    name: string
    email: string
  }
  housingType: string
  ownOrRent: string
  hasYard: boolean
  hasChildren: boolean
  hasOtherPets: boolean
  experience: string
  references: Array<{
    name: string
    relationship: string
    phone: string
    email: string
  }>
  createdAt: string
  updatedAt: string
}

const statusColors = {
  DRAFT: "bg-gray-100 text-gray-800",
  SUBMITTED: "bg-blue-100 text-blue-800",
  UNDER_REVIEW: "bg-yellow-100 text-yellow-800",
  REFERENCE_CHECK: "bg-purple-100 text-purple-800",
  HOME_VISIT_SCHEDULED: "bg-orange-100 text-orange-800",
  HOME_VISIT_COMPLETED: "bg-teal-100 text-teal-800",
  APPROVED: "bg-green-100 text-green-800",
  REJECTED: "bg-red-100 text-red-800",
  WITHDRAWN: "bg-gray-100 text-gray-800",
  WAITLISTED: "bg-indigo-100 text-indigo-800"
}

const statusIcons = {
  DRAFT: Edit,
  SUBMITTED: FileText,
  UNDER_REVIEW: Clock,
  REFERENCE_CHECK: UserCheck,
  HOME_VISIT_SCHEDULED: Calendar,
  HOME_VISIT_COMPLETED: CheckCircle,
  APPROVED: CheckCircle,
  REJECTED: XCircle,
  WITHDRAWN: Archive,
  WAITLISTED: Flag
}

export default function AdminApplicationsPage() {
  const [applications, setApplications] = useState<Application[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sortBy, setSortBy] = useState("submittedAt")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const [viewMode, setViewMode] = useState<"grid" | "list">("list")
  const [selectedApplications, setSelectedApplications] = useState<string[]>([])

  useEffect(() => {
    fetchApplications()
  }, [])

  const fetchApplications = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/admin/applications")
      
      if (!response.ok) {
        throw new Error("Failed to fetch applications")
      }
      
      const data = await response.json()
      setApplications(data.applications || [])
    } catch (error) {
      console.error("Error fetching applications:", error)
      toast.error("Failed to load applications")
    } finally {
      setLoading(false)
    }
  }

  const handleStatusUpdate = async (applicationId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/admin/applications/${applicationId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ status: newStatus })
      })

      if (!response.ok) {
        throw new Error("Failed to update status")
      }

      toast.success("Application status updated successfully")
      fetchApplications()
    } catch (error) {
      console.error("Error updating status:", error)
      toast.error("Failed to update status")
    }
  }

  const handleBulkAction = async (action: string) => {
    if (selectedApplications.length === 0) {
      toast.error("Please select applications first")
      return
    }

    try {
      const response = await fetch("/api/admin/applications/bulk", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          action,
          applicationIds: selectedApplications
        })
      })

      if (!response.ok) {
        throw new Error("Failed to perform bulk action")
      }

      toast.success(`Bulk action completed successfully`)
      setSelectedApplications([])
      fetchApplications()
    } catch (error) {
      console.error("Error performing bulk action:", error)
      toast.error("Failed to perform bulk action")
    }
  }

  const filteredApplications = applications.filter(application => {
    const matchesSearch = application.applicantName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         application.applicantEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         application.pet.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         application.pet.breed.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || application.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const sortedApplications = [...filteredApplications].sort((a, b) => {
    let aValue: any = a[sortBy as keyof Application]
    let bValue: any = b[sortBy as keyof Application]
    
    if (sortBy === "submittedAt" || sortBy === "createdAt" || sortBy === "updatedAt") {
      aValue = new Date(aValue).getTime()
      bValue = new Date(bValue).getTime()
    }
    
    if (sortOrder === "asc") {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatStatus = (status: string) => {
    return status.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
  }

  const getStatusIcon = (status: string) => {
    const IconComponent = statusIcons[status as keyof typeof statusIcons] || FileText
    return <IconComponent className="h-4 w-4" />
  }

  return (
    <AdminLayout
      title="Application Management"
      description="Review and manage adoption applications from potential pet parents"
      requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
    >
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      ) : (
        <div className="space-y-6">
          {/* Header Actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <FileText className="h-6 w-6 text-gray-600" />
              <span className="text-lg font-medium text-gray-900">
                {sortedApplications.length} applications found
              </span>
            </div>
            <div className="flex gap-2">
              <Button variant="outline">
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>

          {/* Filters and Search */}
          <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4 mb-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by applicant name, email, or pet..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="SUBMITTED">Submitted</option>
                <option value="UNDER_REVIEW">Under Review</option>
                <option value="REFERENCE_CHECK">Reference Check</option>
                <option value="HOME_VISIT_SCHEDULED">Home Visit Scheduled</option>
                <option value="HOME_VISIT_COMPLETED">Home Visit Completed</option>
                <option value="APPROVED">Approved</option>
                <option value="REJECTED">Rejected</option>
                <option value="WAITLISTED">Waitlisted</option>
              </select>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="submittedAt">Submission Date</option>
                <option value="applicantName">Applicant Name</option>
                <option value="status">Status</option>
                <option value="updatedAt">Last Updated</option>
              </select>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
              >
                {sortOrder === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
              </Button>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">
                {sortedApplications.length} applications found
              </span>
              {selectedApplications.length > 0 && (
                <div className="flex items-center gap-2 ml-4">
                  <span className="text-sm text-gray-600">
                    {selectedApplications.length} selected
                  </span>
                  <Button size="sm" variant="outline" onClick={() => handleBulkAction("approve")}>
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Approve
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => handleBulkAction("reject")}>
                    <XCircle className="h-3 w-3 mr-1" />
                    Reject
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => handleBulkAction("review")}>
                    <Clock className="h-3 w-3 mr-1" />
                    Mark for Review
                  </Button>
                </div>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("grid")}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("list")}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
          </CardContent>
          </Card>

          {/* Applications List */}
          <div className="space-y-4">
        {sortedApplications.map((application) => (
          <Card key={application.id} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-start gap-4">
                  <input
                    type="checkbox"
                    checked={selectedApplications.includes(application.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedApplications([...selectedApplications, application.id])
                      } else {
                        setSelectedApplications(selectedApplications.filter(id => id !== application.id))
                      }
                    }}
                    className="mt-1"
                  />
                  <div className="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                    {application.pet.photos.find(p => p.isPrimary)?.url ? (
                      <img
                        src={application.pet.photos.find(p => p.isPrimary)?.url}
                        alt={application.pet.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <PawPrint className="h-6 w-6 text-gray-400" />
                      </div>
                    )}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-1">{application.applicantName}</h3>
                    <p className="text-gray-600 mb-2">
                      Applying for {application.pet.name} • {application.pet.breed} • {application.pet.species}
                    </p>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <Mail className="h-3 w-3" />
                        {application.applicantEmail}
                      </div>
                      <div className="flex items-center gap-1">
                        <Phone className="h-3 w-3" />
                        {application.applicantPhone}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {formatDate(application.submittedAt)}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={statusColors[application.status as keyof typeof statusColors]}>
                    {getStatusIcon(application.status)}
                    <span className="ml-1">{formatStatus(application.status)}</span>
                  </Badge>
                  <div className="flex items-center gap-1">
                    <select
                      value={application.status}
                      onChange={(e) => handleStatusUpdate(application.id, e.target.value)}
                      className="text-sm px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="SUBMITTED">Submitted</option>
                      <option value="UNDER_REVIEW">Under Review</option>
                      <option value="REFERENCE_CHECK">Reference Check</option>
                      <option value="HOME_VISIT_SCHEDULED">Home Visit Scheduled</option>
                      <option value="HOME_VISIT_COMPLETED">Home Visit Completed</option>
                      <option value="APPROVED">Approved</option>
                      <option value="REJECTED">Rejected</option>
                      <option value="WAITLISTED">Waitlisted</option>
                    </select>
                    <Link href={`/admin/applications/${application.id}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="h-3 w-3" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <Label className="text-gray-500">Housing</Label>
                  <p>{application.housingType} • {application.ownOrRent}</p>
                </div>
                <div>
                  <Label className="text-gray-500">Yard</Label>
                  <p>{application.hasYard ? "Yes" : "No"}</p>
                </div>
                <div>
                  <Label className="text-gray-500">Children</Label>
                  <p>{application.hasChildren ? "Yes" : "No"}</p>
                </div>
                <div>
                  <Label className="text-gray-500">Other Pets</Label>
                  <p>{application.hasOtherPets ? "Yes" : "No"}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          ))}
          </div>

          {sortedApplications.length === 0 && (
            <Card>
              <CardContent className="text-center py-12">
                <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No applications found</h3>
                <p className="text-gray-600 mb-6">
                  {searchTerm || statusFilter !== "all"
                    ? "Try adjusting your search criteria or filters."
                    : "No adoption applications have been submitted yet."
                  }
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </AdminLayout>
  )
}

"use client"

import { AdminLayout } from "@/components/admin/admin-layout"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { UserRole } from "@prisma/client"
import { Package, Plus, AlertTriangle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export default function InventoryManagement() {
  return (
    <AdminLayout
      title="Inventory Management"
      description="Manage supplies, equipment, and inventory"
      requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
    >
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Package className="h-6 w-6 mr-2" />
              Inventory Management
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Inventory Management Coming Soon
              </h3>
              <p className="text-gray-500 mb-6">
                This feature will allow you to track supplies, equipment, and inventory levels.
              </p>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
                  <div className="p-4 border rounded-lg">
                    <Package className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                    <h4 className="font-medium">Supply Tracking</h4>
                    <p className="text-sm text-gray-500">Monitor food, toys, and supplies</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <AlertTriangle className="h-8 w-8 text-orange-500 mx-auto mb-2" />
                    <h4 className="font-medium">Low Stock Alerts</h4>
                    <p className="text-sm text-gray-500">Get notified when supplies run low</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <Plus className="h-8 w-8 text-green-500 mx-auto mb-2" />
                    <h4 className="font-medium">Order Management</h4>
                    <p className="text-sm text-gray-500">Track orders and deliveries</p>
                  </div>
                </div>
                <Button disabled>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Inventory Item (Coming Soon)
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
"use client"

import { useState, useEffect } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { UserRole } from "@prisma/client"
import {
  Search,
  Plus,
  Edit,
  Eye,
  Filter,
  Download,
  MoreHorizontal,
  Stethoscope,
  Calendar,
  FileText,
  AlertCircle,
  CheckCircle,
  Clock,
  Syringe,
  Heart,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import Link from "next/link"
import toast from "react-hot-toast"

interface MedicalRecord {
  id: string
  petId: string
  recordType: string
  date: string
  veterinarian: string | null
  clinic: string | null
  description: string
  medications: string | null
  nextDueDate: string | null
  cost: number | null
  documents: string[]
  createdAt: string
  updatedAt: string
  pet: {
    id: string
    name: string
    species: string
    breed: string
    status: string
  }
}

interface MedicalRecordsResponse {
  records: MedicalRecord[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export default function MedicalRecordsManagement() {
  const [records, setRecords] = useState<MedicalRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState<string>("all")
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState<MedicalRecordsResponse['pagination'] | null>(null)

  const fetchMedicalRecords = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        ...(searchTerm && { search: searchTerm }),
        ...(selectedType !== "all" && { type: selectedType }),
      })

      const response = await fetch(`/api/admin/medical?${params}`)
      
      if (!response.ok) {
        throw new Error("Failed to fetch medical records")
      }

      const data: MedicalRecordsResponse = await response.json()
      setRecords(data.records)
      setPagination(data.pagination)
    } catch (error) {
      console.error("Error fetching medical records:", error)
      toast.error("Failed to fetch medical records")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchMedicalRecords()
  }, [currentPage, searchTerm, selectedType])

  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setCurrentPage(1)
  }

  const handleTypeFilter = (value: string) => {
    setSelectedType(value)
    setCurrentPage(1)
  }

  const getRecordTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case "vaccination":
        return <Syringe className="h-4 w-4 text-green-500" />
      case "checkup":
        return <Stethoscope className="h-4 w-4 text-blue-500" />
      case "surgery":
        return <Heart className="h-4 w-4 text-red-500" />
      case "treatment":
        return <FileText className="h-4 w-4 text-purple-500" />
      default:
        return <FileText className="h-4 w-4 text-gray-500" />
    }
  }

  const getRecordTypeBadgeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case "vaccination":
        return "bg-green-100 text-green-800"
      case "checkup":
        return "bg-blue-100 text-blue-800"
      case "surgery":
        return "bg-red-100 text-red-800"
      case "treatment":
        return "bg-purple-100 text-purple-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const isOverdue = (nextDueDate: string | null) => {
    if (!nextDueDate) return false
    return new Date(nextDueDate) < new Date()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <AdminLayout
      title="Medical Records Management"
      description="Manage pet medical records, vaccinations, and treatments"
      requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center space-x-2">
            <Stethoscope className="h-6 w-6 text-gray-600" />
            <span className="text-lg font-medium text-gray-900">
              {pagination?.total || 0} Medical Records
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Link href="/admin/medical/new">
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Record
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Syringe className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">
                    {records.filter(r => r.recordType.toLowerCase() === 'vaccination').length}
                  </p>
                  <p className="text-sm text-gray-600">Vaccinations</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Stethoscope className="h-8 w-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">
                    {records.filter(r => r.recordType.toLowerCase() === 'checkup').length}
                  </p>
                  <p className="text-sm text-gray-600">Checkups</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Heart className="h-8 w-8 text-red-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">
                    {records.filter(r => r.recordType.toLowerCase() === 'surgery').length}
                  </p>
                  <p className="text-sm text-gray-600">Surgeries</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertCircle className="h-8 w-8 text-orange-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">
                    {records.filter(r => isOverdue(r.nextDueDate)).length}
                  </p>
                  <p className="text-sm text-gray-600">Overdue</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by pet name, veterinarian, or description..."
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <Select value={selectedType} onValueChange={handleTypeFilter}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="vaccination">Vaccination</SelectItem>
                  <SelectItem value="checkup">Checkup</SelectItem>
                  <SelectItem value="surgery">Surgery</SelectItem>
                  <SelectItem value="treatment">Treatment</SelectItem>
                  <SelectItem value="emergency">Emergency</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Medical Records List */}
        <Card>
          <CardHeader>
            <CardTitle>Medical Records</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-20 bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>
            ) : records.length === 0 ? (
              <div className="text-center py-8">
                <Stethoscope className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No medical records found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {records.map((record) => (
                  <div
                    key={record.id}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                        {getRecordTypeIcon(record.recordType)}
                      </div>
                      
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="font-medium text-gray-900">
                            {record.pet.name} - {record.recordType}
                          </p>
                          <Badge className={getRecordTypeBadgeColor(record.recordType)}>
                            {record.recordType}
                          </Badge>
                          {isOverdue(record.nextDueDate) && (
                            <Badge className="bg-red-100 text-red-800">
                              Overdue
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-500">
                          {record.pet.species} • {record.pet.breed}
                        </p>
                        <div className="flex items-center space-x-4 text-xs text-gray-400 mt-1">
                          <span>Date: {formatDate(record.date)}</span>
                          {record.veterinarian && (
                            <span>Vet: {record.veterinarian}</span>
                          )}
                          {record.clinic && (
                            <span>Clinic: {record.clinic}</span>
                          )}
                          {record.nextDueDate && (
                            <span>Next Due: {formatDate(record.nextDueDate)}</span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                          {record.description}
                        </p>
                      </div>
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/medical/${record.id}`}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/medical/${record.id}/edit`}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Record
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/pets/${record.petId}`}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Pet
                          </Link>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {pagination && pagination.totalPages > 1 && (
              <div className="flex items-center justify-between mt-6">
                <p className="text-sm text-gray-500">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                  {pagination.total} records
                </p>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(pagination.page - 1)}
                    disabled={!pagination.hasPrev}
                  >
                    Previous
                  </Button>
                  <span className="text-sm text-gray-500">
                    Page {pagination.page} of {pagination.totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(pagination.page + 1)}
                    disabled={!pagination.hasNext}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
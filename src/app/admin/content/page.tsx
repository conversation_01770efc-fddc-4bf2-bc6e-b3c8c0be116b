"use client"

import { AdminLayout } from "@/components/admin/admin-layout"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { UserRole } from "@prisma/client"
import { BookOpen, FileText, Image, Video } from "lucide-react"
import { Button } from "@/components/ui/button"

export default function ContentManagement() {
  return (
    <AdminLayout
      title="Content Management"
      description="Manage website content, blog posts, and media"
      requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
    >
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BookOpen className="h-6 w-6 mr-2" />
              Content Management System
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Content Management Coming Soon
              </h3>
              <p className="text-gray-500 mb-6">
                This feature will allow you to manage website content, blog posts, and media files.
              </p>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
                  <div className="p-4 border rounded-lg">
                    <FileText className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                    <h4 className="font-medium">Blog Posts</h4>
                    <p className="text-sm text-gray-500">Create and manage blog content</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <Image className="h-8 w-8 text-green-500 mx-auto mb-2" />
                    <h4 className="font-medium">Media Library</h4>
                    <p className="text-sm text-gray-500">Manage images and files</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <Video className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                    <h4 className="font-medium">Video Content</h4>
                    <p className="text-sm text-gray-500">Upload and manage videos</p>
                  </div>
                </div>
                <Button disabled>
                  <FileText className="h-4 w-4 mr-2" />
                  Create Content (Coming Soon)
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
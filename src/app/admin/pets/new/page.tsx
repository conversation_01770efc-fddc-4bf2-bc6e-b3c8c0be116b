"use client"

import { useState } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { UserRole, PetStatus, PetSize, PetGender } from "@prisma/client"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Heart, Save, ArrowLeft } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import toast from "react-hot-toast"

interface PetFormData {
  name: string
  species: string
  breed: string
  mixedBreed: boolean
  secondaryBreed: string
  age: number | null
  ageEstimated: boolean
  size: PetSize | null
  weight: number | null
  gender: PetGender
  color: string
  markings: string
  status: PetStatus
  isSpayedNeutered: boolean
  isMicrochipped: boolean
  microchipNumber: string
  arrivalDate: string
  availableDate: string
  organizationId: string
  facilityId: string
  kennelNumber: string
  description: string
  personalityTraits: string[]
  specialNeeds: string
  goodWithKids: boolean | null
  goodWithDogs: boolean | null
  goodWithCats: boolean | null
  goodWithOtherPets: boolean | null
  activityLevel: number | null
  trainingLevel: number | null
  isVaccinated: boolean
  lastVetVisit: string
  medications: string
  allergies: string
  adoptionFee: number | null
  isSponsored: boolean
  sponsorshipAmount: number | null
}

export default function AddNewPet() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<PetFormData>({
    name: "",
    species: "",
    breed: "",
    mixedBreed: false,
    secondaryBreed: "",
    age: null,
    ageEstimated: false,
    size: null,
    weight: null,
    gender: PetGender.UNKNOWN,
    color: "",
    markings: "",
    status: PetStatus.AVAILABLE,
    isSpayedNeutered: false,
    isMicrochipped: false,
    microchipNumber: "",
    arrivalDate: new Date().toISOString().split('T')[0],
    availableDate: "",
    organizationId: "default-org", // This would be dynamic in a real app
    facilityId: "",
    kennelNumber: "",
    description: "",
    personalityTraits: [],
    specialNeeds: "",
    goodWithKids: null,
    goodWithDogs: null,
    goodWithCats: null,
    goodWithOtherPets: null,
    activityLevel: null,
    trainingLevel: null,
    isVaccinated: false,
    lastVetVisit: "",
    medications: "",
    allergies: "",
    adoptionFee: null,
    isSponsored: false,
    sponsorshipAmount: null,
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name || !formData.species || !formData.breed) {
      toast.error("Please fill in all required fields")
      return
    }

    try {
      setLoading(true)
      const response = await fetch("/api/admin/pets", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "Failed to create pet")
      }

      const result = await response.json()
      toast.success("Pet added successfully!")
      router.push(`/admin/pets/${result.pet.id}`)
    } catch (error) {
      console.error("Error creating pet:", error)
      toast.error(error instanceof Error ? error.message : "Failed to create pet")
    } finally {
      setLoading(false)
    }
  }

  const updateFormData = (field: keyof PetFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <AdminLayout
      title="Add New Pet"
      description="Add a new pet to the adoption system"
      requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Heart className="h-6 w-6 text-gray-600" />
            <span className="text-lg font-medium text-gray-900">
              Add New Pet
            </span>
          </div>
          
          <Link href="/admin/pets">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Pets
            </Button>
          </Link>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Pet Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => updateFormData('name', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="species">Species *</Label>
                  <Select value={formData.species} onValueChange={(value) => updateFormData('species', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select species" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Dog">Dog</SelectItem>
                      <SelectItem value="Cat">Cat</SelectItem>
                      <SelectItem value="Rabbit">Rabbit</SelectItem>
                      <SelectItem value="Bird">Bird</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="breed">Primary Breed *</Label>
                  <Input
                    id="breed"
                    value={formData.breed}
                    onChange={(e) => updateFormData('breed', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="secondaryBreed">Secondary Breed (if mixed)</Label>
                  <Input
                    id="secondaryBreed"
                    value={formData.secondaryBreed}
                    onChange={(e) => updateFormData('secondaryBreed', e.target.value)}
                    disabled={!formData.mixedBreed}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="mixedBreed"
                  checked={formData.mixedBreed}
                  onCheckedChange={(checked) => updateFormData('mixedBreed', checked)}
                />
                <Label htmlFor="mixedBreed">Mixed Breed</Label>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="age">Age (months)</Label>
                  <Input
                    id="age"
                    type="number"
                    value={formData.age || ""}
                    onChange={(e) => updateFormData('age', e.target.value ? parseInt(e.target.value) : null)}
                  />
                </div>
                <div>
                  <Label htmlFor="size">Size</Label>
                  <Select value={formData.size || ""} onValueChange={(value) => updateFormData('size', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select size" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={PetSize.EXTRA_SMALL}>Extra Small</SelectItem>
                      <SelectItem value={PetSize.SMALL}>Small</SelectItem>
                      <SelectItem value={PetSize.MEDIUM}>Medium</SelectItem>
                      <SelectItem value={PetSize.LARGE}>Large</SelectItem>
                      <SelectItem value={PetSize.EXTRA_LARGE}>Extra Large</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="weight">Weight (lbs)</Label>
                  <Input
                    id="weight"
                    type="number"
                    step="0.1"
                    value={formData.weight || ""}
                    onChange={(e) => updateFormData('weight', e.target.value ? parseFloat(e.target.value) : null)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="gender">Gender</Label>
                  <Select value={formData.gender} onValueChange={(value) => updateFormData('gender', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={PetGender.MALE}>Male</SelectItem>
                      <SelectItem value={PetGender.FEMALE}>Female</SelectItem>
                      <SelectItem value={PetGender.UNKNOWN}>Unknown</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="color">Color</Label>
                  <Input
                    id="color"
                    value={formData.color}
                    onChange={(e) => updateFormData('color', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="status">Status</Label>
                  <Select value={formData.status} onValueChange={(value) => updateFormData('status', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={PetStatus.AVAILABLE}>Available</SelectItem>
                      <SelectItem value={PetStatus.PENDING}>Pending</SelectItem>
                      <SelectItem value={PetStatus.MEDICAL_HOLD}>Medical Hold</SelectItem>
                      <SelectItem value={PetStatus.FOSTER_CARE}>Foster Care</SelectItem>
                      <SelectItem value={PetStatus.QUARANTINE}>Quarantine</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => updateFormData('description', e.target.value)}
                  rows={4}
                  placeholder="Describe the pet's personality, behavior, and any special characteristics..."
                />
              </div>
            </CardContent>
          </Card>

          {/* Medical Information */}
          <Card>
            <CardHeader>
              <CardTitle>Medical Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isSpayedNeutered"
                      checked={formData.isSpayedNeutered}
                      onCheckedChange={(checked) => updateFormData('isSpayedNeutered', checked)}
                    />
                    <Label htmlFor="isSpayedNeutered">Spayed/Neutered</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isMicrochipped"
                      checked={formData.isMicrochipped}
                      onCheckedChange={(checked) => updateFormData('isMicrochipped', checked)}
                    />
                    <Label htmlFor="isMicrochipped">Microchipped</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isVaccinated"
                      checked={formData.isVaccinated}
                      onCheckedChange={(checked) => updateFormData('isVaccinated', checked)}
                    />
                    <Label htmlFor="isVaccinated">Up to Date on Vaccinations</Label>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="microchipNumber">Microchip Number</Label>
                    <Input
                      id="microchipNumber"
                      value={formData.microchipNumber}
                      onChange={(e) => updateFormData('microchipNumber', e.target.value)}
                      disabled={!formData.isMicrochipped}
                    />
                  </div>

                  <div>
                    <Label htmlFor="adoptionFee">Adoption Fee ($)</Label>
                    <Input
                      id="adoptionFee"
                      type="number"
                      step="0.01"
                      value={formData.adoptionFee || ""}
                      onChange={(e) => updateFormData('adoptionFee', e.target.value ? parseFloat(e.target.value) : null)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <Link href="/admin/pets">
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </Link>
            <Button type="submit" disabled={loading}>
              <Save className="h-4 w-4 mr-2" />
              {loading ? "Adding Pet..." : "Add Pet"}
            </Button>
          </div>
        </form>
      </div>
    </AdminLayout>
  )
}
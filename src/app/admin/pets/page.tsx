"use client"

import { useState, useEffect } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { UserRole } from "@prisma/client"
import { 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Filter,
  MoreHorizontal,
  Heart,
  MapPin,
  Calendar,
  DollarSign,
  Users,
  PawPrint,
  Stethoscope,
  Camera,
  FileText,
  Download,
  Upload,
  RefreshCw,
  SortAsc,
  SortDesc,
  Grid,
  List,
  Settings,
  AlertCircle,
  CheckCircle,
  Clock,
  Package
} from "lucide-react"
import Link from "next/link"
import { toast } from "react-hot-toast"

interface Pet {
  id: string
  name: string
  species: string
  breed: string
  age: number
  size: string
  gender: string
  color: string
  status: string
  adoptionFee: number
  arrivalDate: string
  photos: Array<{ url: string; isPrimary: boolean }>
  organization: {
    name: string
    city: string
    state: string
  }
  _count: {
    applications: number
    favorites: number
  }
}

const statusColors = {
  AVAILABLE: "bg-green-100 text-green-800",
  PENDING: "bg-yellow-100 text-yellow-800",
  ADOPTED: "bg-blue-100 text-blue-800",
  MEDICAL_HOLD: "bg-red-100 text-red-800",
  FOSTER_CARE: "bg-purple-100 text-purple-800",
  QUARANTINE: "bg-orange-100 text-orange-800",
  UNAVAILABLE: "bg-gray-100 text-gray-800"
}

const statusIcons = {
  AVAILABLE: CheckCircle,
  PENDING: Clock,
  ADOPTED: Heart,
  MEDICAL_HOLD: Stethoscope,
  FOSTER_CARE: Users,
  QUARANTINE: AlertCircle,
  UNAVAILABLE: Package
}

export default function AdminPetsPage() {
  const [pets, setPets] = useState<Pet[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [speciesFilter, setSpeciesFilter] = useState("all")
  const [sortBy, setSortBy] = useState("arrivalDate")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [selectedPets, setSelectedPets] = useState<string[]>([])

  useEffect(() => {
    fetchPets()
  }, [])

  const fetchPets = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/admin/pets")
      
      if (!response.ok) {
        throw new Error("Failed to fetch pets")
      }
      
      const data = await response.json()
      setPets(data.pets || [])
    } catch (error) {
      console.error("Error fetching pets:", error)
      toast.error("Failed to load pets")
    } finally {
      setLoading(false)
    }
  }

  const handleDeletePet = async (petId: string) => {
    if (!confirm("Are you sure you want to delete this pet? This action cannot be undone.")) {
      return
    }

    try {
      const response = await fetch(`/api/admin/pets/${petId}`, {
        method: "DELETE"
      })

      if (!response.ok) {
        throw new Error("Failed to delete pet")
      }

      setPets(pets.filter(pet => pet.id !== petId))
      toast.success("Pet deleted successfully")
    } catch (error) {
      console.error("Error deleting pet:", error)
      toast.error("Failed to delete pet")
    }
  }

  const handleBulkAction = async (action: string) => {
    if (selectedPets.length === 0) {
      toast.error("Please select pets first")
      return
    }

    try {
      const response = await fetch("/api/admin/pets/bulk", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          action,
          petIds: selectedPets
        })
      })

      if (!response.ok) {
        throw new Error("Failed to perform bulk action")
      }

      toast.success(`Bulk action completed successfully`)
      setSelectedPets([])
      fetchPets()
    } catch (error) {
      console.error("Error performing bulk action:", error)
      toast.error("Failed to perform bulk action")
    }
  }

  const filteredPets = pets.filter(pet => {
    const matchesSearch = pet.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pet.breed.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pet.species.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || pet.status === statusFilter
    const matchesSpecies = speciesFilter === "all" || pet.species.toLowerCase() === speciesFilter.toLowerCase()
    
    return matchesSearch && matchesStatus && matchesSpecies
  })

  const sortedPets = [...filteredPets].sort((a, b) => {
    let aValue: any = a[sortBy as keyof Pet]
    let bValue: any = b[sortBy as keyof Pet]
    
    if (sortBy === "arrivalDate") {
      aValue = new Date(aValue).getTime()
      bValue = new Date(bValue).getTime()
    }
    
    if (sortOrder === "asc") {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatStatus = (status: string) => {
    return status.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
  }

  const getStatusIcon = (status: string) => {
    const IconComponent = statusIcons[status as keyof typeof statusIcons] || Package
    return <IconComponent className="h-4 w-4" />
  }

  return (
    <AdminLayout
      title="Pet Management"
      description="Manage all pets in the system - add, edit, and track their status"
      requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
    >
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      ) : (
        <div className="space-y-6">
          {/* Header Actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <PawPrint className="h-6 w-6 text-gray-600" />
              <span className="text-lg font-medium text-gray-900">
                {sortedPets.length} pets found
              </span>
            </div>
            <div className="flex gap-2">
              <Link href="/admin/pets/new">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add New Pet
                </Button>
              </Link>
              <Button variant="outline">
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>

          {/* Filters and Search */}
          <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4 mb-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search pets by name, breed, or species..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="AVAILABLE">Available</option>
                <option value="PENDING">Pending</option>
                <option value="ADOPTED">Adopted</option>
                <option value="MEDICAL_HOLD">Medical Hold</option>
                <option value="FOSTER_CARE">Foster Care</option>
                <option value="QUARANTINE">Quarantine</option>
                <option value="UNAVAILABLE">Unavailable</option>
              </select>
              <select
                value={speciesFilter}
                onChange={(e) => setSpeciesFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Species</option>
                <option value="dog">Dogs</option>
                <option value="cat">Cats</option>
                <option value="bird">Birds</option>
                <option value="rabbit">Rabbits</option>
                <option value="other">Other</option>
              </select>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="arrivalDate">Arrival Date</option>
                <option value="name">Name</option>
                <option value="age">Age</option>
                <option value="adoptionFee">Adoption Fee</option>
              </select>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
              >
                {sortOrder === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
              </Button>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">
                {sortedPets.length} pets found
              </span>
              {selectedPets.length > 0 && (
                <div className="flex items-center gap-2 ml-4">
                  <span className="text-sm text-gray-600">
                    {selectedPets.length} selected
                  </span>
                  <Button size="sm" variant="outline" onClick={() => handleBulkAction("delete")}>
                    <Trash2 className="h-3 w-3 mr-1" />
                    Delete
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => handleBulkAction("available")}>
                    Make Available
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => handleBulkAction("unavailable")}>
                    Make Unavailable
                  </Button>
                </div>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("grid")}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("list")}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
          </CardContent>
          </Card>

          {/* Pets Grid/List */}
          {viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {sortedPets.map((pet) => (
            <Card key={pet.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="p-4">
                <div className="flex items-start justify-between">
                  <input
                    type="checkbox"
                    checked={selectedPets.includes(pet.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedPets([...selectedPets, pet.id])
                      } else {
                        setSelectedPets(selectedPets.filter(id => id !== pet.id))
                      }
                    }}
                    className="mt-1"
                  />
                  <Badge className={statusColors[pet.status as keyof typeof statusColors]}>
                    {getStatusIcon(pet.status)}
                    <span className="ml-1">{formatStatus(pet.status)}</span>
                  </Badge>
                </div>
                <div className="aspect-square bg-gray-200 rounded-lg overflow-hidden mb-3">
                  {pet.photos.find(p => p.isPrimary)?.url ? (
                    <img
                      src={pet.photos.find(p => p.isPrimary)?.url}
                      alt={pet.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <PawPrint className="h-12 w-12 text-gray-400" />
                    </div>
                  )}
                </div>
                <CardTitle className="text-lg">{pet.name}</CardTitle>
                <CardDescription>
                  {pet.breed} • {pet.species} • {pet.age} years old
                </CardDescription>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <div className="space-y-2 text-sm text-gray-600 mb-4">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-3 w-3" />
                    {pet.organization.city}, {pet.organization.state}
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-3 w-3" />
                    Arrived {formatDate(pet.arrivalDate)}
                  </div>
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-3 w-3" />
                    ${pet.adoptionFee}
                  </div>
                  <div className="flex items-center gap-2">
                    <Heart className="h-3 w-3" />
                    {pet._count.favorites} favorites
                  </div>
                  <div className="flex items-center gap-2">
                    <FileText className="h-3 w-3" />
                    {pet._count.applications} applications
                  </div>
                </div>
                <div className="flex gap-2">
                  <Link href={`/admin/pets/${pet.id}`} className="flex-1">
                    <Button variant="outline" size="sm" className="w-full">
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                  </Link>
                  <Link href={`/pets/${pet.id}`} target="_blank">
                    <Button variant="outline" size="sm">
                      <Eye className="h-3 w-3" />
                    </Button>
                  </Link>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleDeletePet(pet.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b">
                  <tr>
                    <th className="px-4 py-3 text-left">
                      <input
                        type="checkbox"
                        checked={selectedPets.length === sortedPets.length && sortedPets.length > 0}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedPets(sortedPets.map(pet => pet.id))
                          } else {
                            setSelectedPets([])
                          }
                        }}
                      />
                    </th>
                    <th className="px-4 py-3 text-left font-medium text-gray-900">Pet</th>
                    <th className="px-4 py-3 text-left font-medium text-gray-900">Status</th>
                    <th className="px-4 py-3 text-left font-medium text-gray-900">Location</th>
                    <th className="px-4 py-3 text-left font-medium text-gray-900">Arrival Date</th>
                    <th className="px-4 py-3 text-left font-medium text-gray-900">Fee</th>
                    <th className="px-4 py-3 text-left font-medium text-gray-900">Applications</th>
                    <th className="px-4 py-3 text-left font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {sortedPets.map((pet) => (
                    <tr key={pet.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3">
                        <input
                          type="checkbox"
                          checked={selectedPets.includes(pet.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedPets([...selectedPets, pet.id])
                            } else {
                              setSelectedPets(selectedPets.filter(id => id !== pet.id))
                            }
                          }}
                        />
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                            {pet.photos.find(p => p.isPrimary)?.url ? (
                              <img
                                src={pet.photos.find(p => p.isPrimary)?.url}
                                alt={pet.name}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <PawPrint className="h-4 w-4 text-gray-400" />
                              </div>
                            )}
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{pet.name}</div>
                            <div className="text-sm text-gray-600">{pet.breed} • {pet.species}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <Badge className={statusColors[pet.status as keyof typeof statusColors]}>
                          {getStatusIcon(pet.status)}
                          <span className="ml-1">{formatStatus(pet.status)}</span>
                        </Badge>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600">
                        {pet.organization.city}, {pet.organization.state}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600">
                        {formatDate(pet.arrivalDate)}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600">
                        ${pet.adoptionFee}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600">
                        {pet._count.applications}
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex items-center gap-2">
                          <Link href={`/admin/pets/${pet.id}`}>
                            <Button variant="outline" size="sm">
                              <Edit className="h-3 w-3" />
                            </Button>
                          </Link>
                          <Link href={`/pets/${pet.id}`} target="_blank">
                            <Button variant="outline" size="sm">
                              <Eye className="h-3 w-3" />
                            </Button>
                          </Link>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleDeletePet(pet.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {sortedPets.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <PawPrint className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No pets found</h3>
            <p className="text-gray-600 mb-6">
              {searchTerm || statusFilter !== "all" || speciesFilter !== "all"
                ? "Try adjusting your search criteria or filters."
                : "Get started by adding your first pet to the system."
              }
            </p>
            <Link href="/admin/pets/new">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add New Pet
              </Button>
            </Link>
          </CardContent>
        </Card>
          )}
        </div>
      )}
    </AdminLayout>
  )
}

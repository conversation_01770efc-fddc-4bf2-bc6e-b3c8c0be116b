"use client"

import { AdminLayout } from "@/components/admin/admin-layout"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { UserRole } from "@prisma/client"
import { MessageSquare, Mail, Bell, Send } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export default function CommunicationsManagement() {
  return (
    <AdminLayout
      title="Communications"
      description="Send notifications, emails, and messages"
      requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
    >
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <MessageSquare className="h-6 w-6 mr-2" />
              Communications Center
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <MessageSquare className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Communications Center Coming Soon
              </h3>
              <p className="text-gray-500 mb-6">
                This feature will allow you to send emails, notifications, and manage communications.
              </p>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
                  <div className="p-4 border rounded-lg">
                    <Mail className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                    <h4 className="font-medium">Email Campaigns</h4>
                    <p className="text-sm text-gray-500">Send newsletters and updates</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <Bell className="h-8 w-8 text-orange-500 mx-auto mb-2" />
                    <h4 className="font-medium">Push Notifications</h4>
                    <p className="text-sm text-gray-500">Send instant notifications</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <Send className="h-8 w-8 text-green-500 mx-auto mb-2" />
                    <h4 className="font-medium">SMS Messages</h4>
                    <p className="text-sm text-gray-500">Send text message alerts</p>
                  </div>
                </div>
                <Button disabled>
                  <Send className="h-4 w-4 mr-2" />
                  Send Message (Coming Soon)
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
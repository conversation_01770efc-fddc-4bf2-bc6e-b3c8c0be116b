"use client"

import { useState, useEffect } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { UserRole, DonationType, PaymentStatus } from "@prisma/client"
import {
  Search,
  Plus,
  Edit,
  Eye,
  Filter,
  Download,
  MoreHorizontal,
  DollarSign,
  TrendingUp,
  Heart,
  Calendar,
  CheckCircle,
  AlertCircle,
  Clock,
  RefreshCw,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import Link from "next/link"
import toast from "react-hot-toast"

interface Donation {
  id: string
  userId: string | null
  type: DonationType
  amount: number
  currency: string
  paymentStatus: PaymentStatus
  paymentMethod: string | null
  stripePaymentId: string | null
  donorName: string | null
  donorEmail: string | null
  isAnonymous: boolean
  designation: string | null
  petId: string | null
  isMemorial: boolean
  memorialName: string | null
  memorialMessage: string | null
  notifyFamily: boolean
  familyEmail: string | null
  isRecurring: boolean
  recurringInterval: string | null
  nextDonationDate: string | null
  taxDeductible: boolean
  receiptSent: boolean
  receiptSentAt: string | null
  createdAt: string
  updatedAt: string
  user?: {
    id: string
    name: string | null
    email: string
  }
}

interface DonationsResponse {
  donations: Donation[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  stats: {
    totalAmount: number
    totalCount: number
    averageAmount: number
    thisMonthAmount: number
    recurringAmount: number
  }
}

export default function DonationsManagement() {
  const [donations, setDonations] = useState<Donation[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState<string>("all")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState<DonationsResponse['pagination'] | null>(null)
  const [stats, setStats] = useState<DonationsResponse['stats'] | null>(null)

  const fetchDonations = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        ...(searchTerm && { search: searchTerm }),
        ...(selectedType !== "all" && { type: selectedType }),
        ...(selectedStatus !== "all" && { status: selectedStatus }),
      })

      const response = await fetch(`/api/admin/donations?${params}`)
      
      if (!response.ok) {
        throw new Error("Failed to fetch donations")
      }

      const data: DonationsResponse = await response.json()
      setDonations(data.donations)
      setPagination(data.pagination)
      setStats(data.stats)
    } catch (error) {
      console.error("Error fetching donations:", error)
      toast.error("Failed to fetch donations")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDonations()
  }, [currentPage, searchTerm, selectedType, selectedStatus])

  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setCurrentPage(1)
  }

  const handleTypeFilter = (value: string) => {
    setSelectedType(value)
    setCurrentPage(1)
  }

  const handleStatusFilter = (value: string) => {
    setSelectedStatus(value)
    setCurrentPage(1)
  }

  const handleSendReceipt = async (donationId: string) => {
    try {
      const response = await fetch(`/api/admin/donations/${donationId}/receipt`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (!response.ok) {
        throw new Error("Failed to send receipt")
      }

      toast.success("Receipt sent successfully")
      fetchDonations()
    } catch (error) {
      console.error("Error sending receipt:", error)
      toast.error("Failed to send receipt")
    }
  }

  const getStatusBadgeColor = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.COMPLETED:
        return "bg-green-100 text-green-800"
      case PaymentStatus.PENDING:
        return "bg-yellow-100 text-yellow-800"
      case PaymentStatus.FAILED:
        return "bg-red-100 text-red-800"
      case PaymentStatus.REFUNDED:
        return "bg-gray-100 text-gray-800"
      case PaymentStatus.CANCELLED:
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.COMPLETED:
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case PaymentStatus.PENDING:
        return <Clock className="h-4 w-4 text-yellow-500" />
      case PaymentStatus.FAILED:
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case PaymentStatus.REFUNDED:
        return <RefreshCw className="h-4 w-4 text-gray-500" />
      case PaymentStatus.CANCELLED:
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getTypeBadgeColor = (type: DonationType) => {
    switch (type) {
      case DonationType.ONE_TIME:
        return "bg-blue-100 text-blue-800"
      case DonationType.RECURRING:
        return "bg-green-100 text-green-800"
      case DonationType.MEMORIAL:
        return "bg-purple-100 text-purple-800"
      case DonationType.SPONSORSHIP:
        return "bg-orange-100 text-orange-800"
      case DonationType.CORPORATE:
        return "bg-indigo-100 text-indigo-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <AdminLayout
      title="Donations Management"
      description="Manage donations, receipts, and fundraising"
      requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center space-x-2">
            <DollarSign className="h-6 w-6 text-gray-600" />
            <span className="text-lg font-medium text-gray-900">
              {pagination?.total || 0} Donations
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Link href="/admin/donations/new">
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Record Donation
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <DollarSign className="h-8 w-8 text-green-500" />
                  <div className="ml-4">
                    <p className="text-2xl font-bold">
                      {formatCurrency(stats.totalAmount)}
                    </p>
                    <p className="text-sm text-gray-600">Total Raised</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <TrendingUp className="h-8 w-8 text-blue-500" />
                  <div className="ml-4">
                    <p className="text-2xl font-bold">
                      {formatCurrency(stats.averageAmount)}
                    </p>
                    <p className="text-sm text-gray-600">Average Donation</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Calendar className="h-8 w-8 text-purple-500" />
                  <div className="ml-4">
                    <p className="text-2xl font-bold">
                      {formatCurrency(stats.thisMonthAmount)}
                    </p>
                    <p className="text-sm text-gray-600">This Month</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <RefreshCw className="h-8 w-8 text-orange-500" />
                  <div className="ml-4">
                    <p className="text-2xl font-bold">
                      {formatCurrency(stats.recurringAmount)}
                    </p>
                    <p className="text-sm text-gray-600">Recurring Monthly</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Heart className="h-8 w-8 text-red-500" />
                  <div className="ml-4">
                    <p className="text-2xl font-bold">{stats.totalCount}</p>
                    <p className="text-sm text-gray-600">Total Donations</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by donor name, email, or designation..."
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <Select value={selectedType} onValueChange={handleTypeFilter}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value={DonationType.ONE_TIME}>One Time</SelectItem>
                  <SelectItem value={DonationType.RECURRING}>Recurring</SelectItem>
                  <SelectItem value={DonationType.MEMORIAL}>Memorial</SelectItem>
                  <SelectItem value={DonationType.SPONSORSHIP}>Sponsorship</SelectItem>
                  <SelectItem value={DonationType.CORPORATE}>Corporate</SelectItem>
                </SelectContent>
              </Select>

              <Select value={selectedStatus} onValueChange={handleStatusFilter}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value={PaymentStatus.COMPLETED}>Completed</SelectItem>
                  <SelectItem value={PaymentStatus.PENDING}>Pending</SelectItem>
                  <SelectItem value={PaymentStatus.FAILED}>Failed</SelectItem>
                  <SelectItem value={PaymentStatus.REFUNDED}>Refunded</SelectItem>
                  <SelectItem value={PaymentStatus.CANCELLED}>Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Donations List */}
        <Card>
          <CardHeader>
            <CardTitle>Donations</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-20 bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>
            ) : donations.length === 0 ? (
              <div className="text-center py-8">
                <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No donations found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {donations.map((donation) => (
                  <div
                    key={donation.id}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                        {getStatusIcon(donation.paymentStatus)}
                      </div>
                      
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="font-medium text-gray-900">
                            {formatCurrency(donation.amount, donation.currency)}
                          </p>
                          <Badge className={getTypeBadgeColor(donation.type)}>
                            {donation.type.replace('_', ' ')}
                          </Badge>
                          <Badge className={getStatusBadgeColor(donation.paymentStatus)}>
                            {donation.paymentStatus}
                          </Badge>
                          {donation.isRecurring && (
                            <Badge variant="outline">Recurring</Badge>
                          )}
                          {donation.isMemorial && (
                            <Badge variant="outline">Memorial</Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-500">
                          {donation.isAnonymous 
                            ? "Anonymous Donor" 
                            : donation.donorName || donation.user?.name || donation.donorEmail
                          }
                        </p>
                        <div className="flex items-center space-x-4 text-xs text-gray-400 mt-1">
                          <span>{formatDate(donation.createdAt)}</span>
                          {donation.designation && (
                            <span>For: {donation.designation}</span>
                          )}
                          {donation.paymentMethod && (
                            <span>Via: {donation.paymentMethod}</span>
                          )}
                          {donation.receiptSent ? (
                            <span className="text-green-600">✓ Receipt Sent</span>
                          ) : (
                            <span className="text-orange-600">Receipt Pending</span>
                          )}
                        </div>
                        {donation.memorialName && (
                          <p className="text-sm text-purple-600 mt-1">
                            In memory of: {donation.memorialName}
                          </p>
                        )}
                      </div>
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/donations/${donation.id}`}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/donations/${donation.id}/edit`}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Donation
                          </Link>
                        </DropdownMenuItem>
                        {!donation.receiptSent && donation.paymentStatus === PaymentStatus.COMPLETED && (
                          <DropdownMenuItem
                            onClick={() => handleSendReceipt(donation.id)}
                          >
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Send Receipt
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {pagination && pagination.totalPages > 1 && (
              <div className="flex items-center justify-between mt-6">
                <p className="text-sm text-gray-500">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                  {pagination.total} donations
                </p>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(pagination.page - 1)}
                    disabled={!pagination.hasPrev}
                  >
                    Previous
                  </Button>
                  <span className="text-sm text-gray-500">
                    Page {pagination.page} of {pagination.totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(pagination.page + 1)}
                    disabled={!pagination.hasNext}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
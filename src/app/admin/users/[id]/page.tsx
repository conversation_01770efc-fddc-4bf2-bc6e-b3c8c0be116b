"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { AdminLayout } from "@/components/admin/admin-layout"
import { Button } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { UserRole, UserStatus } from "@prisma/client"
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Edit,
  Shield,
  FileText,
  Heart,
  Users,
  DollarSign,
  Activity,
  ArrowLeft,
} from "lucide-react"
import Link from "next/link"
import toast from "react-hot-toast"

interface UserDetail {
  id: string
  email: string
  name: string | null
  firstName: string | null
  lastName: string | null
  phone: string | null
  role: UserRole
  status: UserStatus
  image: string | null
  createdAt: string
  updatedAt: string
  emailVerified: string | null
  dateOfBirth: string | null
  address: string | null
  city: string | null
  state: string | null
  zipCode: string | null
  country: string | null
  emergencyContactName: string | null
  emergencyContactPhone: string | null
  emergencyContactRelation: string | null
  _count: {
    adoptionApplications: number
    fosterApplications: number
    volunteerApplications: number
    favoritePets: number
  }
  adoptionApplications: Array<{
    id: string
    status: string
    submittedAt: string
    pet: {
      id: string
      name: string
      species: string
      breed: string
    }
  }>
  fosterApplications: Array<{
    id: string
    status: string
    submittedAt: string
  }>
  volunteerApplications: Array<{
    id: string
    status: string
    submittedAt: string
  }>
}

export default function UserDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [user, setUser] = useState<UserDetail | null>(null)
  const [loading, setLoading] = useState(true)

  const userId = params.id as string

  useEffect(() => {
    fetchUserDetail()
  }, [userId])

  const fetchUserDetail = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/users/${userId}`)
      
      if (!response.ok) {
        throw new Error("Failed to fetch user details")
      }

      const data = await response.json()
      setUser(data.user)
    } catch (error) {
      console.error("Error fetching user details:", error)
      toast.error("Failed to fetch user details")
    } finally {
      setLoading(false)
    }
  }

  const getRoleBadgeColor = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return "bg-red-100 text-red-800"
      case UserRole.STAFF:
        return "bg-blue-100 text-blue-800"
      case UserRole.VOLUNTEER:
        return "bg-green-100 text-green-800"
      case UserRole.ADOPTER:
        return "bg-purple-100 text-purple-800"
      case UserRole.PARTNER_ORG:
        return "bg-orange-100 text-orange-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusBadgeColor = (status: UserStatus) => {
    switch (status) {
      case UserStatus.ACTIVE:
        return "bg-green-100 text-green-800"
      case UserStatus.INACTIVE:
        return "bg-gray-100 text-gray-800"
      case UserStatus.SUSPENDED:
        return "bg-red-100 text-red-800"
      case UserStatus.PENDING_VERIFICATION:
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Not provided"
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <AdminLayout
        title="User Details"
        description="View user information and activity"
        requiredRoles={[UserRole.ADMIN]}
      >
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </AdminLayout>
    )
  }

  if (!user) {
    return (
      <AdminLayout
        title="User Not Found"
        description="The requested user could not be found"
        requiredRoles={[UserRole.ADMIN]}
      >
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">User Not Found</h2>
          <p className="text-gray-600 mb-6">The user you're looking for doesn't exist or has been deleted.</p>
          <Link href="/admin/users">
            <Button>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Users
            </Button>
          </Link>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout
      title={user.name || `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'User Details'}
      description="View user information and activity"
      requiredRoles={[UserRole.ADMIN]}
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Link href="/admin/users">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Users
            </Button>
          </Link>
          
          <div className="flex items-center space-x-2">
            <Link href={`/admin/users/${user.id}/edit`}>
              <Button>
                <Edit className="h-4 w-4 mr-2" />
                Edit User
              </Button>
            </Link>
          </div>
        </div>

        {/* User Overview */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-4">
              <div className="h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-xl font-medium text-blue-700">
                  {user.name?.charAt(0).toUpperCase() || user.email.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <h2 className="text-2xl font-bold">
                    {user.name || `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'No name'}
                  </h2>
                  <Badge className={getRoleBadgeColor(user.role)}>
                    {user.role}
                  </Badge>
                  <Badge className={getStatusBadgeColor(user.status)}>
                    {user.status}
                  </Badge>
                </div>
                <p className="text-gray-600">{user.email}</p>
                <p className="text-sm text-gray-500">
                  Member since {formatDate(user.createdAt)}
                </p>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Activity Summary */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">{user._count.adoptionApplications}</p>
                  <p className="text-sm text-gray-600">Adoption Apps</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Heart className="h-8 w-8 text-red-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">{user._count.favoritePets}</p>
                  <p className="text-sm text-gray-600">Favorite Pets</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">{user._count.volunteerApplications}</p>
                  <p className="text-sm text-gray-600">Volunteer Apps</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Activity className="h-8 w-8 text-purple-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">{user._count.fosterApplications}</p>
                  <p className="text-sm text-gray-600">Foster Apps</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Information */}
        <Tabs defaultValue="profile" className="space-y-4">
          <TabsList>
            <TabsTrigger value="profile">Profile Information</TabsTrigger>
            <TabsTrigger value="applications">Applications</TabsTrigger>
            <TabsTrigger value="activity">Activity Log</TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Personal Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <User className="h-5 w-5 mr-2" />
                    Personal Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Full Name</label>
                    <p className="text-gray-900">
                      {user.name || `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Not provided'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Email</label>
                    <p className="text-gray-900">{user.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Phone</label>
                    <p className="text-gray-900">{user.phone || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Date of Birth</label>
                    <p className="text-gray-900">{formatDate(user.dateOfBirth)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Email Verified</label>
                    <p className="text-gray-900">
                      {user.emailVerified ? formatDate(user.emailVerified) : 'Not verified'}
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Address Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <MapPin className="h-5 w-5 mr-2" />
                    Address Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Address</label>
                    <p className="text-gray-900">{user.address || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">City</label>
                    <p className="text-gray-900">{user.city || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">State</label>
                    <p className="text-gray-900">{user.state || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">ZIP Code</label>
                    <p className="text-gray-900">{user.zipCode || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Country</label>
                    <p className="text-gray-900">{user.country || 'Not provided'}</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Emergency Contact */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Phone className="h-5 w-5 mr-2" />
                  Emergency Contact
                </CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Name</label>
                  <p className="text-gray-900">{user.emergencyContactName || 'Not provided'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Phone</label>
                  <p className="text-gray-900">{user.emergencyContactPhone || 'Not provided'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Relationship</label>
                  <p className="text-gray-900">{user.emergencyContactRelation || 'Not provided'}</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="applications" className="space-y-4">
            {/* Adoption Applications */}
            <Card>
              <CardHeader>
                <CardTitle>Adoption Applications ({user._count.adoptionApplications})</CardTitle>
              </CardHeader>
              <CardContent>
                {user.adoptionApplications.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">No adoption applications</p>
                ) : (
                  <div className="space-y-4">
                    {user.adoptionApplications.map((application) => (
                      <div key={application.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <h4 className="font-medium">{application.pet.name}</h4>
                          <p className="text-sm text-gray-600">
                            {application.pet.species} • {application.pet.breed}
                          </p>
                          <p className="text-xs text-gray-500">
                            Submitted {formatDate(application.submittedAt)}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">{application.status}</Badge>
                          <Link href={`/admin/applications/${application.id}`}>
                            <Button variant="outline" size="sm">View</Button>
                          </Link>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Volunteer Applications */}
            <Card>
              <CardHeader>
                <CardTitle>Volunteer Applications ({user._count.volunteerApplications})</CardTitle>
              </CardHeader>
              <CardContent>
                {user.volunteerApplications.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">No volunteer applications</p>
                ) : (
                  <div className="space-y-4">
                    {user.volunteerApplications.map((application) => (
                      <div key={application.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <h4 className="font-medium">Volunteer Application</h4>
                          <p className="text-xs text-gray-500">
                            Submitted {formatDate(application.submittedAt)}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">{application.status}</Badge>
                          <Link href={`/admin/volunteers/${application.id}`}>
                            <Button variant="outline" size="sm">View</Button>
                          </Link>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Foster Applications */}
            <Card>
              <CardHeader>
                <CardTitle>Foster Applications ({user._count.fosterApplications})</CardTitle>
              </CardHeader>
              <CardContent>
                {user.fosterApplications.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">No foster applications</p>
                ) : (
                  <div className="space-y-4">
                    {user.fosterApplications.map((application) => (
                      <div key={application.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <h4 className="font-medium">Foster Application</h4>
                          <p className="text-xs text-gray-500">
                            Submitted {formatDate(application.submittedAt)}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">{application.status}</Badge>
                          <Link href={`/admin/foster/${application.id}`}>
                            <Button variant="outline" size="sm">View</Button>
                          </Link>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="activity">
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500 text-center py-8">Activity log feature coming soon</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  )
}

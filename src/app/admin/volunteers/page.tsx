"use client"

import { useState, useEffect } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { UserRole, VolunteerStatus } from "@prisma/client"
import {
  Search,
  Plus,
  Edit,
  Eye,
  Filter,
  Download,
  MoreHorizontal,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  UserCheck,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import Link from "next/link"
import toast from "react-hot-toast"

interface VolunteerProfile {
  id: string
  userId: string
  status: VolunteerStatus
  skills: string[]
  interests: string[]
  availability: any
  volunteerExperience: string | null
  animalExperience: string | null
  specialSkills: string | null
  preferredActivities: string[]
  maxHoursPerWeek: number | null
  orientationCompleted: boolean
  orientationDate: string | null
  backgroundCheckDate: string | null
  backgroundCheckStatus: string | null
  createdAt: string
  updatedAt: string
  user: {
    id: string
    name: string | null
    email: string
    phone: string | null
    firstName: string | null
    lastName: string | null
  }
  _count: {
    volunteerHours: number
    assignments: number
  }
}

interface VolunteersResponse {
  volunteers: VolunteerProfile[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export default function VolunteersManagement() {
  const [volunteers, setVolunteers] = useState<VolunteerProfile[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState<VolunteersResponse['pagination'] | null>(null)

  const fetchVolunteers = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        ...(searchTerm && { search: searchTerm }),
        ...(selectedStatus !== "all" && { status: selectedStatus }),
      })

      const response = await fetch(`/api/admin/volunteers?${params}`)
      
      if (!response.ok) {
        throw new Error("Failed to fetch volunteers")
      }

      const data: VolunteersResponse = await response.json()
      setVolunteers(data.volunteers)
      setPagination(data.pagination)
    } catch (error) {
      console.error("Error fetching volunteers:", error)
      toast.error("Failed to fetch volunteers")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchVolunteers()
  }, [currentPage, searchTerm, selectedStatus])

  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setCurrentPage(1)
  }

  const handleStatusFilter = (value: string) => {
    setSelectedStatus(value)
    setCurrentPage(1)
  }

  const handleUpdateStatus = async (volunteerId: string, newStatus: VolunteerStatus) => {
    try {
      const response = await fetch(`/api/admin/volunteers/${volunteerId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (!response.ok) {
        throw new Error("Failed to update volunteer status")
      }

      toast.success("Volunteer status updated successfully")
      fetchVolunteers()
    } catch (error) {
      console.error("Error updating volunteer status:", error)
      toast.error("Failed to update volunteer status")
    }
  }

  const getStatusBadgeColor = (status: VolunteerStatus) => {
    switch (status) {
      case VolunteerStatus.ACTIVE:
        return "bg-green-100 text-green-800"
      case VolunteerStatus.INACTIVE:
        return "bg-gray-100 text-gray-800"
      case VolunteerStatus.TRAINING:
        return "bg-blue-100 text-blue-800"
      case VolunteerStatus.ON_HOLD:
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: VolunteerStatus) => {
    switch (status) {
      case VolunteerStatus.ACTIVE:
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case VolunteerStatus.INACTIVE:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
      case VolunteerStatus.TRAINING:
        return <Clock className="h-4 w-4 text-blue-500" />
      case VolunteerStatus.ON_HOLD:
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <AdminLayout
      title="Volunteer Management"
      description="Manage volunteer applications, assignments, and hours"
      requiredRoles={[UserRole.STAFF, UserRole.ADMIN]}
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center space-x-2">
            <Users className="h-6 w-6 text-gray-600" />
            <span className="text-lg font-medium text-gray-900">
              {pagination?.total || 0} Volunteers
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Link href="/admin/volunteers/new">
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Volunteer
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">
                    {volunteers.filter(v => v.status === VolunteerStatus.ACTIVE).length}
                  </p>
                  <p className="text-sm text-gray-600">Active Volunteers</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">
                    {volunteers.filter(v => v.status === VolunteerStatus.TRAINING).length}
                  </p>
                  <p className="text-sm text-gray-600">In Training</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <UserCheck className="h-8 w-8 text-purple-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">
                    {volunteers.filter(v => v.orientationCompleted).length}
                  </p>
                  <p className="text-sm text-gray-600">Orientation Complete</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-orange-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold">
                    {volunteers.reduce((total, v) => total + v._count.volunteerHours, 0)}
                  </p>
                  <p className="text-sm text-gray-600">Total Hours Logged</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search volunteers by name, email, skills..."
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <Select value={selectedStatus} onValueChange={handleStatusFilter}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value={VolunteerStatus.ACTIVE}>Active</SelectItem>
                  <SelectItem value={VolunteerStatus.INACTIVE}>Inactive</SelectItem>
                  <SelectItem value={VolunteerStatus.TRAINING}>Training</SelectItem>
                  <SelectItem value={VolunteerStatus.ON_HOLD}>On Hold</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Volunteers List */}
        <Card>
          <CardHeader>
            <CardTitle>Volunteers</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-20 bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>
            ) : volunteers.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No volunteers found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {volunteers.map((volunteer) => (
                  <div
                    key={volunteer.id}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-blue-700">
                          {volunteer.user.name?.charAt(0).toUpperCase() || volunteer.user.email.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="font-medium text-gray-900">
                            {volunteer.user.name || `${volunteer.user.firstName || ''} ${volunteer.user.lastName || ''}`.trim() || 'No name'}
                          </p>
                          {getStatusIcon(volunteer.status)}
                          <Badge className={getStatusBadgeColor(volunteer.status)}>
                            {volunteer.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-500">{volunteer.user.email}</p>
                        <div className="flex items-center space-x-4 text-xs text-gray-400 mt-1">
                          <span>{volunteer._count.volunteerHours} hours logged</span>
                          <span>{volunteer._count.assignments} assignments</span>
                          {volunteer.orientationCompleted && (
                            <span className="text-green-600">✓ Orientation Complete</span>
                          )}
                        </div>
                        {volunteer.preferredActivities.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {volunteer.preferredActivities.slice(0, 3).map((activity, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {activity}
                              </Badge>
                            ))}
                            {volunteer.preferredActivities.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{volunteer.preferredActivities.length - 3} more
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/volunteers/${volunteer.id}`}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/volunteers/${volunteer.id}/edit`}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Profile
                          </Link>
                        </DropdownMenuItem>
                        {volunteer.status === VolunteerStatus.TRAINING && (
                          <DropdownMenuItem
                            onClick={() => handleUpdateStatus(volunteer.id, VolunteerStatus.ACTIVE)}
                          >
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Activate
                          </DropdownMenuItem>
                        )}
                        {volunteer.status === VolunteerStatus.ACTIVE && (
                          <DropdownMenuItem
                            onClick={() => handleUpdateStatus(volunteer.id, VolunteerStatus.ON_HOLD)}
                          >
                            <Clock className="h-4 w-4 mr-2" />
                            Put On Hold
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {pagination && pagination.totalPages > 1 && (
              <div className="flex items-center justify-between mt-6">
                <p className="text-sm text-gray-500">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                  {pagination.total} volunteers
                </p>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(pagination.page - 1)}
                    disabled={!pagination.hasPrev}
                  >
                    Previous
                  </Button>
                  <span className="text-sm text-gray-500">
                    Page {pagination.page} of {pagination.totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(pagination.page + 1)}
                    disabled={!pagination.hasNext}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
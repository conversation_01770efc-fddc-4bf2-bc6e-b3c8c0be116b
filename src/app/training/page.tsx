"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Search, 
  BookOpen, 
  Award, 
  Clock, 
  Users, 
  Heart,
  Play,
  Target,
  Zap,
  Shield,
  Star,
  ChevronRight,
  Dog,
  Cat,
  Rabbit
} from "lucide-react"
import Link from "next/link"

interface TrainingTip {
  id: string
  title: string
  description: string
  category: string
  difficulty: "Beginner" | "Intermediate" | "Advanced"
  duration: string
  petType: string[]
  tags: string[]
  content: {
    overview: string
    steps: string[]
    tips: string[]
    commonMistakes: string[]
  }
}

const trainingTips: TrainingTip[] = [
  {
    id: "basic-sit-command",
    title: "Teaching the Basic 'Sit' Command",
    description: "Master the fundamental sit command that forms the foundation of all dog training.",
    category: "Basic Commands",
    difficulty: "Beginner",
    duration: "5-10 minutes",
    petType: ["Dog"],
    tags: ["obedience", "basic", "commands", "puppies"],
    content: {
      overview: "The 'sit' command is one of the most important basic commands every dog should learn. It's the foundation for many other commands and helps establish you as the pack leader.",
      steps: [
        "Hold a treat close to your dog's nose",
        "Slowly lift the treat over their head - their bottom should naturally touch the ground",
        "As soon as they sit, say 'Sit!' clearly and give them the treat",
        "Repeat this process 5-10 times per training session",
        "Practice 2-3 times daily for best results",
        "Gradually phase out treats and use praise instead"
      ],
      tips: [
        "Keep training sessions short (5-10 minutes) to maintain attention",
        "Always end on a positive note",
        "Be consistent with your command word",
        "Practice in different locations once mastered at home",
        "Use high-value treats your dog loves"
      ],
      commonMistakes: [
        "Using the command word before the dog performs the action",
        "Training sessions that are too long",
        "Inconsistent commands or hand signals",
        "Not rewarding immediately when the behavior occurs",
        "Getting frustrated - dogs pick up on your emotions"
      ]
    }
  },
  {
    id: "litter-box-training",
    title: "Litter Box Training for Cats",
    description: "Step-by-step guide to successfully litter train your cat or kitten.",
    category: "House Training",
    difficulty: "Beginner",
    duration: "1-2 weeks",
    petType: ["Cat"],
    tags: ["house training", "litter box", "kittens", "hygiene"],
    content: {
      overview: "Most cats naturally prefer to eliminate in sand-like substrates, making litter box training relatively straightforward when done correctly.",
      steps: [
        "Choose the right litter box size (1.5x your cat's length)",
        "Place the box in a quiet, accessible location",
        "Fill with 2-3 inches of unscented, clumping litter",
        "Show your cat the box location",
        "Place your cat in the box after meals and naps",
        "Clean the box daily and completely change litter weekly",
        "Reward your cat with praise when they use the box correctly"
      ],
      tips: [
        "Have one litter box per cat, plus one extra",
        "Avoid scented litters initially",
        "Keep the box away from food and water",
        "Never punish accidents - clean thoroughly with enzyme cleaner",
        "Gradually transition litter types if needed"
      ],
      commonMistakes: [
        "Using a box that's too small",
        "Not cleaning the box frequently enough",
        "Placing the box in a high-traffic area",
        "Changing litter types too quickly",
        "Punishing the cat for accidents"
      ]
    }
  },
  {
    id: "leash-training",
    title: "Leash Training Your Dog",
    description: "Learn how to train your dog to walk calmly on a leash without pulling.",
    category: "Walking & Exercise",
    difficulty: "Intermediate",
    duration: "2-4 weeks",
    petType: ["Dog"],
    tags: ["leash", "walking", "exercise", "pulling"],
    content: {
      overview: "Proper leash training makes walks enjoyable for both you and your dog while ensuring safety and control in various situations.",
      steps: [
        "Start indoors with the collar and leash to get your dog comfortable",
        "Use positive reinforcement when your dog walks beside you",
        "Stop moving forward when your dog pulls",
        "Only continue walking when the leash is loose",
        "Practice the 'heel' command with treats",
        "Gradually increase walk duration and add distractions",
        "Be consistent with your expectations every time"
      ],
      tips: [
        "Use a properly fitted collar or harness",
        "Start with short 5-10 minute sessions",
        "Bring high-value treats for motivation",
        "Practice in low-distraction environments first",
        "Stay patient and consistent",
        "Consider a front-clip harness for strong pullers"
      ],
      commonMistakes: [
        "Allowing pulling sometimes but not others",
        "Using retractable leashes during training",
        "Yanking or jerking the leash",
        "Training sessions that are too long",
        "Not bringing enough rewards"
      ]
    }
  },
  {
    id: "crate-training",
    title: "Crate Training for Dogs",
    description: "Create a safe, comfortable space for your dog with proper crate training techniques.",
    category: "House Training",
    difficulty: "Beginner",
    duration: "1-3 weeks",
    petType: ["Dog"],
    tags: ["crate", "house training", "safety", "comfort"],
    content: {
      overview: "Crate training provides your dog with a safe den-like space and is invaluable for house training, travel, and preventing destructive behavior.",
      steps: [
        "Choose the right size crate (dog should be able to stand and turn around)",
        "Make the crate comfortable with bedding and toys",
        "Feed meals in the crate with the door open",
        "Gradually increase time spent in crate with door closed",
        "Use a command like 'crate' or 'kennel' when directing them in",
        "Never use the crate as punishment",
        "Gradually extend crate time as your dog becomes comfortable"
      ],
      tips: [
        "Start slowly - don't rush the process",
        "Make the crate a positive space with treats and toys",
        "Cover the crate with a blanket for a den-like feel",
        "Place the crate in a family area, not isolated",
        "Ignore whining and only let them out when quiet"
      ],
      commonMistakes: [
        "Using the crate as punishment",
        "Crate is too large (dogs won't soil their sleeping area)",
        "Letting the dog out when they're whining",
        "Forcing the dog into the crate",
        "Leaving the dog crated for too long"
      ]
    }
  },
  {
    id: "recall-training",
    title: "Teaching Reliable Recall (Come Command)",
    description: "Train your dog to come when called, even with distractions present.",
    category: "Basic Commands",
    difficulty: "Advanced",
    duration: "4-8 weeks",
    petType: ["Dog"],
    tags: ["recall", "come", "safety", "off-leash"],
    content: {
      overview: "A reliable recall command can be life-saving and allows your dog more freedom. This is one of the most important commands to master.",
      steps: [
        "Start in a small, enclosed area with minimal distractions",
        "Use an excited, happy voice when calling your dog",
        "Reward generously every time they come to you",
        "Practice the 'come' command during play and positive moments",
        "Gradually increase distance and add mild distractions",
        "Never call your dog to come for something they perceive as negative",
        "Practice in various locations and situations"
      ],
      tips: [
        "Always make coming to you the best thing that happens",
        "Use high-value rewards like special treats or play",
        "Practice recall during walks on a long training leash",
        "Never chase your dog if they don't come",
        "Make yourself interesting - run away, clap, or make noise"
      ],
      commonMistakes: [
        "Calling the dog to come for bath time or other 'negative' activities",
        "Repeating the command multiple times",
        "Chasing the dog when they don't come",
        "Not rewarding enthusiastically enough",
        "Practicing only when you need the dog to come inside"
      ]
    }
  },
  {
    id: "socialization-tips",
    title: "Proper Pet Socialization",
    description: "Essential socialization techniques for puppies, kittens, and adult pets.",
    category: "Behavior & Socialization",
    difficulty: "Intermediate",
    duration: "Ongoing",
    petType: ["Dog", "Cat"],
    tags: ["socialization", "behavior", "puppies", "kittens"],
    content: {
      overview: "Proper socialization helps pets become well-adjusted, confident, and friendly. The critical period is 3-14 weeks for puppies and 2-7 weeks for kittens.",
      steps: [
        "Start socialization as early as possible (after initial vaccinations)",
        "Expose your pet to various people, animals, sounds, and environments",
        "Keep experiences positive and rewarding",
        "Allow your pet to approach new things at their own pace",
        "Attend puppy/kitten socialization classes",
        "Continue socialization throughout your pet's life",
        "Monitor your pet's body language and stress signals"
      ],
      tips: [
        "Quality over quantity - a few positive experiences are better than many stressful ones",
        "Bring treats to make new experiences positive",
        "Don't force interactions - let your pet choose",
        "Socialize with calm, well-behaved animals first",
        "Expose to different surfaces, sounds, and environments"
      ],
      commonMistakes: [
        "Waiting until after all vaccinations (missing critical period)",
        "Forcing interactions when the pet is fearful",
        "Only socializing with other pets, not people",
        "Stopping socialization after the puppy/kitten stage",
        "Not recognizing stress signals"
      ]
    }
  }
]

const categories = [
  "All Categories",
  "Basic Commands", 
  "House Training", 
  "Walking & Exercise", 
  "Behavior & Socialization"
]

const difficulties = ["All Levels", "Beginner", "Intermediate", "Advanced"]
const petTypes = ["All Pets", "Dog", "Cat", "Rabbit"]

export default function TrainingTipsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All Categories")
  const [selectedDifficulty, setSelectedDifficulty] = useState("All Levels")
  const [selectedPetType, setSelectedPetType] = useState("All Pets")
  const [expandedTip, setExpandedTip] = useState<string | null>(null)

  const filteredTips = trainingTips.filter(tip => {
    const matchesSearch = tip.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tip.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tip.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesCategory = selectedCategory === "All Categories" || tip.category === selectedCategory
    const matchesDifficulty = selectedDifficulty === "All Levels" || tip.difficulty === selectedDifficulty
    const matchesPetType = selectedPetType === "All Pets" || tip.petType.includes(selectedPetType)

    return matchesSearch && matchesCategory && matchesDifficulty && matchesPetType
  })

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Beginner": return "bg-green-100 text-green-800"
      case "Intermediate": return "bg-yellow-100 text-yellow-800"
      case "Advanced": return "bg-red-100 text-red-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const getPetTypeIcon = (petType: string) => {
    switch (petType) {
      case "Dog": return <Dog className="h-4 w-4" />
      case "Cat": return <Cat className="h-4 w-4" />
      case "Rabbit": return <Rabbit className="h-4 w-4" />
      default: return <Heart className="h-4 w-4" />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-purple-100 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-blue-100 rounded-full">
                <BookOpen className="h-12 w-12 text-blue-600" />
              </div>
            </div>
            <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Pet Training Tips & Guides
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Expert training advice to help you build a strong, positive relationship with your pet. 
              From basic commands to advanced techniques, we've got you covered.
            </p>
            
            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">{trainingTips.length}</div>
                <div className="text-sm text-gray-600">Training Guides</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">3</div>
                <div className="text-sm text-gray-600">Difficulty Levels</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">5</div>
                <div className="text-sm text-gray-600">Categories</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-600">100%</div>
                <div className="text-sm text-gray-600">Expert Approved</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-12">
        {/* Search and Filters */}
        <div className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Search className="h-5 w-5 mr-2" />
                Find Training Tips
              </CardTitle>
              <CardDescription>
                Search and filter training guides to find exactly what you need
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Search Bar */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    type="text"
                    placeholder="Search training tips..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* Filters */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Category</label>
                    <select
                      value={selectedCategory}
                      onChange={(e) => setSelectedCategory(e.target.value)}
                      className="w-full p-2 border rounded-md"
                    >
                      {categories.map((category) => (
                        <option key={category} value={category}>
                          {category}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">Difficulty</label>
                    <select
                      value={selectedDifficulty}
                      onChange={(e) => setSelectedDifficulty(e.target.value)}
                      className="w-full p-2 border rounded-md"
                    >
                      {difficulties.map((difficulty) => (
                        <option key={difficulty} value={difficulty}>
                          {difficulty}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">Pet Type</label>
                    <select
                      value={selectedPetType}
                      onChange={(e) => setSelectedPetType(e.target.value)}
                      className="w-full p-2 border rounded-md"
                    >
                      {petTypes.map((petType) => (
                        <option key={petType} value={petType}>
                          {petType}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-gray-600">
            Showing {filteredTips.length} training tip{filteredTips.length !== 1 ? 's' : ''}
          </p>
        </div>

        {/* Training Tips Grid */}
        <div className="space-y-6">
          {filteredTips.map((tip) => (
            <Card key={tip.id} className="overflow-hidden">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className="text-xs">
                        {tip.category}
                      </Badge>
                      <Badge className={getDifficultyColor(tip.difficulty)}>
                        {tip.difficulty}
                      </Badge>
                      <div className="flex items-center text-sm text-gray-500">
                        <Clock className="h-3 w-3 mr-1" />
                        {tip.duration}
                      </div>
                    </div>
                    <CardTitle className="text-xl mb-2">{tip.title}</CardTitle>
                    <CardDescription className="text-base">
                      {tip.description}
                    </CardDescription>
                  </div>
                </div>

                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      {tip.petType.map((type, index) => (
                        <div key={index} className="flex items-center text-sm text-gray-600">
                          {getPetTypeIcon(type)}
                          <span className="ml-1">{type}</span>
                          {index < tip.petType.length - 1 && <span className="mx-1">•</span>}
                        </div>
                      ))}
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {tip.tags.slice(0, 3).map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {tip.tags.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{tip.tags.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <Button
                    variant="outline"
                    onClick={() => setExpandedTip(expandedTip === tip.id ? null : tip.id)}
                  >
                    {expandedTip === tip.id ? "Hide Details" : "View Guide"}
                    <ChevronRight className={`h-4 w-4 ml-2 transition-transform ${
                      expandedTip === tip.id ? "rotate-90" : ""
                    }`} />
                  </Button>
                </div>
              </CardHeader>

              {/* Expanded Content */}
              {expandedTip === tip.id && (
                <CardContent className="border-t bg-gray-50">
                  <div className="space-y-6">
                    {/* Overview */}
                    <div>
                      <h4 className="font-semibold text-lg mb-2 flex items-center">
                        <Target className="h-5 w-5 mr-2 text-blue-600" />
                        Overview
                      </h4>
                      <p className="text-gray-700">{tip.content.overview}</p>
                    </div>

                    {/* Steps */}
                    <div>
                      <h4 className="font-semibold text-lg mb-3 flex items-center">
                        <Play className="h-5 w-5 mr-2 text-green-600" />
                        Step-by-Step Instructions
                      </h4>
                      <ol className="space-y-2">
                        {tip.content.steps.map((step, index) => (
                          <li key={index} className="flex items-start">
                            <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                              {index + 1}
                            </span>
                            <span className="text-gray-700">{step}</span>
                          </li>
                        ))}
                      </ol>
                    </div>

                    {/* Tips */}
                    <div>
                      <h4 className="font-semibold text-lg mb-3 flex items-center">
                        <Zap className="h-5 w-5 mr-2 text-yellow-600" />
                        Pro Tips
                      </h4>
                      <ul className="space-y-2">
                        {tip.content.tips.map((tipItem, index) => (
                          <li key={index} className="flex items-start">
                            <Star className="h-4 w-4 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700">{tipItem}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Common Mistakes */}
                    <div>
                      <h4 className="font-semibold text-lg mb-3 flex items-center">
                        <Shield className="h-5 w-5 mr-2 text-red-600" />
                        Common Mistakes to Avoid
                      </h4>
                      <ul className="space-y-2">
                        {tip.content.commonMistakes.map((mistake, index) => (
                          <li key={index} className="flex items-start">
                            <div className="w-2 h-2 bg-red-500 rounded-full mr-3 mt-2 flex-shrink-0"></div>
                            <span className="text-gray-700">{mistake}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>

        {/* No Results */}
        {filteredTips.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🎯</div>
            <h3 className="text-2xl font-semibold text-gray-900 mb-2">No training tips found</h3>
            <p className="text-gray-600 mb-4">
              Try adjusting your search criteria or browse all available guides.
            </p>
            <Button 
              onClick={() => {
                setSearchTerm("")
                setSelectedCategory("All Categories")
                setSelectedDifficulty("All Levels")
                setSelectedPetType("All Pets")
              }}
            >
              Clear Filters
            </Button>
          </div>
        )}

        {/* Additional Resources */}
        <div className="mt-16">
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
            <CardHeader>
              <CardTitle className="text-center text-2xl">Need More Help?</CardTitle>
              <CardDescription className="text-center text-lg">
                Explore our other resources for comprehensive pet care information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="p-4 bg-blue-100 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <Heart className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="font-semibold mb-2">Health Information</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Learn about pet health, nutrition, and wellness
                  </p>
                  <Link href="/health">
                    <Button variant="outline" size="sm">
                      View Health Guides
                    </Button>
                  </Link>
                </div>

                <div className="text-center">
                  <div className="p-4 bg-green-100 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <BookOpen className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="font-semibold mb-2">Pet Care Guides</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Comprehensive guides for new pet owners
                  </p>
                  <Link href="/pet-care">
                    <Button variant="outline" size="sm">
                      Browse Guides
                    </Button>
                  </Link>
                </div>

                <div className="text-center">
                  <div className="p-4 bg-purple-100 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <Users className="h-8 w-8 text-purple-600" />
                  </div>
                  <h3 className="font-semibold mb-2">Expert Support</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Get personalized advice from our experts
                  </p>
                  <Link href="/contact">
                    <Button variant="outline" size="sm">
                      Contact Us
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
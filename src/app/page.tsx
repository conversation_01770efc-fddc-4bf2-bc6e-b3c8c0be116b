import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Heart, Users, Home as HomeIcon, Award, ArrowRight, Dog, Cat, <PERSON>, <PERSON>, Quote } from "lucide-react";

export default function HomePage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 to-indigo-100 py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  Find Your Perfect
                  <span className="text-blue-600 block">Companion</span>
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed">
                  Connect with loving pets in need of homes. Every adoption saves a life and makes room for another rescue.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/pets">
                  <Button size="lg" className="w-full sm:w-auto">
                    Find Pets
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
                <Link href="/foster">
                  <Button variant="outline" size="lg" className="w-full sm:w-auto">
                    Foster a Pet
                  </Button>
                </Link>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-3 gap-6 pt-8">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">2,500+</div>
                  <div className="text-sm text-gray-600">Pets Adopted</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">150+</div>
                  <div className="text-sm text-gray-600">Available Now</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600">500+</div>
                  <div className="text-sm text-gray-600">Happy Families</div>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-blue-200 to-purple-200 rounded-3xl flex items-center justify-center">
                <div className="text-8xl">🐕</div>
              </div>
              {/* Floating cards */}
              <div className="absolute -top-4 -left-4 bg-white rounded-lg shadow-lg p-4 transform rotate-3">
                <Heart className="h-6 w-6 text-red-500 mb-2" />
                <div className="text-sm font-medium">Loved by families</div>
              </div>
              <div className="absolute -bottom-4 -right-4 bg-white rounded-lg shadow-lg p-4 transform -rotate-3">
                <Award className="h-6 w-6 text-yellow-500 mb-2" />
                <div className="text-sm font-medium">Award winning care</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pet Categories */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Find Your New Best Friend
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Browse our available pets by category and find the perfect match for your family.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Link href="/pets?species=dog" className="group">
              <Card className="h-full transition-all duration-300 group-hover:shadow-lg group-hover:-translate-y-1">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 p-4 bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center">
                    <Dog className="h-8 w-8 text-blue-600" />
                  </div>
                  <CardTitle className="text-2xl">Dogs</CardTitle>
                  <CardDescription>
                    Loyal companions ready for walks, play, and endless love.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-2">85</div>
                    <div className="text-sm text-gray-600">Available for adoption</div>
                  </div>
                </CardContent>
              </Card>
            </Link>

            <Link href="/pets?species=cat" className="group">
              <Card className="h-full transition-all duration-300 group-hover:shadow-lg group-hover:-translate-y-1">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 p-4 bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center">
                    <Cat className="h-8 w-8 text-purple-600" />
                  </div>
                  <CardTitle className="text-2xl">Cats</CardTitle>
                  <CardDescription>
                    Independent and affectionate feline friends seeking homes.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600 mb-2">62</div>
                    <div className="text-sm text-gray-600">Available for adoption</div>
                  </div>
                </CardContent>
              </Card>
            </Link>

            <Link href="/pets?species=other" className="group">
              <Card className="h-full transition-all duration-300 group-hover:shadow-lg group-hover:-translate-y-1">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 p-4 bg-green-100 rounded-full w-16 h-16 flex items-center justify-center">
                    <Rabbit className="h-8 w-8 text-green-600" />
                  </div>
                  <CardTitle className="text-2xl">Other Pets</CardTitle>
                  <CardDescription>
                    Rabbits, birds, and other wonderful animals looking for love.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">18</div>
                    <div className="text-sm text-gray-600">Available for adoption</div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              How Adoption Works
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Our simple process makes it easy to find and adopt your new companion.
            </p>
          </div>

          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="mx-auto mb-4 p-4 bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center">
                <span className="text-2xl font-bold text-blue-600">1</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Browse Pets</h3>
              <p className="text-gray-600">
                Search our database of available pets and find ones that match your lifestyle.
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto mb-4 p-4 bg-green-100 rounded-full w-16 h-16 flex items-center justify-center">
                <span className="text-2xl font-bold text-green-600">2</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Apply Online</h3>
              <p className="text-gray-600">
                Fill out our comprehensive adoption application with your information and preferences.
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto mb-4 p-4 bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center">
                <span className="text-2xl font-bold text-purple-600">3</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Meet & Greet</h3>
              <p className="text-gray-600">
                Schedule a visit to meet your potential new family member in person.
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto mb-4 p-4 bg-red-100 rounded-full w-16 h-16 flex items-center justify-center">
                <span className="text-2xl font-bold text-red-600">4</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Take Home</h3>
              <p className="text-gray-600">
                Complete the adoption process and welcome your new companion home!
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Success Stories */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Success Stories
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Read about the amazing transformations and happy endings that make our work worthwhile.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <Card className="overflow-hidden">
              <div className="aspect-video bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
                <span className="text-4xl">🐕</span>
              </div>
              <CardHeader>
                <CardTitle className="text-lg">Max's Journey to Therapy Dog</CardTitle>
                <CardDescription>
                  From scared shelter dog to certified therapy animal helping children with autism.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Max arrived at our shelter traumatized and fearful. Today, he works at a local autism center,
                  providing comfort and helping children develop social skills.
                </p>
                <Link href="/blog/3">
                  <Button variant="outline" size="sm" className="w-full">
                    Read Max's Story
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <div className="aspect-video bg-gradient-to-br from-purple-100 to-purple-200 flex items-center justify-center">
                <span className="text-4xl">🐱</span>
              </div>
              <CardHeader>
                <CardTitle className="text-lg">Luna's Second Chance</CardTitle>
                <CardDescription>
                  A senior cat finds her perfect retirement home with a loving elderly couple.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  At 12 years old, Luna was overlooked by many adopters. But the perfect family saw past her age
                  and gave her the loving home she deserved.
                </p>
                <Link href="/blog">
                  <Button variant="outline" size="sm" className="w-full">
                    Read Luna's Story
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <div className="aspect-video bg-gradient-to-br from-green-100 to-green-200 flex items-center justify-center">
                <span className="text-4xl">🐰</span>
              </div>
              <CardHeader>
                <CardTitle className="text-lg">Buddy's Big Family</CardTitle>
                <CardDescription>
                  A bonded pair of rabbits finds their forever home with a family of five.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Buddy and his companion Clover needed to be adopted together. The Johnson family
                  opened their hearts and home to both bunnies.
                </p>
                <Link href="/blog">
                  <Button variant="outline" size="sm" className="w-full">
                    Read Buddy's Story
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>

          <div className="text-center">
            <Link href="/blog?category=Success Stories">
              <Button variant="outline" size="lg">
                View All Success Stories
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              What Our Adopters Say
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Hear from families who have found their perfect companions through our adoption program.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="relative">
              <CardContent className="pt-6">
                <Quote className="h-8 w-8 text-blue-500 mb-4" />
                <p className="text-gray-700 mb-4">
                  "Adopting Bella was the best decision we ever made. She's brought so much joy and laughter
                  to our home. The adoption process was smooth and the staff was incredibly helpful."
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-blue-600 font-semibold">SM</span>
                  </div>
                  <div>
                    <div className="font-semibold">Sarah Martinez</div>
                    <div className="text-sm text-gray-600">Adopted Bella (Golden Retriever)</div>
                  </div>
                </div>
                <div className="flex mt-3">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="relative">
              <CardContent className="pt-6">
                <Quote className="h-8 w-8 text-purple-500 mb-4" />
                <p className="text-gray-700 mb-4">
                  "The team helped us find the perfect cat for our family. Whiskers has been amazing with
                  our kids and fits right into our routine. Couldn't be happier!"
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-purple-600 font-semibold">MJ</span>
                  </div>
                  <div>
                    <div className="font-semibold">Michael Johnson</div>
                    <div className="text-sm text-gray-600">Adopted Whiskers (Maine Coon)</div>
                  </div>
                </div>
                <div className="flex mt-3">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="relative">
              <CardContent className="pt-6">
                <Quote className="h-8 w-8 text-green-500 mb-4" />
                <p className="text-gray-700 mb-4">
                  "As a first-time pet owner, I was nervous about adoption. The staff provided excellent
                  guidance and support. Charlie and I are inseparable now!"
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-green-600 font-semibold">EL</span>
                  </div>
                  <div>
                    <div className="font-semibold">Emily Lee</div>
                    <div className="text-sm text-gray-600">Adopted Charlie (Beagle Mix)</div>
                  </div>
                </div>
                <div className="flex mt-3">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="relative">
              <CardContent className="pt-6">
                <Quote className="h-8 w-8 text-red-500 mb-4" />
                <p className="text-gray-700 mb-4">
                  "We adopted two senior cats and they've brought such peace to our home. The adoption
                  team made sure we were prepared for their special needs."
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-red-600 font-semibold">DT</span>
                  </div>
                  <div>
                    <div className="font-semibold">David Thompson</div>
                    <div className="text-sm text-gray-600">Adopted Milo & Ginger (Senior Cats)</div>
                  </div>
                </div>
                <div className="flex mt-3">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="relative">
              <CardContent className="pt-6">
                <Quote className="h-8 w-8 text-indigo-500 mb-4" />
                <p className="text-gray-700 mb-4">
                  "The foster-to-adopt program was perfect for us. We got to know Ruby before committing,
                  and now she's officially part of our family!"
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-indigo-600 font-semibold">LW</span>
                  </div>
                  <div>
                    <div className="font-semibold">Lisa Wang</div>
                    <div className="text-sm text-gray-600">Adopted Ruby (Border Collie)</div>
                  </div>
                </div>
                <div className="flex mt-3">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="relative">
              <CardContent className="pt-6">
                <Quote className="h-8 w-8 text-pink-500 mb-4" />
                <p className="text-gray-700 mb-4">
                  "Adopting from this shelter was an amazing experience. The staff truly cares about
                  matching pets with the right families. Oscar is living his best life!"
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-pink-600 font-semibold">JR</span>
                  </div>
                  <div>
                    <div className="font-semibold">Jennifer Rodriguez</div>
                    <div className="text-sm text-gray-600">Adopted Oscar (German Shepherd)</div>
                  </div>
                </div>
                <div className="flex mt-3">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Ways to Help */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Ways to Help
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Not ready to adopt? There are many ways you can help animals in need.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto mb-4 p-4 bg-yellow-100 rounded-full w-16 h-16 flex items-center justify-center">
                  <HomeIcon className="h-8 w-8 text-yellow-600" />
                </div>
                <CardTitle>Foster</CardTitle>
                <CardDescription>
                  Provide temporary homes for pets in need of extra care and attention.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href="/foster">
                  <Button variant="outline" className="w-full">
                    Learn About Fostering
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto mb-4 p-4 bg-green-100 rounded-full w-16 h-16 flex items-center justify-center">
                  <Users className="h-8 w-8 text-green-600" />
                </div>
                <CardTitle>Volunteer</CardTitle>
                <CardDescription>
                  Help with daily care, events, transport, and other essential activities.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href="/volunteer">
                  <Button variant="outline" className="w-full">
                    Volunteer With Us
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto mb-4 p-4 bg-red-100 rounded-full w-16 h-16 flex items-center justify-center">
                  <Heart className="h-8 w-8 text-red-600" />
                </div>
                <CardTitle>Donate</CardTitle>
                <CardDescription>
                  Support our mission with financial contributions for food, medical care, and shelter.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href="/donate">
                  <Button variant="outline" className="w-full">
                    Make a Donation
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Ready to Change a Life?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Every pet deserves a loving home. Start your adoption journey today and discover the joy of unconditional love.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/pets">
              <Button size="lg" variant="secondary" className="w-full sm:w-auto">
                Browse Available Pets
              </Button>
            </Link>
            <Link href="/auth/signup">
              <Button size="lg" variant="outline" className="w-full sm:w-auto border-white text-white hover:bg-white hover:text-blue-600">
                Create Account
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}

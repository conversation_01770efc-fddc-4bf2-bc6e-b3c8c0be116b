"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Search, 
  Calendar, 
  User, 
  ArrowRight,
  Heart,
  MessageCircle,
  Share2,
  BookOpen,
  Filter,
  Clock
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"

interface BlogPost {
  id: string
  title: string
  excerpt: string
  content: string
  author: string
  publishedAt: string
  category: string
  tags: string[]
  readTime: number
  views: number
  likes: number
  comments: number
  featured: boolean
  imageUrl?: string
}

const BLOG_POSTS: BlogPost[] = [
  {
    id: "1",
    title: "The Complete Guide to Adopting Your First Pet",
    excerpt: "Everything you need to know before bringing your new furry friend home, from preparation to the first few weeks of adjustment.",
    content: "Full article content here...",
    author: "<PERSON><PERSON> <PERSON>",
    publishedAt: "2024-01-15",
    category: "Adoption Tips",
    tags: ["first-time-owners", "preparation", "adoption"],
    readTime: 8,
    views: 2450,
    likes: 89,
    comments: 23,
    featured: true,
    imageUrl: "/images/blog/first-pet-guide.jpg"
  },
  {
    id: "2",
    title: "Understanding Your Cat's Body Language",
    excerpt: "Learn to decode what your feline friend is trying to tell you through their posture, tail movements, and vocalizations.",
    content: "Full article content here...",
    author: "Emily Rodriguez",
    publishedAt: "2024-01-12",
    category: "Pet Care",
    tags: ["cats", "behavior", "communication"],
    readTime: 6,
    views: 1890,
    likes: 67,
    comments: 15,
    featured: false,
    imageUrl: "/images/blog/cat-body-language.jpg"
  },
  {
    id: "3",
    title: "Success Story: Max's Journey from Shelter to Service Dog",
    excerpt: "Follow Max's incredible transformation from a scared shelter dog to a certified therapy animal helping children with autism.",
    content: "Full article content here...",
    author: "Michael Chen",
    publishedAt: "2024-01-10",
    category: "Success Stories",
    tags: ["success-story", "therapy-dog", "training"],
    readTime: 5,
    views: 3200,
    likes: 156,
    comments: 42,
    featured: true,
    imageUrl: "/images/blog/max-success-story.jpg"
  },
  {
    id: "4",
    title: "Essential Supplies for New Dog Owners",
    excerpt: "A comprehensive checklist of everything you'll need to welcome your new canine companion into your home.",
    content: "Full article content here...",
    author: "David Park",
    publishedAt: "2024-01-08",
    category: "Adoption Tips",
    tags: ["dogs", "supplies", "checklist"],
    readTime: 7,
    views: 1650,
    likes: 45,
    comments: 18,
    featured: false,
    imageUrl: "/images/blog/dog-supplies.jpg"
  },
  {
    id: "5",
    title: "The Importance of Spaying and Neutering",
    excerpt: "Understanding the health and behavioral benefits of spaying and neutering your pets, plus addressing common concerns.",
    content: "Full article content here...",
    author: "Dr. Sarah Johnson",
    publishedAt: "2024-01-05",
    category: "Health & Wellness",
    tags: ["health", "spay-neuter", "prevention"],
    readTime: 9,
    views: 2100,
    likes: 78,
    comments: 31,
    featured: false,
    imageUrl: "/images/blog/spay-neuter.jpg"
  },
  {
    id: "6",
    title: "Creating a Pet-Friendly Home Environment",
    excerpt: "Tips for pet-proofing your home and creating a safe, comfortable space for your new family member.",
    content: "Full article content here...",
    author: "Emily Rodriguez",
    publishedAt: "2024-01-03",
    category: "Pet Care",
    tags: ["home-safety", "environment", "preparation"],
    readTime: 6,
    views: 1420,
    likes: 52,
    comments: 12,
    featured: false,
    imageUrl: "/images/blog/pet-friendly-home.jpg"
  },
  {
    id: "7",
    title: "Luna's Golden Years: A Senior Cat Success Story",
    excerpt: "How a 12-year-old cat found her perfect retirement home and proved that age is just a number.",
    content: "Full article content here...",
    author: "Michael Chen",
    publishedAt: "2024-01-20",
    category: "Success Stories",
    tags: ["senior-pets", "cats", "adoption"],
    readTime: 4,
    views: 2800,
    likes: 124,
    comments: 38,
    featured: true,
    imageUrl: "/images/blog/luna-senior-cat.jpg"
  },
  {
    id: "8",
    title: "From Stray to Star: Bella's Transformation",
    excerpt: "A malnourished stray dog becomes a beloved family pet and therapy animal in training.",
    content: "Full article content here...",
    author: "Dr. Sarah Johnson",
    publishedAt: "2024-01-18",
    category: "Success Stories",
    tags: ["rescue", "transformation", "therapy-animals"],
    readTime: 6,
    views: 3500,
    likes: 189,
    comments: 56,
    featured: false,
    imageUrl: "/images/blog/bella-transformation.jpg"
  },
  {
    id: "9",
    title: "The Bonded Pair: Buddy and Clover's Journey",
    excerpt: "Two rabbits who couldn't be separated find their perfect family together.",
    content: "Full article content here...",
    author: "Emily Rodriguez",
    publishedAt: "2024-01-16",
    category: "Success Stories",
    tags: ["bonded-pairs", "rabbits", "family-adoption"],
    readTime: 5,
    views: 1950,
    likes: 87,
    comments: 29,
    featured: false,
    imageUrl: "/images/blog/buddy-clover-rabbits.jpg"
  },
  {
    id: "10",
    title: "Volunteer Spotlight: Making a Difference Every Day",
    excerpt: "Meet our amazing volunteers and learn how they're changing lives, one animal at a time.",
    content: "Full article content here...",
    author: "David Park",
    publishedAt: "2024-01-14",
    category: "Community",
    tags: ["volunteers", "community", "impact"],
    readTime: 7,
    views: 1680,
    likes: 73,
    comments: 22,
    featured: false,
    imageUrl: "/images/blog/volunteer-spotlight.jpg"
  },
  {
    id: "11",
    title: "Charlie's Second Chance: From Fearful to Fearless",
    excerpt: "A timid rescue dog learns to trust again with patience, love, and professional training.",
    content: "Full article content here...",
    author: "Dr. Sarah Johnson",
    publishedAt: "2024-01-11",
    category: "Success Stories",
    tags: ["behavioral-rehabilitation", "trust", "training"],
    readTime: 8,
    views: 2750,
    likes: 142,
    comments: 47,
    featured: true,
    imageUrl: "/images/blog/charlie-fearless.jpg"
  },
  {
    id: "12",
    title: "The Foster Family That Couldn't Say Goodbye",
    excerpt: "When fostering turns into forever: The heartwarming story of Ruby and her foster-turned-adoptive family.",
    content: "Full article content here...",
    author: "Michael Chen",
    publishedAt: "2024-01-09",
    category: "Success Stories",
    tags: ["foster-to-adopt", "family", "love"],
    readTime: 5,
    views: 3100,
    likes: 167,
    comments: 52,
    featured: false,
    imageUrl: "/images/blog/ruby-foster-family.jpg"
  }
]

const CATEGORIES = [
  "All Categories",
  "Adoption Tips",
  "Pet Care",
  "Success Stories",
  "Health & Wellness",
  "Training",
  "Community"
]

export default function BlogPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All Categories")
  const [filteredPosts, setFilteredPosts] = useState(BLOG_POSTS)

  const handleSearch = (term: string) => {
    setSearchTerm(term)
    filterPosts(term, selectedCategory)
  }

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category)
    filterPosts(searchTerm, category)
  }

  const filterPosts = (search: string, category: string) => {
    let filtered = BLOG_POSTS

    if (search) {
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(search.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(search.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(search.toLowerCase()))
      )
    }

    if (category !== "All Categories") {
      filtered = filtered.filter(post => post.category === category)
    }

    setFilteredPosts(filtered)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const featuredPosts = filteredPosts.filter(post => post.featured)
  const regularPosts = filteredPosts.filter(post => !post.featured)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-gradient-to-br from-blue-50 to-purple-100 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Pet Care Blog
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Expert advice, heartwarming stories, and essential tips for pet owners and animal lovers.
            </p>
            
            {/* Search and Filter */}
            <div className="max-w-2xl mx-auto space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search articles..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10 bg-white"
                />
              </div>
              
              <div className="flex flex-wrap gap-2 justify-center">
                {CATEGORIES.map((category) => (
                  <button
                    key={category}
                    onClick={() => handleCategoryChange(category)}
                    className={`px-4 py-2 rounded-full text-sm transition-colors ${
                      selectedCategory === category
                        ? 'bg-blue-600 text-white'
                        : 'bg-white text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-12">
        {/* Featured Posts */}
        {featuredPosts.length > 0 && (
          <section className="mb-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Featured Articles</h2>
            <div className="grid lg:grid-cols-2 gap-8">
              {featuredPosts.map((post) => (
                <Card key={post.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="aspect-video bg-gray-200 flex items-center justify-center">
                    <BookOpen className="h-16 w-16 text-gray-400" />
                  </div>
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <Badge className="bg-blue-100 text-blue-800">{post.category}</Badge>
                      <Badge variant="secondary">Featured</Badge>
                    </div>
                    <CardTitle className="text-xl hover:text-blue-600 transition-colors">
                      <Link href={`/blog/${post.id}`}>
                        {post.title}
                      </Link>
                    </CardTitle>
                    <CardDescription className="text-base">
                      {post.excerpt}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                          <User className="h-3 w-3 mr-1" />
                          {post.author}
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          {formatDate(post.publishedAt)}
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {post.readTime} min read
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <Heart className="h-3 w-3 mr-1" />
                          {post.likes}
                        </div>
                        <div className="flex items-center">
                          <MessageCircle className="h-3 w-3 mr-1" />
                          {post.comments}
                        </div>
                      </div>
                      <Link href={`/blog/${post.id}`}>
                        <Button variant="outline" size="sm">
                          Read More
                          <ArrowRight className="h-3 w-3 ml-1" />
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        )}

        {/* Regular Posts */}
        <section>
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold text-gray-900">
              {selectedCategory === "All Categories" ? "Latest Articles" : selectedCategory}
            </h2>
            <div className="text-sm text-gray-500">
              {filteredPosts.length} article{filteredPosts.length !== 1 ? 's' : ''} found
            </div>
          </div>

          {filteredPosts.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No Articles Found</h3>
                <p className="text-gray-600 mb-4">
                  Try adjusting your search criteria or browse our categories.
                </p>
                <Button onClick={() => {
                  setSearchTerm("")
                  setSelectedCategory("All Categories")
                  setFilteredPosts(BLOG_POSTS)
                }}>
                  View All Articles
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {regularPosts.map((post) => (
                <Card key={post.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="aspect-video bg-gray-200 flex items-center justify-center">
                    <BookOpen className="h-12 w-12 text-gray-400" />
                  </div>
                  <CardHeader>
                    <Badge className="w-fit bg-gray-100 text-gray-800 mb-2">{post.category}</Badge>
                    <CardTitle className="text-lg hover:text-blue-600 transition-colors">
                      <Link href={`/blog/${post.id}`}>
                        {post.title}
                      </Link>
                    </CardTitle>
                    <CardDescription>
                      {post.excerpt}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                      <div className="flex items-center">
                        <User className="h-3 w-3 mr-1" />
                        {post.author}
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {formatDate(post.publishedAt)}
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {post.readTime}m
                      </div>
                    </div>
                    
                    <div className="flex flex-wrap gap-1 mb-4">
                      {post.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 text-xs text-gray-500">
                        <div className="flex items-center">
                          <Heart className="h-3 w-3 mr-1" />
                          {post.likes}
                        </div>
                        <div className="flex items-center">
                          <MessageCircle className="h-3 w-3 mr-1" />
                          {post.comments}
                        </div>
                      </div>
                      <Link href={`/blog/${post.id}`}>
                        <Button variant="outline" size="sm">
                          Read
                          <ArrowRight className="h-3 w-3 ml-1" />
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </section>

        {/* Newsletter Signup */}
        <section className="mt-20">
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
            <CardContent className="p-8 text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Stay Updated with Pet Care Tips
              </h3>
              <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                Subscribe to our newsletter and get the latest articles, tips, and success stories 
                delivered straight to your inbox.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <Input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1"
                />
                <Button className="bg-blue-600 hover:bg-blue-700">
                  Subscribe
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-3">
                No spam, unsubscribe at any time.
              </p>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  )
}

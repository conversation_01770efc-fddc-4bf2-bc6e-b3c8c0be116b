"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { 
  ChevronDown, 
  ChevronUp, 
  Search, 
  HelpCircle,
  Heart,
  Users,
  Home,
  DollarSign,
  FileText,
  Phone
} from "lucide-react"

interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
  tags: string[]
}

const FAQ_CATEGORIES = [
  { id: "all", name: "All Questions", icon: HelpCircle },
  { id: "adoption", name: "Adoption Process", icon: Heart },
  { id: "volunteer", name: "Volunteering", icon: Users },
  { id: "foster", name: "<PERSON> Care", icon: Home },
  { id: "fees", name: "Fees & Donations", icon: DollarSign },
  { id: "policies", name: "Polici<PERSON>", icon: FileText },
  { id: "contact", name: "Contact & Support", icon: Phone },
]

const FAQ_ITEMS: FAQItem[] = [
  {
    id: "1",
    question: "How do I adopt a pet?",
    answer: "To adopt a pet, start by browsing our available animals online. When you find a pet you're interested in, submit an adoption application through our website. Our team will review your application, conduct a phone interview, and if approved, schedule a meet-and-greet. If everything goes well, we'll arrange a home visit and finalize the adoption with contract signing and fee payment.",
    category: "adoption",
    tags: ["process", "application", "steps"]
  },
  {
    id: "2",
    question: "What are the adoption fees?",
    answer: "Adoption fees vary by animal type and age. Dogs typically range from $150-$300, cats from $100-$200, and small animals from $25-$75. All adoption fees include spaying/neutering, current vaccinations, microchipping, and a health check. These fees help cover the cost of care and make room for more rescues.",
    category: "fees",
    tags: ["cost", "pricing", "included"]
  },
  {
    id: "3",
    question: "How long does the adoption process take?",
    answer: "The adoption process typically takes 3-7 days from application submission to taking your pet home. This includes application review (1-2 days), phone interview, meet-and-greet scheduling, and home visit. Emergency situations or special needs animals may require additional time for proper matching.",
    category: "adoption",
    tags: ["timeline", "duration", "process"]
  },
  {
    id: "4",
    question: "Can I volunteer if I'm under 18?",
    answer: "Yes! We welcome young volunteers aged 16-17 with parental consent and supervision. Volunteers under 16 can participate in family volunteer activities. We have age-appropriate tasks including animal socialization, fundraising events, and administrative support. All volunteers receive training and ongoing support.",
    category: "volunteer",
    tags: ["age", "youth", "requirements"]
  },
  {
    id: "5",
    question: "What's included in the adoption fee?",
    answer: "All adoption fees include: spaying/neutering, current vaccinations (DHPP, rabies, etc.), microchipping, deworming, flea/tick prevention, health examination, and a starter kit with food, toys, and care information. For dogs, we also include a collar and leash. For cats, we provide a carrier for transport.",
    category: "fees",
    tags: ["included", "services", "medical"]
  },
  {
    id: "6",
    question: "Do you accept pet surrenders?",
    answer: "We accept surrenders on a case-by-case basis depending on our current capacity and the animal's needs. We require a surrender fee to help cover initial care costs. Before surrendering, we encourage exploring all options including rehoming resources, training support, and temporary fostering. Please call us to discuss your situation.",
    category: "policies",
    tags: ["surrender", "rehoming", "options"]
  },
  {
    id: "7",
    question: "How do I become a foster family?",
    answer: "To become a foster family, complete our foster application online, attend a foster orientation session, and have a home visit. Foster families provide temporary care for animals who need extra attention, are too young for adoption, or are recovering from medical treatment. We provide all supplies, medical care, and 24/7 support.",
    category: "foster",
    tags: ["application", "requirements", "support"]
  },
  {
    id: "8",
    question: "Can I return an adopted pet?",
    answer: "Yes, we have a return policy to ensure the best outcome for both pets and families. If an adoption doesn't work out, contact us immediately. We'll work with you to address any issues and, if necessary, arrange for the pet's return. We ask that you give the adjustment period at least 2 weeks, as pets need time to settle into new homes.",
    category: "policies",
    tags: ["return", "policy", "adjustment"]
  },
  {
    id: "9",
    question: "What volunteer opportunities are available?",
    answer: "We offer diverse volunteer opportunities including: animal care (feeding, cleaning, socialization), dog walking, cat enrichment, administrative support, event assistance, transport help, photography, social media, fundraising, and specialized roles like veterinary assistance or training support. We match volunteers with roles based on interests, skills, and availability.",
    category: "volunteer",
    tags: ["opportunities", "roles", "activities"]
  },
  {
    id: "10",
    question: "How can I donate?",
    answer: "You can donate in several ways: online through our website, by mail with a check, in-person during business hours, through workplace giving programs, or by donating supplies from our wish list. We also accept vehicle donations, planned giving, and memorial donations. All donations are tax-deductible and directly support animal care.",
    category: "fees",
    tags: ["donation", "methods", "tax-deductible"]
  },
  {
    id: "11",
    question: "What if I have allergies but want to adopt?",
    answer: "We can help you find a pet that may be more suitable for people with allergies. Some animals produce fewer allergens than others. We recommend spending time with potential pets before adoption, consulting with your doctor, and considering hypoallergenic breeds. We also provide information about allergy management strategies.",
    category: "adoption",
    tags: ["allergies", "health", "compatibility"]
  },
  {
    id: "12",
    question: "Do you provide training support after adoption?",
    answer: "Yes! We offer post-adoption support including training resources, behavioral consultations, and access to our network of professional trainers. We provide training guides, video resources, and can recommend local training classes. Our goal is to ensure successful, lifelong adoptions through ongoing support.",
    category: "adoption",
    tags: ["training", "support", "post-adoption"]
  }
]

export default function FAQPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const filteredFAQs = FAQ_ITEMS.filter(faq => {
    const matchesSearch = searchTerm === "" || 
      faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faq.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesCategory = selectedCategory === "all" || faq.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  const toggleExpanded = (id: string) => {
    setExpandedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-gradient-to-br from-blue-50 to-purple-100 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6">
              <HelpCircle className="h-8 w-8 text-blue-600" />
            </div>
            <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Frequently Asked Questions
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Find answers to common questions about adoption, volunteering, and our services.
            </p>
            
            {/* Search */}
            <div className="max-w-md mx-auto relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search questions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white"
              />
            </div>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-6xl mx-auto">
          {/* Categories */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Browse by Category</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-7 gap-4">
              {FAQ_CATEGORIES.map((category) => {
                const Icon = category.icon
                return (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`p-4 rounded-lg border transition-all text-center ${
                      selectedCategory === category.id
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 hover:border-gray-300 text-gray-600'
                    }`}
                  >
                    <Icon className="h-6 w-6 mx-auto mb-2" />
                    <div className="text-sm font-medium">{category.name}</div>
                  </button>
                )
              })}
            </div>
          </div>

          {/* Results */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">
                {selectedCategory === "all" ? "All Questions" : 
                 FAQ_CATEGORIES.find(cat => cat.id === selectedCategory)?.name}
              </h2>
              <div className="text-sm text-gray-500">
                {filteredFAQs.length} question{filteredFAQs.length !== 1 ? 's' : ''} found
              </div>
            </div>
          </div>

          {/* FAQ Items */}
          {filteredFAQs.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <HelpCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No Questions Found</h3>
                <p className="text-gray-600 mb-4">
                  Try adjusting your search or browse our categories.
                </p>
                <button
                  onClick={() => {
                    setSearchTerm("")
                    setSelectedCategory("all")
                  }}
                  className="text-blue-600 hover:text-blue-700 font-medium"
                >
                  View All Questions
                </button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredFAQs.map((faq) => {
                const isExpanded = expandedItems.includes(faq.id)
                return (
                  <Card key={faq.id} className="overflow-hidden">
                    <CardHeader 
                      className="cursor-pointer hover:bg-gray-50 transition-colors"
                      onClick={() => toggleExpanded(faq.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg text-left">{faq.question}</CardTitle>
                          <div className="flex items-center space-x-2 mt-2">
                            <Badge variant="secondary" className="text-xs">
                              {FAQ_CATEGORIES.find(cat => cat.id === faq.category)?.name}
                            </Badge>
                            {faq.tags.slice(0, 2).map((tag) => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div className="ml-4">
                          {isExpanded ? (
                            <ChevronUp className="h-5 w-5 text-gray-400" />
                          ) : (
                            <ChevronDown className="h-5 w-5 text-gray-400" />
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    {isExpanded && (
                      <CardContent>
                        <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                      </CardContent>
                    )}
                  </Card>
                )
              })}
            </div>
          )}

          {/* Contact Section */}
          <section className="mt-16">
            <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
              <CardContent className="p-8 text-center">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Still Have Questions?
                </h3>
                <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                  Can't find the answer you're looking for? Our friendly team is here to help. 
                  Contact us and we'll get back to you as soon as possible.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="/contact"
                    className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Contact Us
                  </a>
                  <a
                    href="tel:(555)123-PETS"
                    className="inline-flex items-center justify-center px-6 py-3 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
                  >
                    <Phone className="h-4 w-4 mr-2" />
                    Call (555) 123-PETS
                  </a>
                </div>
              </CardContent>
            </Card>
          </section>
        </div>
      </div>
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  BookOpen, 
  Video, 
  FileText, 
  Download, 
  Search, 
  Filter,
  Heart,
  Home,
  Stethoscope,
  Users,
  GraduationCap,
  Play,
  ExternalLink
} from "lucide-react"
import Link from "next/link"

interface Resource {
  id: string
  title: string
  description: string
  type: string
  category: string
  url?: string
  downloadUrl?: string
  thumbnailUrl?: string
  author: string
  publishedAt: string
  tags: string[]
  difficulty: string
  estimatedReadTime?: number
  views: number
  likes: number
}

const RESOURCE_CATEGORIES = [
  { id: "pet-care", name: "Pet Care", icon: Heart, color: "bg-red-100 text-red-600" },
  { id: "training", name: "Training", icon: GraduationCap, color: "bg-blue-100 text-blue-600" },
  { id: "health", name: "Health & Medical", icon: Stethoscope, color: "bg-green-100 text-green-600" },
  { id: "behavior", name: "Behavior", icon: Users, color: "bg-purple-100 text-purple-600" },
  { id: "adoption", name: "Adoption Process", icon: Home, color: "bg-orange-100 text-orange-600" },
  { id: "emergency", name: "Emergency Care", icon: FileText, color: "bg-red-100 text-red-600" },
]

const SAMPLE_RESOURCES: Resource[] = [
  {
    id: "1",
    title: "First Week with Your New Dog",
    description: "Essential tips for helping your newly adopted dog adjust to their new home during the critical first week.",
    type: "article",
    category: "adoption",
    author: "Dr. Sarah Johnson",
    publishedAt: "2024-01-15",
    tags: ["new-pet", "adjustment", "bonding"],
    difficulty: "beginner",
    estimatedReadTime: 8,
    views: 1250,
    likes: 89,
  },
  {
    id: "2",
    title: "Basic Dog Training Commands",
    description: "Learn the fundamental commands every dog should know: sit, stay, come, down, and heel.",
    type: "video",
    category: "training",
    url: "https://example.com/video/basic-commands",
    thumbnailUrl: "/images/training-video-thumb.jpg",
    author: "Mark Thompson, Certified Dog Trainer",
    publishedAt: "2024-01-10",
    tags: ["commands", "obedience", "basics"],
    difficulty: "beginner",
    views: 2100,
    likes: 156,
  },
  {
    id: "3",
    title: "Cat Health Checklist",
    description: "Comprehensive guide to monitoring your cat's health and recognizing signs of illness.",
    type: "pdf",
    category: "health",
    downloadUrl: "/downloads/cat-health-checklist.pdf",
    author: "Dr. Emily Chen, DVM",
    publishedAt: "2024-01-08",
    tags: ["health", "prevention", "symptoms"],
    difficulty: "intermediate",
    views: 890,
    likes: 67,
  },
  {
    id: "4",
    title: "Understanding Dog Body Language",
    description: "Learn to read your dog's body language to better understand their emotions and needs.",
    type: "article",
    category: "behavior",
    author: "Animal Behaviorist Lisa Rodriguez",
    publishedAt: "2024-01-05",
    tags: ["body-language", "communication", "behavior"],
    difficulty: "intermediate",
    estimatedReadTime: 12,
    views: 1680,
    likes: 124,
  },
  {
    id: "5",
    title: "Emergency First Aid for Pets",
    description: "Critical first aid techniques every pet owner should know for emergency situations.",
    type: "video",
    category: "emergency",
    url: "https://example.com/video/pet-first-aid",
    thumbnailUrl: "/images/first-aid-thumb.jpg",
    author: "Dr. Michael Park, Emergency Veterinarian",
    publishedAt: "2024-01-03",
    tags: ["first-aid", "emergency", "safety"],
    difficulty: "advanced",
    views: 3200,
    likes: 245,
  },
]

export default function ResourcesPage() {
  const [resources, setResources] = useState<Resource[]>(SAMPLE_RESOURCES)
  const [filteredResources, setFilteredResources] = useState<Resource[]>(SAMPLE_RESOURCES)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("")
  const [selectedType, setSelectedType] = useState("")
  const [selectedDifficulty, setSelectedDifficulty] = useState("")

  useEffect(() => {
    let filtered = resources

    if (searchTerm) {
      filtered = filtered.filter(resource =>
        resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        resource.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    if (selectedCategory) {
      filtered = filtered.filter(resource => resource.category === selectedCategory)
    }

    if (selectedType) {
      filtered = filtered.filter(resource => resource.type === selectedType)
    }

    if (selectedDifficulty) {
      filtered = filtered.filter(resource => resource.difficulty === selectedDifficulty)
    }

    setFilteredResources(filtered)
  }, [resources, searchTerm, selectedCategory, selectedType, selectedDifficulty])

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "video":
        return <Video className="h-4 w-4" />
      case "pdf":
        return <FileText className="h-4 w-4" />
      default:
        return <BookOpen className="h-4 w-4" />
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "beginner":
        return "bg-green-100 text-green-800"
      case "intermediate":
        return "bg-yellow-100 text-yellow-800"
      case "advanced":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Educational Resources
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Comprehensive guides, videos, and resources to help you provide the best care for your pets.
        </p>
      </div>

      {/* Categories */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
        {RESOURCE_CATEGORIES.map((category) => {
          const Icon = category.icon
          return (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(selectedCategory === category.id ? "" : category.id)}
              className={`p-4 rounded-lg border transition-all ${
                selectedCategory === category.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className={`inline-flex items-center justify-center w-10 h-10 rounded-lg mb-2 ${category.color}`}>
                <Icon className="h-5 w-5" />
              </div>
              <div className="text-sm font-medium text-center">{category.name}</div>
            </button>
          )
        })}
      </div>

      {/* Search and Filters */}
      <div className="mb-8 space-y-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            type="text"
            placeholder="Search resources..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex flex-wrap gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="p-2 border border-gray-300 rounded-md"
            >
              <option value="">All Types</option>
              <option value="article">Articles</option>
              <option value="video">Videos</option>
              <option value="pdf">PDFs</option>
            </select>
          </div>

          <select
            value={selectedDifficulty}
            onChange={(e) => setSelectedDifficulty(e.target.value)}
            className="p-2 border border-gray-300 rounded-md"
          >
            <option value="">All Levels</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>

          {(selectedCategory || selectedType || selectedDifficulty || searchTerm) && (
            <Button
              variant="outline"
              onClick={() => {
                setSelectedCategory("")
                setSelectedType("")
                setSelectedDifficulty("")
                setSearchTerm("")
              }}
            >
              Clear Filters
            </Button>
          )}
        </div>
      </div>

      {/* Results Count */}
      <div className="mb-6">
        <p className="text-gray-600">
          Showing {filteredResources.length} of {resources.length} resources
        </p>
      </div>

      {/* Resources Grid */}
      {filteredResources.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No Resources Found</h3>
            <p className="text-gray-600 mb-4">
              Try adjusting your search criteria or browse our categories.
            </p>
            <Button onClick={() => {
              setSelectedCategory("")
              setSelectedType("")
              setSelectedDifficulty("")
              setSearchTerm("")
            }}>
              View All Resources
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredResources.map((resource) => (
            <Card key={resource.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              {resource.thumbnailUrl && (
                <div className="relative aspect-video bg-gray-200">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Play className="h-12 w-12 text-white bg-black/50 rounded-full p-3" />
                  </div>
                </div>
              )}

              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-2">
                    {getTypeIcon(resource.type)}
                    <Badge variant="outline" className="text-xs">
                      {resource.type}
                    </Badge>
                  </div>
                  <Badge className={getDifficultyColor(resource.difficulty)}>
                    {resource.difficulty}
                  </Badge>
                </div>
                <CardTitle className="text-lg">{resource.title}</CardTitle>
                <CardDescription>{resource.description}</CardDescription>
              </CardHeader>

              <CardContent>
                <div className="space-y-3">
                  <div className="flex flex-wrap gap-1">
                    {resource.tags.slice(0, 3).map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <div className="text-sm text-gray-600">
                    <div>By {resource.author}</div>
                    <div>{formatDate(resource.publishedAt)}</div>
                    {resource.estimatedReadTime && (
                      <div>{resource.estimatedReadTime} min read</div>
                    )}
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>{resource.views.toLocaleString()} views</span>
                    <span>{resource.likes} likes</span>
                  </div>

                  <div className="flex gap-2 pt-2">
                    {resource.url && (
                      <Button size="sm" className="flex-1">
                        <ExternalLink className="h-3 w-3 mr-1" />
                        View
                      </Button>
                    )}
                    {resource.downloadUrl && (
                      <Button variant="outline" size="sm" className="flex-1">
                        <Download className="h-3 w-3 mr-1" />
                        Download
                      </Button>
                    )}
                    {!resource.url && !resource.downloadUrl && (
                      <Button size="sm" className="flex-1">
                        Read Article
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Call to Action */}
      <div className="mt-16 text-center">
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Need More Help?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Can't find what you're looking for? Our team of experts is here to help with personalized advice and support.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact">
                <Button size="lg">Contact Our Experts</Button>
              </Link>
              <Link href="/volunteer">
                <Button variant="outline" size="lg">Join Our Community</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Search, 
  Heart, 
  Shield, 
  Stethoscope, 
  Pill, 
  Calendar, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Phone,
  MapPin,
  ExternalLink,
  Download,
  FileText,
  Activity
} from "lucide-react"
import Link from "next/link"

interface HealthResource {
  id: string
  title: string
  category: string
  description: string
  content: string
  tags: string[]
  lastUpdated: string
  downloadUrl?: string
  externalUrl?: string
}

const healthResources: HealthResource[] = [
  {
    id: "1",
    title: "Vaccination Schedule for Dogs",
    category: "Preventive Care",
    description: "Complete vaccination timeline for puppies and adult dogs",
    content: "Essential vaccines include DHPP, Rabies, and optional vaccines based on lifestyle...",
    tags: ["vaccines", "dogs", "puppies", "preventive"],
    lastUpdated: "2024-01-15",
    downloadUrl: "/resources/dog-vaccination-schedule.pdf"
  },
  {
    id: "2",
    title: "Cat Health Checklist",
    category: "General Health",
    description: "Monthly health monitoring checklist for cat owners",
    content: "Regular health checks help detect issues early. Monitor eating, drinking, litter box habits...",
    tags: ["cats", "health-check", "monitoring"],
    lastUpdated: "2024-01-12",
    downloadUrl: "/resources/cat-health-checklist.pdf"
  },
  {
    id: "3",
    title: "Emergency First Aid for Pets",
    category: "Emergency Care",
    description: "Essential first aid steps every pet owner should know",
    content: "In emergency situations, quick action can save your pet's life. Learn basic first aid...",
    tags: ["emergency", "first-aid", "safety"],
    lastUpdated: "2024-01-10",
    downloadUrl: "/resources/pet-first-aid-guide.pdf"
  },
  {
    id: "4",
    title: "Senior Pet Care Guide",
    category: "Senior Care",
    description: "Special care considerations for aging pets",
    content: "Senior pets need adjusted care including modified diet, exercise, and regular checkups...",
    tags: ["senior-pets", "aging", "care"],
    lastUpdated: "2024-01-08",
    downloadUrl: "/resources/senior-pet-care.pdf"
  },
  {
    id: "5",
    title: "Nutrition Guidelines",
    category: "Nutrition",
    description: "Proper nutrition for different life stages and health conditions",
    content: "Balanced nutrition is crucial for pet health. Learn about proper feeding schedules...",
    tags: ["nutrition", "diet", "feeding"],
    lastUpdated: "2024-01-05",
    downloadUrl: "/resources/pet-nutrition-guide.pdf"
  },
  {
    id: "6",
    title: "Dental Care for Pets",
    category: "Dental Health",
    description: "Maintaining your pet's oral health and preventing dental disease",
    content: "Dental health affects overall health. Learn proper brushing techniques and signs of dental issues...",
    tags: ["dental", "oral-health", "brushing"],
    lastUpdated: "2024-01-03",
    downloadUrl: "/resources/pet-dental-care.pdf"
  }
]

const emergencyContacts = [
  {
    name: "24/7 Pet Emergency Hotline",
    phone: "(555) 911-PETS",
    description: "Immediate emergency assistance and guidance"
  },
  {
    name: "Poison Control for Pets",
    phone: "(*************",
    description: "ASPCA Animal Poison Control Center"
  },
  {
    name: "Local Emergency Vet",
    phone: "(555) 123-EMRG",
    description: "After-hours emergency veterinary services"
  }
]

const categories = ["All", "Preventive Care", "General Health", "Emergency Care", "Senior Care", "Nutrition", "Dental Health"]

export default function HealthPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [expandedResource, setExpandedResource] = useState<string | null>(null)

  const filteredResources = healthResources.filter(resource => {
    const matchesSearch = 
      resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesCategory = selectedCategory === "All" || resource.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Pet Health Information
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive health resources to keep your pets happy and healthy. 
            Find vaccination schedules, care guides, and emergency information.
          </p>
        </div>

        {/* Emergency Contacts */}
        <Card className="mb-8 border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800 flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Emergency Contacts
            </CardTitle>
            <CardDescription className="text-red-700">
              Keep these numbers handy for pet emergencies
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4">
              {emergencyContacts.map((contact, index) => (
                <div key={index} className="bg-white p-4 rounded-lg border border-red-200">
                  <h4 className="font-semibold text-gray-900 mb-1">{contact.name}</h4>
                  <p className="text-lg font-bold text-red-600 mb-2">{contact.phone}</p>
                  <p className="text-sm text-gray-600">{contact.description}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Search and Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search health resources..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2 flex-wrap">
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                  >
                    {category}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Health Resources */}
        <div className="grid gap-6 mb-8">
          {filteredResources.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No Resources Found</h3>
                <p className="text-gray-600">
                  Try adjusting your search terms or category filter.
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredResources.map((resource) => (
              <Card key={resource.id} className="overflow-hidden">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="secondary">{resource.category}</Badge>
                        <span className="text-sm text-gray-500">
                          Updated {formatDate(resource.lastUpdated)}
                        </span>
                      </div>
                      <CardTitle className="text-xl mb-2">{resource.title}</CardTitle>
                      <CardDescription className="text-base">
                        {resource.description}
                      </CardDescription>
                    </div>
                    <div className="flex gap-2 ml-4">
                      {resource.downloadUrl && (
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      )}
                      {resource.externalUrl && (
                        <Button variant="outline" size="sm">
                          <ExternalLink className="h-4 w-4 mr-2" />
                          View
                        </Button>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {resource.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        #{tag}
                      </Badge>
                    ))}
                  </div>
                  
                  {expandedResource === resource.id ? (
                    <div>
                      <p className="text-gray-700 mb-4">{resource.content}</p>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => setExpandedResource(null)}
                      >
                        Show Less
                      </Button>
                    </div>
                  ) : (
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => setExpandedResource(resource.id)}
                    >
                      Read More
                    </Button>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Quick Health Tips */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="text-center">
            <CardContent className="p-6">
              <Heart className="h-8 w-8 text-red-500 mx-auto mb-3" />
              <h3 className="font-semibold mb-2">Regular Checkups</h3>
              <p className="text-sm text-gray-600">
                Annual vet visits help catch health issues early
              </p>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="p-6">
              <Shield className="h-8 w-8 text-blue-500 mx-auto mb-3" />
              <h3 className="font-semibold mb-2">Vaccinations</h3>
              <p className="text-sm text-gray-600">
                Keep vaccines up to date to prevent diseases
              </p>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="p-6">
              <Activity className="h-8 w-8 text-green-500 mx-auto mb-3" />
              <h3 className="font-semibold mb-2">Exercise</h3>
              <p className="text-sm text-gray-600">
                Regular exercise maintains physical and mental health
              </p>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="p-6">
              <Pill className="h-8 w-8 text-purple-500 mx-auto mb-3" />
              <h3 className="font-semibold mb-2">Nutrition</h3>
              <p className="text-sm text-gray-600">
                Proper diet is foundation of good health
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Veterinary Partners */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Stethoscope className="h-5 w-5 mr-2" />
              Veterinary Partners
            </CardTitle>
            <CardDescription>
              Trusted veterinary clinics in our network
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="border rounded-lg p-4">
                <h4 className="font-semibold mb-2">City Animal Hospital</h4>
                <div className="flex items-center text-sm text-gray-600 mb-1">
                  <MapPin className="h-4 w-4 mr-1" />
                  123 Main St, City, State 12345
                </div>
                <div className="flex items-center text-sm text-gray-600 mb-3">
                  <Phone className="h-4 w-4 mr-1" />
                  (555) 123-VETS
                </div>
                <Badge variant="outline" className="text-xs">
                  24/7 Emergency
                </Badge>
              </div>
              
              <div className="border rounded-lg p-4">
                <h4 className="font-semibold mb-2">Compassionate Care Clinic</h4>
                <div className="flex items-center text-sm text-gray-600 mb-1">
                  <MapPin className="h-4 w-4 mr-1" />
                  456 Oak Ave, City, State 12345
                </div>
                <div className="flex items-center text-sm text-gray-600 mb-3">
                  <Phone className="h-4 w-4 mr-1" />
                  (555) 456-CARE
                </div>
                <Badge variant="outline" className="text-xs">
                  Specialty Services
                </Badge>
              </div>
              
              <div className="border rounded-lg p-4">
                <h4 className="font-semibold mb-2">Wellness Pet Center</h4>
                <div className="flex items-center text-sm text-gray-600 mb-1">
                  <MapPin className="h-4 w-4 mr-1" />
                  789 Pine St, City, State 12345
                </div>
                <div className="flex items-center text-sm text-gray-600 mb-3">
                  <Phone className="h-4 w-4 mr-1" />
                  (555) 789-WELL
                </div>
                <Badge variant="outline" className="text-xs">
                  Preventive Care
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

"use client"

import Link from "next/link"
import { useSession, signOut } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { UserX, ArrowLeft, Mail, LogOut } from "lucide-react"

export default function AccountInactive() {
  const { data: session } = useSession()

  const handleSignOut = () => {
    signOut({ callbackUrl: "/" })
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 h-16 w-16 bg-orange-100 rounded-full flex items-center justify-center">
            <UserX className="h-8 w-8 text-orange-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">
            Account Inactive
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="text-center space-y-2">
            <p className="text-gray-600">
              Your account is currently inactive and cannot access the system.
            </p>
            {session?.user && (
              <div className="text-sm text-gray-500 space-y-1">
                <p>Account: {session.user.email}</p>
                <p>Status: <span className="font-medium text-orange-600">
                  {session.user.status || 'Inactive'}
                </span></p>
              </div>
            )}
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-medium text-blue-900 mb-2">What can you do?</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Contact our support team for assistance</li>
              <li>• Check if your email needs verification</li>
              <li>• Wait for admin approval if recently registered</li>
            </ul>
          </div>

          <div className="space-y-3">
            <Link href="/contact" className="block">
              <Button className="w-full">
                <Mail className="h-4 w-4 mr-2" />
                Contact Support
              </Button>
            </Link>
            
            <Button 
              variant="outline" 
              className="w-full"
              onClick={handleSignOut}
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </Button>
            
            <Link href="/" className="block">
              <Button variant="ghost" className="w-full">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Home
              </Button>
            </Link>
          </div>

          <div className="pt-4 border-t border-gray-200">
            <p className="text-xs text-gray-500 text-center">
              If you believe this is an error, please contact our support team
              and reference your account email address.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
